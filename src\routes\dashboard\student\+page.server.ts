import { eq, desc } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { PageServerLoad } from './$types';
import { requireRole } from '$lib/utils/auth';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;
  const authenticatedUser = requireRole(user, 'student');

  const submissions = await db
    .select({
      id: table.submission.id,
      filePath: table.submission.filePath,
      fileSize: table.submission.fileSize,
      originalFilename: table.submission.originalFilename,
      submittedAt: table.submission.submittedAt,
      project: {
        id: table.project.id,
        name: table.project.name,
        description: table.project.description,
        createdBy: table.project.createdBy
      },
      lecturer: {
        username: table.user.username
      }
    })
    .from(table.submission)
    .innerJoin(table.project, eq(table.submission.projectId, table.project.id))
    .innerJoin(table.user, eq(table.project.createdBy, table.user.id))
    .where(eq(table.submission.studentId, authenticatedUser.id))
    .orderBy(desc(table.submission.submittedAt))
    .limit(5);

  const assignedProjects = await db
    .select({
      id: table.projectStudent.projectId,
      project: {
        id: table.project.id,
        name: table.project.name,
        description: table.project.description,
        isHidden: table.project.isHidden
      }
    })
    .from(table.projectStudent)
    .innerJoin(table.project, eq(table.projectStudent.projectId, table.project.id))
    .where(
      eq(table.projectStudent.studentId, authenticatedUser.id)
    )
    .limit(10);

  const groups = await db
    .select({
      id: table.groupMember.id,
      groupId: table.groupMember.groupId,
      group: {
        name: table.group.name,
        description: table.group.description
      }
    })
    .from(table.groupMember)
    .innerJoin(
      table.group,
      eq(table.groupMember.groupId, table.group.id)
    )
    .where(eq(table.groupMember.studentId, authenticatedUser.id))
    .limit(3);

  const visibleProjects = assignedProjects
    .filter(p => !p.project.isHidden)
    .map(p => ({
      id: p.project.id,
      name: p.project.name,
      description: p.project.description
    }));

  const visibleSubmissions = submissions.filter(s => !assignedProjects.find(p =>
    p.project.id === s.project.id && p.project.isHidden
  ));

  return {
    user: authenticatedUser,
    submissions: visibleSubmissions,
    groups,
    projects: visibleProjects
  };
};