import { fail, redirect } from '@sveltejs/kit';
import { eq } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;

  if (!user || user.role !== 'lecturer') {
    return redirect(303, '/auth/login');
  }

  if (!user.isApproved) {
    return redirect(303, '/pending');
  }

  const createdGroups = await db
    .select({
      id: table.group.id,
      name: table.group.name,
      description: table.group.description
    })
    .from(table.group)
    .where(eq(table.group.createdBy, user.id));

  const managedGroups = await db
    .select({
      id: table.group.id,
      name: table.group.name,
      description: table.group.description
    })
    .from(table.group)
    .innerJoin(
      table.groupManager,
      eq(table.group.id, table.groupManager.groupId)
    )
    .where(eq(table.groupManager.lecturerId, user.id));

  const groupIds = new Set();
  const groups = [];

  for (const group of [...createdGroups, ...managedGroups]) {
    if (!groupIds.has(group.id)) {
      groupIds.add(group.id);
      groups.push(group);
    }
  }

  const groupStudents = await Promise.all(
    groups.map(async (group) => {
      const students = await db
        .select({
          id: table.user.id,
          username: table.user.username,
          name: table.user.name,
          email: table.user.email,
          membershipId: table.groupMember.id
        })
        .from(table.groupMember)
        .innerJoin(
          table.user,
          eq(table.groupMember.studentId, table.user.id)
        )
        .where(eq(table.groupMember.groupId, group.id));

      return {
        groupId: group.id,
        groupName: group.name,
        students
      };
    })
  );

  return {
    user,
    groups,
    groupStudents
  };
};

export const actions: Actions = {
  createProject: async (event) => {
    const { user } = event.locals;

    if (!user || user.role !== 'lecturer' || !user.isApproved) {
      return fail(403, { message: 'Unauthorized' });
    }

    const formData = await event.request.formData();
    const name = formData.get('name')?.toString().trim();
    const description = formData.get('description')?.toString().trim() || null;
    const maxAttemptsStr = formData.get('maxAttempts')?.toString();
    const maxAttempts = maxAttemptsStr ? parseInt(maxAttemptsStr) : 0;
    const deadlineStr = formData.get('deadline')?.toString();
    const deadline = deadlineStr && deadlineStr.trim() !== '' ? new Date(deadlineStr) : null;
    const selectedGroups = formData.getAll('groups[]').map(g => g.toString());
    const selectedStudents = formData.getAll('students[]').map(s => s.toString());

    if (!name) {
      return fail(400, {
        message: 'Project name is required',
        data: {
          name,
          description,
          maxAttempts,
          deadline: deadlineStr || '',
          selectedGroups,
          selectedStudents
        }
      });
    }

    try {
      const projectId = crypto.randomUUID();

      await db.insert(table.project).values({
        id: projectId,
        name,
        description,
        maxAttempts,
        deadline,
        createdBy: user.id,
        updatedAt: new Date()
      });

      // Batch insert for project groups
      if (selectedGroups.length > 0) {
        const now = new Date();
        const projectGroupValues = selectedGroups.map(groupId => ({
          id: crypto.randomUUID(),
          projectId,
          groupId,
          createdBy: user.id,
          createdAt: now
        }));

        await db.insert(table.projectGroup).values(projectGroupValues);
      }

      // Batch insert for project students
      if (selectedStudents.length > 0) {
        const now = new Date();
        const projectStudentValues = selectedStudents.map(studentId => ({
          id: crypto.randomUUID(),
          projectId,
          studentId,
          createdBy: user.id,
          createdAt: now
        }));

        await db.insert(table.projectStudent).values(projectStudentValues);
      }

      return { success: true, projectId };
    } catch (error) {
      console.error('Project creation error:', error);
      return fail(500, {
        message: 'An error occurred while creating the project',
        data: {
          name,
          description,
          maxAttempts,
          deadline: deadlineStr || '',
          selectedGroups,
          selectedStudents
        }
      });
    }
  }
};