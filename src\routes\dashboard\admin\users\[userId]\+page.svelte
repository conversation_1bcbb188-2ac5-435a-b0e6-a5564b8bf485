<script lang="ts">
  import { enhance } from '$app/forms';
  import {
    <PERSON>Header,
    BackButton,
    FormInput,
    FormSelect,
    AlertMessage,
    Button,
    Card
  } from '$lib/components/ui';

  let { data, form } = $props();

  function formatDate(dateString: string | Date | null | undefined): string {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString();
  }
</script>

<div>
  <div class="mb-6">
    <BackButton href="/dashboard/admin/users" label="Back to Users" />
    <PageHeader title="Edit User: {data.user.username}" />
  </div>

  {#if form?.success}
    <AlertMessage
      type="success"
      message={form.message}
      class="mb-6"
    />
  {/if}

  <Card class="mb-6">
    <h2 class="text-xl font-semibold mb-4">User Information</h2>

    <form method="POST" action="?/updateUser" use:enhance class="space-y-6">
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <FormInput
          id="username"
          name="username"
          label="Username"
          value={data.user.username}
          disabled={true}
          helpText="Username cannot be changed"
        />

        <FormInput
          id="email"
          name="email"
          type="email"
          label="Email"
          value={data.user.email}
          required
        />

        <FormSelect
          id="role"
          name="role"
          label="Role"
          value={data.user.role}
          options={[
            { value: 'student', label: 'Student' },
            { value: 'lecturer', label: 'Lecturer' },
            { value: 'admin', label: 'Admin' }
          ]}
        />

        <FormInput
          id="createdAt"
          name="createdAt"
          label="Created"
          value={formatDate(data.user.createdAt)}
          disabled={true}
        />
      </div>

      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <input
            type="checkbox"
            id="isActive"
            name="isActive"
            checked={data.user.isActive || false}
            class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <label for="isActive" class="ml-2 block text-sm text-gray-700">
            Active
          </label>
        </div>

        {#if data.user.role === 'lecturer'}
          <div class="flex items-center">
            <input
              type="checkbox"
              id="isApproved"
              name="isApproved"
              checked={data.user.isApproved || false}
              class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label for="isApproved" class="ml-2 block text-sm text-gray-700">
              Approved
            </label>
          </div>
        {/if}
      </div>

      <div>
        <Button
          type="submit"
          variant="primary"
        >
          Update User
        </Button>
      </div>
    </form>
  </Card>

  {#if data.user.role === 'lecturer' || data.user.role === 'admin'}
    <Card>
      <h2 class="text-xl font-semibold mb-4">Organization Information</h2>

      {#if data.organization}
        <div class="space-y-4">
          <div>
            <p class="text-sm font-medium text-gray-700">Organization Name</p>
            <p class="mt-1">{data.organization.name}</p>
          </div>

          <div>
            <p class="text-sm font-medium text-gray-700">Status</p>
            <div class="mt-1">
              {#if data.organization.isActive}
                <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Active</span>
              {:else}
                <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">Inactive</span>
              {/if}

              {#if data.organization.isFrozen}
                <span class="ml-2 px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">Frozen</span>
              {/if}
            </div>
          </div>
        </div>
      {:else}
        <p class="text-gray-500">This user is not associated with any organization.</p>
      {/if}
    </Card>
  {/if}
</div>