import { redirect } from '@sveltejs/kit';
import { eq, desc } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { PageServerLoad } from './$types';
import { requireRole } from '$lib/utils/auth';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;
  const authenticatedUser = requireRole(user, 'lecturer');

  if (!authenticatedUser.isApproved) {
    return redirect(303, '/pending-approval');
  }

  let organizationInfo = null;

  if (authenticatedUser.organization) {
    const [organization] = await db
      .select()
      .from(table.organization)
      .where(eq(table.organization.id, authenticatedUser.organization));

    if (organization) {
      organizationInfo = {
        id: organization.id,
        name: organization.name
      };
    }
  }

  const recentProjects = await db
    .select()
    .from(table.project)
    .where(eq(table.project.createdBy, authenticatedUser.id))
    .orderBy(desc(table.project.updatedAt))
    .limit(3);

  const recentSubmissions = await db
    .select({
      id: table.submission.id,
      originalFilename: table.submission.originalFilename,
      submittedAt: table.submission.submittedAt,
      project: {
        id: table.project.id,
        name: table.project.name
      },
      student: {
        id: table.user.id,
        username: table.user.username
      }
    })
    .from(table.submission)
    .innerJoin(table.project, eq(table.submission.projectId, table.project.id))
    .innerJoin(table.user, eq(table.submission.studentId, table.user.id))
    .where(eq(table.project.createdBy, authenticatedUser.id))
    .orderBy(desc(table.submission.submittedAt))
    .limit(5);

  // Get created groups
  const createdGroups = await db
    .select({
      id: table.group.id,
      name: table.group.name,
      description: table.group.description,
      createdAt: table.group.createdAt
    })
    .from(table.group)
    .where(eq(table.group.createdBy, authenticatedUser.id))
    .orderBy(desc(table.group.createdAt))
    .limit(2);

  // Get managed groups
  const managedGroups = await db
    .select({
      id: table.group.id,
      name: table.group.name,
      description: table.group.description,
      createdAt: table.group.createdAt
    })
    .from(table.group)
    .innerJoin(
      table.groupManager,
      eq(table.group.id, table.groupManager.groupId)
    )
    .where(eq(table.groupManager.lecturerId, authenticatedUser.id))
    .orderBy(desc(table.group.createdAt))
    .limit(2);

  const groupIds = new Set();
  const recentGroups = [];

  // Sort all groups by createdAt
  const allGroups = [...createdGroups, ...managedGroups]
    .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

  // Take the first 3 unique groups
  for (const group of allGroups) {
    if (!groupIds.has(group.id)) {
      groupIds.add(group.id);
      recentGroups.push(group);
    }
    if (recentGroups.length >= 3) break;
  }

  return {
    user: authenticatedUser,
    organization: organizationInfo,
    recentProjects,
    recentSubmissions,
    recentGroups
  };
};