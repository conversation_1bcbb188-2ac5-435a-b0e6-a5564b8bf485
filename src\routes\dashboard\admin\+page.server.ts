import { and, count, eq, or } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { PageServerLoad } from './$types';
import { requireRole } from '$lib/utils/auth';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;
  const authenticatedUser = requireRole(user, 'admin');

  const [pendingApprovalsResult] = await db
    .select({ count: count() })
    .from(table.user)
    .where(
      and(
        eq(table.user.role, 'lecturer'),
        eq(table.user.isApproved, false),
        eq(table.user.isActive, true),
        authenticatedUser.organization ?
          eq(table.user.organization, authenticatedUser.organization) :
          undefined
      )
    );

  // Get total users count with organization filter
  const [totalUsersResult] = await db
    .select({ count: count() })
    .from(table.user)
    .where(
      authenticatedUser.organization ?
        and(
          eq(table.user.organization, authenticatedUser.organization),
          or(
            eq(table.user.role, 'student'),
            eq(table.user.role, 'lecturer')
          )
        ) :
        undefined
    );

  // Get active user count
  const [activeUsersResult] = await db
    .select({ count: count() })
    .from(table.user)
    .where(
      and(
        eq(table.user.isActive, true),
        authenticatedUser.organization ?
          and(
            eq(table.user.organization, authenticatedUser.organization),
            or(
              eq(table.user.role, 'student'),
              eq(table.user.role, 'lecturer')
            )
          ) :
          undefined
      )
    );

  const [totalProjectsResult] = await db
    .select({ count: count() })
    .from(table.project);

  const [totalSubmissionsResult] = await db
    .select({ count: count() })
    .from(table.submission);

  return {
    user: authenticatedUser,
    pendingApprovals: pendingApprovalsResult?.count || 0,
    stats: {
      totalUsers: totalUsersResult?.count || 0,
      activeUsers: activeUsersResult?.count || 0,
      totalProjects: totalProjectsResult?.count || 0,
      totalSubmissions: totalSubmissionsResult?.count || 0
    }
  };
};