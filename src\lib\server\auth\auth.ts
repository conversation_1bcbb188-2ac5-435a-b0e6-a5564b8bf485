import type { RequestEvent } from '@sveltejs/kit';
import { eq } from 'drizzle-orm';
import { sha256 } from '@oslojs/crypto/sha2';
import { encodeBase64url, encodeHexLowerCase } from '@oslojs/encoding';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';

const MINUTE_IN_MS = 1000 * 60;
const HOUR_IN_MS = MINUTE_IN_MS * 60;
const DAY_IN_MS = HOUR_IN_MS * 24;

const SESSION_CONFIG = {
  DEFAULT_DURATION_HOURS: 3,
  RENEW_THRESHOLD_HOURS: 1,
  TOKEN_SIZE_BYTES: 32 // Increased from 24 to 32 bytes for better security
};

const getSessionDuration = (): number => {
  const envDuration = parseInt(process.env.SESSION_DURATION_HOURS || '');
  return (envDuration || SESSION_CONFIG.DEFAULT_DURATION_HOURS) * HOUR_IN_MS;
};

export const sessionCookieName = 'auth-session';

export function generateSessionToken() {
  const bytes = crypto.getRandomValues(new Uint8Array(SESSION_CONFIG.TOKEN_SIZE_BYTES));
  const token = encodeBase64url(bytes);
  return token;
}

export async function createSession(token: string, userId: string) {
  const sessionId = encodeHexLowerCase(sha256(new TextEncoder().encode(token)));

  // Invalidate any existing sessions for this user to prevent session fixation
  await db.delete(table.session).where(eq(table.session.userId, userId));

  const session: table.Session = {
    id: sessionId,
    userId,
    expiresAt: new Date(Date.now() + getSessionDuration())
  };
  await db.insert(table.session).values(session);
  return session;
}

export async function validateSessionToken(token: string) {
	const sessionId = encodeHexLowerCase(sha256(new TextEncoder().encode(token)));
	const [result] = await db
		.select({
			user: {
				id: table.user.id,
				username: table.user.username,
				role: table.user.role,
				isActive: table.user.isActive,
				isApproved: table.user.isApproved,
				email: table.user.email,
				organization: table.user.organization
			},
			session: table.session
		})
		.from(table.session)
		.innerJoin(table.user, eq(table.session.userId, table.user.id))
		.where(eq(table.session.id, sessionId));

	if (!result) {
		return { session: null, user: null };
	}

	const { session, user } = result;

	if (!user.isActive) {
		await invalidateSession(session.id);
		return { session: null, user: null };
	}

	const sessionExpired = Date.now() >= session.expiresAt.getTime();
	if (sessionExpired) {
		await db.delete(table.session).where(eq(table.session.id, session.id));
		return { session: null, user: null };
	}

	const renewalThresholdMs = SESSION_CONFIG.RENEW_THRESHOLD_HOURS * HOUR_IN_MS;
	const shouldRenewSession = Date.now() >= session.expiresAt.getTime() - renewalThresholdMs;

	if (shouldRenewSession) {
		// Set new expiration date
		session.expiresAt = new Date(Date.now() + getSessionDuration());
		await db
			.update(table.session)
			.set({ expiresAt: session.expiresAt })
			.where(eq(table.session.id, session.id));
	}

	return { session, user };
}

export type SessionValidationResult = Awaited<ReturnType<typeof validateSessionToken>>;
export type { SessionUser } from '$lib/types';

export async function invalidateSession(sessionId: string) {
	await db.delete(table.session).where(eq(table.session.id, sessionId));
}

export function setSessionTokenCookie(event: RequestEvent, token: string, expiresAt: Date) {
	event.cookies.set(sessionCookieName, token, {
		expires: expiresAt,
		path: '/',
		httpOnly: true,
		secure: process.env.NODE_ENV === 'production',
		sameSite: 'lax'
	});
}

export function deleteSessionTokenCookie(event: RequestEvent) {
	event.cookies.delete(sessionCookieName, {
		path: '/'
	});
}

export function hasPermission(user: SessionValidationResult['user'], requiredRoles: Array<'student' | 'lecturer' | 'admin' | 'developer'>) {
	if (!user) return false;
	return requiredRoles.includes(user.role);
}
