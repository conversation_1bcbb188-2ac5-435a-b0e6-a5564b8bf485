import * as auth from '$lib/server/auth';
import type { Actions, PageServerLoad } from './$types';
import { redirect } from '@sveltejs/kit';
import { requireAuth, redirectToRoleDashboard } from '$lib/utils/auth';

export const load: PageServerLoad = async (event) => {
	const { user } = event.locals;
	const authenticatedUser = requireAuth(user);

	// If not a lecturer or already approved, redirect to appropriate dashboard
	if (authenticatedUser.role !== 'lecturer' || authenticatedUser.isApproved) {
		return redirectToRoleDashboard(authenticatedUser);
	}

	return { user: authenticatedUser };
};

export const actions: Actions = {
	logout: async (event) => {
		if (event.locals.session) {
			await auth.invalidateSession(event.locals.session.id);
		}
		auth.deleteSessionTokenCookie(event);

		return redirect(303, '/auth/login');
	}
};