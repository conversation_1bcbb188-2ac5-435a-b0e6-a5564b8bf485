import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;

  if (!user) {
    return redirect(303, '/auth/login');
  }

  if (user.role !== 'developer') {
    const redirectMap = {
      student: '/dashboard/student',
      lecturer: '/dashboard/lecturer',
      admin: '/dashboard/admin'
    };

    return redirect(303, redirectMap[user.role as keyof typeof redirectMap] || '/');
  }

  return {
    user
  };
};
