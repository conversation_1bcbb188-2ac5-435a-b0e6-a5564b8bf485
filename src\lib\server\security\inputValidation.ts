import { fail } from '@sveltejs/kit';

/**
 * Enhanced input validation with security checks
 */

export interface ValidationResult {
  isValid: boolean;
  error?: string;
  sanitized?: any;
}

/**
 * Validates and sanitizes file uploads
 */
export function validateFileUpload(file: File, allowedTypes: string[], maxSize: number): ValidationResult {
  if (!file) {
    return { isValid: false, error: 'No file provided' };
  }

  if (file.size === 0) {
    return { isValid: false, error: 'File is empty' };
  }

  if (file.size > maxSize) {
    return { isValid: false, error: `File size exceeds maximum allowed size of ${Math.round(maxSize / (1024 * 1024))}MB` };
  }

  if (!allowedTypes.includes(file.type)) {
    return { isValid: false, error: `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}` };
  }

  // Check for suspicious file names
  const suspiciousPatterns = [
    /\.\./,           // Directory traversal
    /[<>:"|?*]/,      // Invalid filename characters
    /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i, // Windows reserved names
    /^\./,            // Hidden files
    /\.(exe|bat|cmd|scr|pif|com|vbs|js|jar|app)$/i // Executable extensions
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(file.name)) {
      return { isValid: false, error: 'Suspicious filename detected' };
    }
  }

  return { isValid: true };
}

/**
 * Validates project ID format
 */
export function validateProjectId(projectId: unknown): ValidationResult {
  if (typeof projectId !== 'string') {
    return { isValid: false, error: 'Project ID must be a string' };
  }

  if (projectId.length === 0) {
    return { isValid: false, error: 'Project ID cannot be empty' };
  }

  // UUID format validation
  const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidPattern.test(projectId)) {
    return { isValid: false, error: 'Invalid project ID format' };
  }

  return { isValid: true, sanitized: projectId };
}

/**
 * Validates user ID format
 */
export function validateUserId(userId: unknown): ValidationResult {
  if (typeof userId !== 'string') {
    return { isValid: false, error: 'User ID must be a string' };
  }

  if (userId.length === 0) {
    return { isValid: false, error: 'User ID cannot be empty' };
  }

  // UUID format validation
  const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidPattern.test(userId)) {
    return { isValid: false, error: 'Invalid user ID format' };
  }

  return { isValid: true, sanitized: userId };
}

/**
 * Validates and sanitizes text input
 */
export function validateTextInput(input: unknown, minLength: number = 0, maxLength: number = 1000): ValidationResult {
  if (typeof input !== 'string') {
    return { isValid: false, error: 'Input must be a string' };
  }

  const trimmed = input.trim();

  if (trimmed.length < minLength) {
    return { isValid: false, error: `Input must be at least ${minLength} characters long` };
  }

  if (trimmed.length > maxLength) {
    return { isValid: false, error: `Input must not exceed ${maxLength} characters` };
  }

  // Check for potential XSS patterns
  const xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi
  ];

  for (const pattern of xssPatterns) {
    if (pattern.test(trimmed)) {
      return { isValid: false, error: 'Potentially malicious content detected' };
    }
  }

  return { isValid: true, sanitized: trimmed };
}

/**
 * Validates email format with additional security checks
 */
export function validateEmailSecure(email: unknown): ValidationResult {
  if (typeof email !== 'string') {
    return { isValid: false, error: 'Email must be a string' };
  }

  const trimmed = email.trim().toLowerCase();

  if (trimmed.length < 3 || trimmed.length > 255) {
    return { isValid: false, error: 'Email must be between 3 and 255 characters' };
  }

  // Enhanced email validation
  const emailPattern = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  
  if (!emailPattern.test(trimmed)) {
    return { isValid: false, error: 'Invalid email format' };
  }

  // Check for suspicious patterns
  const suspiciousPatterns = [
    /\+.*\+/,         // Multiple plus signs
    /\.{2,}/,         // Multiple consecutive dots
    /@.*@/,           // Multiple @ symbols
    /^\.|\.$|@\./     // Starting/ending with dots or dot after @
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(trimmed)) {
      return { isValid: false, error: 'Invalid email format' };
    }
  }

  return { isValid: true, sanitized: trimmed };
}

/**
 * Validates numeric input with range checking
 */
export function validateNumericInput(input: unknown, min?: number, max?: number): ValidationResult {
  const num = Number(input);

  if (isNaN(num)) {
    return { isValid: false, error: 'Input must be a valid number' };
  }

  if (min !== undefined && num < min) {
    return { isValid: false, error: `Value must be at least ${min}` };
  }

  if (max !== undefined && num > max) {
    return { isValid: false, error: `Value must not exceed ${max}` };
  }

  return { isValid: true, sanitized: num };
}

/**
 * Validates boolean input
 */
export function validateBooleanInput(input: unknown): ValidationResult {
  if (typeof input === 'boolean') {
    return { isValid: true, sanitized: input };
  }

  if (typeof input === 'string') {
    const lower = input.toLowerCase();
    if (lower === 'true' || lower === '1' || lower === 'yes') {
      return { isValid: true, sanitized: true };
    }
    if (lower === 'false' || lower === '0' || lower === 'no' || lower === '') {
      return { isValid: true, sanitized: false };
    }
  }

  return { isValid: false, error: 'Input must be a valid boolean value' };
}

/**
 * Creates a validation error response
 */
export function createValidationError(message: string, status: number = 400) {
  return fail(status, { success: false, message });
}
