<script lang="ts">
    import {
      Card,
      ActionButton,
      ListItem,
      PageHeader,
      DashboardSection,
      EmptyState,
      ViewAllLink
    } from '$lib/components/ui';
    import { locale, t } from '$lib/stores/locale';

    let { data } = $props();

    // Use reactive store access instead of manual state management
    const currentLocale = $derived($locale);
  </script>

  <div>
    <PageHeader title={t('dashboard.student.title', currentLocale)} />

    <DashboardSection marginBottom={true}>
      <Card title={t('dashboard.quickActions', currentLocale)}>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <ActionButton
            href="/dashboard/student/submit"
            color="purple"
            title={t('dashboard.student.myProjects', currentLocale)}
            description={t('dashboard.student.viewAssignedProjects', currentLocale)}
          />
          <ActionButton
            href="/dashboard/student/submissions"
            color="blue"
            title={t('dashboard.student.mySubmissions', currentLocale)}
            description={t('dashboard.student.viewSubmissionHistory', currentLocale)}
          />
          <ActionButton
            href="/dashboard/student/groups"
            color="green"
            title={t('dashboard.student.myGroups', currentLocale)}
            description={t('dashboard.student.viewLectureGroups', currentLocale)}
          />
        </div>
      </Card>
    </DashboardSection>

    <DashboardSection cols="3" gap="6">
      <Card title={t('dashboard.student.recentActivity', currentLocale)}>
        {#if data.submissions && data.submissions.length > 0}
          <ul class="space-y-2">
            {#each data.submissions as submission}
              <ListItem href="/dashboard/student/submissions">
                <div class="flex justify-between">
                  <span class="font-medium text-gray-900 dark:text-white">{submission.originalFilename}</span>
                  <span class="text-sm text-gray-500 dark:text-gray-400">{new Date(submission.submittedAt).toLocaleDateString()}</span>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('dashboard.student.project', currentLocale)}: {submission.project?.name || 'N/A'}</p>
              </ListItem>
            {/each}
          </ul>
        {:else}
          <EmptyState
            message={t('dashboard.student.noRecentSubmissions', currentLocale)}
            description={t('dashboard.student.noRecentSubmissionsDesc', currentLocale)}
            icon="document"
          />
        {/if}
      </Card>

      <Card title={t('dashboard.student.myProjects', currentLocale)}>
        {#if data.projects && data.projects.length > 0}
          <ul class="space-y-2">
            {#each data.projects as project}
              <ListItem href={`/dashboard/student/projects/${project.id}`}>
                <div class="flex justify-between">
                  <span class="font-medium text-gray-900 dark:text-white">{project.name}</span>
                </div>
                {#if project.description}
                  <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{project.description}</p>
                {/if}
              </ListItem>
            {/each}
            {#if data.projects.length >= 3}
              <ViewAllLink
                href="/dashboard/student/submit"
                label={t('dashboard.student.viewAllProjects', currentLocale)}
              />
            {/if}
          </ul>
        {:else}
          <EmptyState
            message={t('dashboard.student.notAssignedProjects', currentLocale)}
            icon="folder"
          />
        {/if}
      </Card>

      <Card title={t('dashboard.student.myGroups', currentLocale)}>
        {#if data.groups && data.groups.length > 0}
          <ul class="space-y-2">
            {#each data.groups as group}
              <ListItem href="/dashboard/student/groups">
                <div class="flex justify-between">
                  <span class="font-medium text-gray-900 dark:text-white">{group.group.name}</span>
                </div>
                {#if group.group.description}
                  <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{group.group.description}</p>
                {/if}
              </ListItem>
            {/each}
            {#if data.groups.length >= 3}
              <ViewAllLink
                href="/dashboard/student/groups"
                label={t('common.viewAll', currentLocale)}
              />
            {/if}
          </ul>
        {:else}
          <EmptyState
            message={t('dashboard.student.notAssignedProjects', currentLocale)}
            icon="users"
          />
        {/if}
      </Card>
    </DashboardSection>
  </div>