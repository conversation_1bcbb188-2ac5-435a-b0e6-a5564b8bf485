import { redirect } from '@sveltejs/kit';
import { eq, count, and, or, sum, isNotNull } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;

  if (!user || user.role !== 'developer') {
    return redirect(303, '/auth/login');
  }

  const organizations = await db
    .select({
      id: table.organization.id,
      name: table.organization.name,
      isActive: table.organization.isActive,
      isFrozen: table.organization.isFrozen
    })
    .from(table.organization)
    .orderBy(table.organization.name);

  const adminCounts = await db
    .select({
      organization: table.user.organization,
      count: count()
    })
    .from(table.user)
    .where(
      and(
        eq(table.user.role, 'admin'),
        isNotNull(table.user.organization)
      )
    )
    .groupBy(table.user.organization);

  const adminCountMap = new Map();
  adminCounts.forEach(item => {
    if (item.organization) {
      adminCountMap.set(item.organization, item.count);
    }
  });

  const userCounts = await db
    .select({
      organization: table.user.organization,
      count: count()
    })
    .from(table.user)
    .where(
      and(
        or(
          eq(table.user.role, 'student'),
          eq(table.user.role, 'lecturer')
        ),
        isNotNull(table.user.organization)
      )
    )
    .groupBy(table.user.organization);

  const userCountMap = new Map();
  userCounts.forEach(item => {
    if (item.organization) {
      userCountMap.set(item.organization, item.count);
    }
  });

  const enhancedOrganizations = organizations.map(org => ({
    ...org,
    adminCount: adminCountMap.get(org.id) || 0,
    userCount: userCountMap.get(org.id) || 0
  }));

  const stats = {
    totalOrganizations: organizations.length,
    activeOrganizations: organizations.filter(org => org.isActive && !org.isFrozen).length,
    frozenOrganizations: organizations.filter(org => org.isFrozen).length
  };

  return {
    user,
    organizations: enhancedOrganizations,
    stats
  };
};
