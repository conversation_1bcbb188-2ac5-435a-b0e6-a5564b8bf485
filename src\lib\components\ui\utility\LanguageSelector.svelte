<script lang="ts">
  import { locale, type Locale, t } from '$lib/stores/locale';
  import { slide, fade } from 'svelte/transition';
  import { Button } from '$lib/components/ui';
  import { clickOutside } from '$lib/actions/clickOutside';

  let showMenu = $state(false);

  $effect(() => {});
  const localeFlags = {
    en: '🇬🇧',
    lt: '🇱🇹'
  };

  const currentFlag = $derived(localeFlags[$locale]);
  const currentLabel = $derived(t(`language.${$locale === 'en' ? 'english' : 'lithuanian'}`, $locale));

  const menuClass = `
    origin-top-right absolute right-0 mt-2 w-40
    rounded-md shadow-lg
    bg-white dark:bg-gray-800
    ring-1 ring-black ring-opacity-5 dark:ring-opacity-20
    focus:outline-none z-50
    border border-gray-200 dark:border-gray-700
  `;
  function changeLocale(newLocale: Locale) {
    locale.set(newLocale);
    showMenu = false;
  }

  function toggleMenu() {
    showMenu = !showMenu;
  }

  function handleClickOutside() {
    showMenu = false;
  }
</script>

<div class="relative inline-block text-left" use:clickOutside={handleClickOutside}>
  <Button
    onClick={toggleMenu}
    size="sm"
    class="w-full"
  >
    <span class="flex items-center">
      {currentFlag} {currentLabel}
    </span>
    <svg class="-mr-1 ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
      <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
    </svg>
  </Button>

  {#if showMenu}
    <div
      transition:slide={{ duration: 200 }}
      class={menuClass}
      role="menu"
      aria-orientation="vertical"
      aria-labelledby="language-menu-button"
      tabindex="-1"
    >
      <div class="py-1" role="none">
        <div transition:fade={{ duration: 150 }}>
          <Button
            onClick={() => changeLocale('en')}
            variant="light"
            size="sm"
            class="w-full justify-start"
          >
            <span class="mr-2">🇬🇧</span> {t('language.english', $locale)}
          </Button>
        </div>
        <div transition:fade={{ duration: 150 }}>
          <Button
            onClick={() => changeLocale('lt')}
            variant="light"
            size="sm"
            class="w-full justify-start"
          >
            <span class="mr-2">🇱🇹</span> {t('language.lithuanian', $locale)}
          </Button>
        </div>
      </div>
    </div>
  {/if}
</div>
