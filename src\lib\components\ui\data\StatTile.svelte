<script lang="ts">
  const {
    label = '',
    value = 0,
    icon = '',
    color = 'bg-blue-500',
    href = '',
    class: className = ''
  } = $props<{
    label: string,
    value: number | string,
    icon?: string,
    color?: string,
    href?: string,
    class?: string
  }>();
  const cardClass = $derived(`
    bg-white dark:bg-gray-800
    overflow-hidden shadow-sm rounded-lg
    border border-gray-200 dark:border-gray-700
    hover:shadow-md transition-shadow duration-200
    ${className}
  `);

  function getIconSvg(iconName: string): string {
    const icons: Record<string, string> = {
      users: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />',
      file: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />',
      folder: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />',
      check: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />',
      clock: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />',
      calendar: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />',
      chart: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />'
    };

    return icons[iconName] || icons.file;
  }

  const displayValue = $derived(
    typeof value === 'number' && !Number.isInteger(value)
      ? value.toFixed(2)
      : value.toString()
  );

  const isLink = $derived(!!href);
</script>

{#if isLink}
  <a {href} class={cardClass}>
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="{color} text-white p-3 rounded-md">
            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              {@html getIconSvg(icon)}
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{label}</dt>
            <dd>
              <div class="text-lg font-medium text-gray-900 dark:text-white">{displayValue}</div>
            </dd>
          </dl>
        </div>
      </div>
    </div>
  </a>
{:else}
  <div class={cardClass}>
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="{color} text-white p-3 rounded-md">
            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              {@html getIconSvg(icon)}
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{label}</dt>
            <dd>
              <div class="text-lg font-medium text-gray-900 dark:text-white">{displayValue}</div>
            </dd>
          </dl>
        </div>
      </div>
    </div>
  </div>
{/if}
