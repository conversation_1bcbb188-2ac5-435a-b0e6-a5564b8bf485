// This code I generated using AI
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import crypto from 'crypto';
import { encodeBase32LowerCase } from '@oslojs/encoding';
import * as argon2 from '@node-rs/argon2';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const schemaPath = path.join(__dirname, 'src', 'lib', 'server', 'db', 'schema.ts');

const schema = {
  roleEnum: { enumName: 'role', values: ['student', 'lecturer', 'admin', 'developer'] },
  weekdayEnum: { enumName: 'weekday', values: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] },
  organization: createTable('organization'),
  user: createTable('user'),
  session: createTable('session'),
  project: createTable('project'),
  submission: createTable('submission'),
  submissionAttempt: createTable('submission_attempt'),
  csFile: createTable('cs_file'),
  group: createTable('group'),
  groupMember: createTable('group_member'),
  groupManager: createTable('group_manager'),
  lectureTime: createTable('lecture_time'),
  projectGroup: createTable('project_group'),
  projectStudent: createTable('project_student'),
  passwordReset: createTable('password_reset'),
  complexityAnalysis: createTable('complexity_analysis'),
  batchFile: createTable('batch_file')
};

function createTable(tableName) {
  return { tableName };
}

const databaseUrl = process.env.DATABASE_URL || 'postgres://root:mysecretpassword@localhost:5432/local';

const client = postgres(databaseUrl, {
  max: 8,
  idle_timeout: 20,
  prepare: true
});

const db = drizzle(client, { schema });
function generateUserId() {
  const bytes = crypto.getRandomValues(new Uint8Array(15));
  const id = encodeBase32LowerCase(bytes);
  return id;
}

// Helper function to hash passwords
async function hashPassword(password) {
  const salt = crypto.randomBytes(16);
  return argon2.hash(password, {
    memoryCost: 19456,
    timeCost: 2,
    parallelism: 1,
    outputLen: 32,
    salt: salt,
    algorithm: 2 // Argon2id
  });
}

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(process.cwd(), 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Sample data
const organizations = [
  {
    id: crypto.randomUUID(),
    name: 'Vilnius University',
    description: 'The oldest university in Lithuania',
    isActive: true,
    isFrozen: false
  },
  {
    id: crypto.randomUUID(),
    name: 'Kaunas University of Technology',
    description: 'Leading technical university in Lithuania',
    isActive: true,
    isFrozen: false
  },
  {
    id: crypto.randomUUID(),
    name: 'Vilnius Tech',
    description: 'Technical university in Vilnius',
    isActive: true,
    isFrozen: false
  }
];

// Seed the database
async function seedDatabase() {
  console.log('Starting database seeding...');

  try {
    // Clear existing data (in reverse order of dependencies)
    console.log('Clearing existing data...');
    await db.delete(schema.complexityAnalysis);
    await db.delete(schema.csFile);
    await db.delete(schema.batchFile);
    await db.delete(schema.submission);
    await db.delete(schema.submissionAttempt);
    await db.delete(schema.projectStudent);
    await db.delete(schema.projectGroup);
    await db.delete(schema.project);
    await db.delete(schema.lectureTime);
    await db.delete(schema.groupMember);
    await db.delete(schema.groupManager);
    await db.delete(schema.group);
    await db.delete(schema.passwordReset);
    await db.delete(schema.session);
    await db.delete(schema.user);
    await db.delete(schema.organization);

    // Insert organizations
    console.log('Inserting organizations...');
    await db.insert(schema.organization).values(organizations);

    // Create users
    console.log('Creating users...');
    const users = {
      developers: [],
      admins: [],
      lecturers: [],
      students: []
    };

    // Create a developer
    const devId = generateUserId();
    users.developers.push({
      id: devId,
      username: 'developer',
      name: 'System Developer',
      email: '<EMAIL>',
      passwordHash: await hashPassword('password123'),
      role: 'developer',
      isActive: true,
      isApproved: true,
      organization: organizations[0].id
    });

    // Create admins
    for (let i = 0; i < 3; i++) {
      const adminId = generateUserId();
      users.admins.push({
        id: adminId,
        username: `admin${i + 1}`,
        name: `Admin User ${i + 1}`,
        email: `admin${i + 1}@example.com`,
        passwordHash: await hashPassword('password123'),
        role: 'admin',
        isActive: true,
        isApproved: true,
        organization: organizations[i % organizations.length].id
      });
    }

    // Create lecturers
    const lecturerNames = [
      'Jonas Jonaitis', 'Petras Petraitis', 'Ona Onaitė',
      'Marija Marijona', 'Tomas Tomaitis', 'Lina Linaitė',
      'Darius Dariūnas', 'Eglė Eglaitė', 'Rimas Rimaitis'
    ];

    for (let i = 0; i < lecturerNames.length; i++) {
      const lecturerId = generateUserId();
      const name = lecturerNames[i];
      const nameParts = name.split(' ');
      const username = (nameParts[0].toLowerCase() + nameParts[1].charAt(0).toLowerCase()).normalize('NFD').replace(/[\u0300-\u036f]/g, '');

      users.lecturers.push({
        id: lecturerId,
        username,
        name,
        email: `${username}@example.com`,
        passwordHash: await hashPassword('password123'),
        role: 'lecturer',
        isActive: true,
        isApproved: true,
        organization: organizations[i % organizations.length].id
      });
    }

    // Create students
    const studentNames = [
      'Aistė Aistytė', 'Benas Benaitis', 'Cezaris Cezaraitis',
      'Dovilė Dovilaitė', 'Eimantas Eimantaitis', 'Fausta Faustaitė',
      'Giedrius Giedraitis', 'Herkus Herkaitis', 'Ieva Ievaitė',
      'Justinas Justinaitis', 'Kotryna Kotrynaitė', 'Lukas Lukaitis',
      'Miglė Miglytė', 'Nojus Nojaitis', 'Olivija Olivijaitė',
      'Paulius Paulaitis', 'Raminta Ramintaitė', 'Simonas Simonaitis',
      'Tadas Tadaitis', 'Ugnė Ugnytė', 'Viltė Viltytė',
      'Žygimantas Žygimantaitis', 'Austėja Austėjaitė', 'Benas Benaitis',
      'Gabija Gabijaitė', 'Dominykas Dominykaitis', 'Emilija Emilijaitė',
      'Faustas Faustaitis', 'Gustas Gustaitis', 'Ieva Ievaitė'
    ];

    for (let i = 0; i < studentNames.length; i++) {
      const studentId = generateUserId();
      const name = studentNames[i];
      const nameParts = name.split(' ');
      const username = (nameParts[0].toLowerCase() + nameParts[1].charAt(0).toLowerCase()).normalize('NFD').replace(/[\u0300-\u036f]/g, '');

      users.students.push({
        id: studentId,
        username,
        name,
        email: `${username}@example.com`,
        passwordHash: await hashPassword('password123'),
        role: 'student',
        isActive: true,
        isApproved: true,
        organization: organizations[i % organizations.length].id
      });
    }

    // Insert all users
    const allUsers = [
      ...users.developers,
      ...users.admins,
      ...users.lecturers,
      ...users.students
    ];

    await db.insert(schema.user).values(allUsers);

    // Create groups
    console.log('Creating groups...');
    const groups = [];
    const groupNames = [
      'Programming Fundamentals', 'Data Structures', 'Algorithms',
      'Object-Oriented Programming', 'Web Development', 'Database Systems',
      'Computer Networks', 'Operating Systems', 'Software Engineering'
    ];

    for (let i = 0; i < groupNames.length; i++) {
      groups.push({
        id: crypto.randomUUID(),
        name: groupNames[i],
        description: `Group for ${groupNames[i]} course`,
        createdBy: users.lecturers[i % users.lecturers.length].id,
        createdAt: new Date(),
        updatedAt: new Date(),
        joinEnabled: i % 3 === 0, // Enable join for some groups
        joinCode: i % 3 === 0 ? 'ABC123' : null,
        joinCodeExpiry: i % 3 === 0 ? new Date(Date.now() + 24 * 60 * 60 * 1000) : null // 24 hours from now
      });
    }

    await db.insert(schema.group).values(groups);

    // Assign lecturers to groups as managers
    console.log('Assigning group managers...');
    const groupManagers = [];

    for (let i = 0; i < groups.length; i++) {
      // Assign the lecturer who created the group
      groupManagers.push({
        id: crypto.randomUUID(),
        groupId: groups[i].id,
        lecturerId: groups[i].createdBy,
        addedBy: users.admins[0].id,
        addedAt: new Date()
      });

      // Assign another lecturer to some groups
      if (i % 2 === 0) {
        const anotherLecturerIndex = (i + 1) % users.lecturers.length;
        groupManagers.push({
          id: crypto.randomUUID(),
          groupId: groups[i].id,
          lecturerId: users.lecturers[anotherLecturerIndex].id,
          addedBy: users.admins[0].id,
          addedAt: new Date()
        });
      }
    }

    await db.insert(schema.groupManager).values(groupManagers);

    // Assign students to groups
    console.log('Assigning students to groups...');
    const groupMembers = [];

    for (let i = 0; i < groups.length; i++) {
      // Assign 8-12 students to each group
      const numStudents = 8 + Math.floor(Math.random() * 5);
      const startIndex = (i * 3) % users.students.length;

      for (let j = 0; j < numStudents; j++) {
        const studentIndex = (startIndex + j) % users.students.length;
        groupMembers.push({
          id: crypto.randomUUID(),
          groupId: groups[i].id,
          studentId: users.students[studentIndex].id,
          addedBy: groups[i].createdBy, // Added by the lecturer who created the group
          addedAt: new Date()
        });
      }
    }

    await db.insert(schema.groupMember).values(groupMembers);

    // Create lecture times for groups
    console.log('Creating lecture times...');
    const lectureTimes = [];
    const weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
    const locations = ['Room 101', 'Room 202', 'Room 303', 'Room 404', 'Room 505', 'Lab 1', 'Lab 2', 'Auditorium A', 'Auditorium B'];

    for (let i = 0; i < groups.length; i++) {
      // Each group has 1-2 lecture times
      const numLectures = 1 + Math.floor(Math.random() * 2);

      for (let j = 0; j < numLectures; j++) {
        const weekday = weekdays[(i + j) % weekdays.length];
        const startHour = 8 + Math.floor(Math.random() * 10); // 8 AM to 6 PM

        lectureTimes.push({
          id: crypto.randomUUID(),
          groupId: groups[i].id,
          weekday,
          startTime: `${startHour.toString().padStart(2, '0')}:00:00`,
          endTime: `${(startHour + 1).toString().padStart(2, '0')}:30:00`,
          location: locations[i % locations.length],
          createdBy: groups[i].createdBy,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
    }

    await db.insert(schema.lectureTime).values(lectureTimes);

    // Create projects
    console.log('Creating projects...');
    const projects = [];
    const projectNames = [
      'Introduction to C#', 'Data Structures Implementation', 'Algorithm Analysis',
      'Object-Oriented Design Patterns', 'Web API Development', 'Database Design Project',
      'Network Protocol Implementation', 'Operating System Scheduler', 'Software Testing Project',
      'Mobile App Development', 'Machine Learning Basics', 'Computer Graphics Project'
    ];

    for (let i = 0; i < projectNames.length; i++) {
      // Set deadline between 1 week and 1 month from now
      const daysToDeadline = 7 + Math.floor(Math.random() * 24);
      const deadline = new Date();
      deadline.setDate(deadline.getDate() + daysToDeadline);

      projects.push({
        id: crypto.randomUUID(),
        name: projectNames[i],
        description: `Project for implementing ${projectNames[i]}`,
        maxAttempts: Math.floor(Math.random() * 5), // 0-4 attempts (0 means unlimited)
        deadline,
        isHidden: i % 5 === 0, // Some projects are hidden
        createdBy: users.lecturers[i % users.lecturers.length].id,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }

    await db.insert(schema.project).values(projects);

    // Assign projects to groups
    console.log('Assigning projects to groups...');
    const projectGroups = [];

    for (let i = 0; i < projects.length; i++) {
      // Assign each project to 1-2 groups
      const numGroups = 1 + Math.floor(Math.random() * 2);

      for (let j = 0; j < numGroups; j++) {
        const groupIndex = (i + j) % groups.length;
        projectGroups.push({
          id: crypto.randomUUID(),
          projectId: projects[i].id,
          groupId: groups[groupIndex].id,
          createdBy: projects[i].createdBy,
          createdAt: new Date()
        });
      }
    }

    await db.insert(schema.projectGroup).values(projectGroups);

    // Assign projects to individual students
    console.log('Assigning projects to individual students...');
    const projectStudents = [];

    for (let i = 0; i < projects.length; i++) {
      // Assign each project to 3-5 individual students
      const numStudents = 3 + Math.floor(Math.random() * 3);
      const startIndex = (i * 2) % users.students.length;

      for (let j = 0; j < numStudents; j++) {
        const studentIndex = (startIndex + j) % users.students.length;
        projectStudents.push({
          id: crypto.randomUUID(),
          projectId: projects[i].id,
          studentId: users.students[studentIndex].id,
          createdBy: projects[i].createdBy,
          createdAt: new Date()
        });
      }
    }

    await db.insert(schema.projectStudent).values(projectStudents);

    // Create submissions for projects
    console.log('Creating submissions...');

    // Sample CS file content templates
    const sampleCsContents = [
      // Sample 1: Sorting algorithms
      `using System;
using System.Collections.Generic;
using System.Diagnostics;

namespace AlgorithmAnalysis
{
    public class Program
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("Algorithm Analysis - Sorting Algorithms");

            // Generate test data
            int[] sizes = { 100, 1000, 10000 };

            foreach (var size in sizes)
            {
                Console.WriteLine($"\\nTesting with array size: {size}");
                int[] data = GenerateRandomArray(size);

                // Test different sorting algorithms
                TestSortingAlgorithm("Bubble Sort", data.Clone() as int[], SortingAlgorithms.BubbleSort);
                TestSortingAlgorithm("Selection Sort", data.Clone() as int[], SortingAlgorithms.SelectionSort);
                TestSortingAlgorithm("Insertion Sort", data.Clone() as int[], SortingAlgorithms.InsertionSort);
                TestSortingAlgorithm("Quick Sort", data.Clone() as int[], SortingAlgorithms.QuickSort);
            }
        }

        private static int[] GenerateRandomArray(int size)
        {
            int[] array = new int[size];
            Random random = new Random();

            for (int i = 0; i < size; i++)
            {
                array[i] = random.Next(1, 10000);
            }

            return array;
        }

        private static void TestSortingAlgorithm(string name, int[] data, Action<int[]> sortMethod)
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            sortMethod(data);

            stopwatch.Stop();
            Console.WriteLine($"{name}: {stopwatch.ElapsedMilliseconds} ms");
        }
    }

    public static class SortingAlgorithms
    {
        public static void BubbleSort(int[] arr)
        {
            int n = arr.Length;
            for (int i = 0; i < n - 1; i++)
            {
                for (int j = 0; j < n - i - 1; j++)
                {
                    if (arr[j] > arr[j + 1])
                    {
                        // Swap arr[j] and arr[j+1]
                        int temp = arr[j];
                        arr[j] = arr[j + 1];
                        arr[j + 1] = temp;
                    }
                }
            }
        }

        public static void SelectionSort(int[] arr)
        {
            int n = arr.Length;

            for (int i = 0; i < n - 1; i++)
            {
                int minIndex = i;

                for (int j = i + 1; j < n; j++)
                {
                    if (arr[j] < arr[minIndex])
                    {
                        minIndex = j;
                    }
                }

                // Swap the found minimum element with the element at index i
                int temp = arr[i];
                arr[i] = arr[minIndex];
                arr[minIndex] = temp;
            }
        }

        public static void InsertionSort(int[] arr)
        {
            int n = arr.Length;

            for (int i = 1; i < n; i++)
            {
                int key = arr[i];
                int j = i - 1;

                while (j >= 0 && arr[j] > key)
                {
                    arr[j + 1] = arr[j];
                    j--;
                }

                arr[j + 1] = key;
            }
        }

        public static void QuickSort(int[] arr)
        {
            QuickSort(arr, 0, arr.Length - 1);
        }

        private static void QuickSort(int[] arr, int low, int high)
        {
            if (low < high)
            {
                int partitionIndex = Partition(arr, low, high);

                QuickSort(arr, low, partitionIndex - 1);
                QuickSort(arr, partitionIndex + 1, high);
            }
        }

        private static int Partition(int[] arr, int low, int high)
        {
            int pivot = arr[high];
            int i = low - 1;

            for (int j = low; j < high; j++)
            {
                if (arr[j] <= pivot)
                {
                    i++;

                    // Swap arr[i] and arr[j]
                    int temp = arr[i];
                    arr[i] = arr[j];
                    arr[j] = temp;
                }
            }

            // Swap arr[i+1] and arr[high] (pivot)
            int temp2 = arr[i + 1];
            arr[i + 1] = arr[high];
            arr[high] = temp2;

            return i + 1;
        }
    }
}`,

      // Sample 2: Data structures
      `using System;
using System.Collections.Generic;

namespace DataStructures
{
    public class Program
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("Data Structures Implementation");

            // Test LinkedList
            Console.WriteLine("\\nTesting LinkedList:");
            CustomLinkedList<int> linkedList = new CustomLinkedList<int>();
            linkedList.Add(10);
            linkedList.Add(20);
            linkedList.Add(30);
            linkedList.PrintList();

            Console.WriteLine($"Contains 20: {linkedList.Contains(20)}");
            Console.WriteLine($"Contains 40: {linkedList.Contains(40)}");

            linkedList.Remove(20);
            Console.WriteLine("After removing 20:");
            linkedList.PrintList();

            // Test BinarySearchTree
            Console.WriteLine("\\nTesting BinarySearchTree:");
            BinarySearchTree bst = new BinarySearchTree();
            bst.Insert(50);
            bst.Insert(30);
            bst.Insert(70);
            bst.Insert(20);
            bst.Insert(40);
            bst.Insert(60);
            bst.Insert(80);

            Console.WriteLine("Inorder traversal:");
            bst.InorderTraversal();

            Console.WriteLine($"\\nSearch for 40: {bst.Search(40)}");
            Console.WriteLine($"Search for 90: {bst.Search(90)}");

            // Test Stack
            Console.WriteLine("\\nTesting Stack:");
            CustomStack<string> stack = new CustomStack<string>();
            stack.Push("First");
            stack.Push("Second");
            stack.Push("Third");

            Console.WriteLine($"Stack size: {stack.Size()}");
            Console.WriteLine($"Peek: {stack.Peek()}");
            Console.WriteLine($"Pop: {stack.Pop()}");
            Console.WriteLine($"Stack size after pop: {stack.Size()}");
        }
    }

    public class CustomLinkedList<T>
    {
        private class Node
        {
            public T Data { get; set; }
            public Node Next { get; set; }

            public Node(T data)
            {
                Data = data;
                Next = null;
            }
        }

        private Node head;

        public CustomLinkedList()
        {
            head = null;
        }

        public void Add(T data)
        {
            Node newNode = new Node(data);

            if (head == null)
            {
                head = newNode;
                return;
            }

            Node current = head;
            while (current.Next != null)
            {
                current = current.Next;
            }

            current.Next = newNode;
        }

        public bool Remove(T data)
        {
            if (head == null)
            {
                return false;
            }

            if (head.Data.Equals(data))
            {
                head = head.Next;
                return true;
            }

            Node current = head;
            while (current.Next != null && !current.Next.Data.Equals(data))
            {
                current = current.Next;
            }

            if (current.Next == null)
            {
                return false;
            }

            current.Next = current.Next.Next;
            return true;
        }

        public bool Contains(T data)
        {
            Node current = head;
            while (current != null)
            {
                if (current.Data.Equals(data))
                {
                    return true;
                }
                current = current.Next;
            }

            return false;
        }

        public void PrintList()
        {
            Node current = head;
            while (current != null)
            {
                Console.Write($"{current.Data} -> ");
                current = current.Next;
            }
            Console.WriteLine("null");
        }
    }

    public class BinarySearchTree
    {
        private class Node
        {
            public int Data { get; set; }
            public Node Left { get; set; }
            public Node Right { get; set; }

            public Node(int data)
            {
                Data = data;
                Left = null;
                Right = null;
            }
        }

        private Node root;

        public BinarySearchTree()
        {
            root = null;
        }

        public void Insert(int data)
        {
            root = InsertRec(root, data);
        }

        private Node InsertRec(Node root, int data)
        {
            if (root == null)
            {
                root = new Node(data);
                return root;
            }

            if (data < root.Data)
            {
                root.Left = InsertRec(root.Left, data);
            }
            else if (data > root.Data)
            {
                root.Right = InsertRec(root.Right, data);
            }

            return root;
        }

        public bool Search(int data)
        {
            return SearchRec(root, data);
        }

        private bool SearchRec(Node root, int data)
        {
            if (root == null)
            {
                return false;
            }

            if (root.Data == data)
            {
                return true;
            }

            if (data < root.Data)
            {
                return SearchRec(root.Left, data);
            }

            return SearchRec(root.Right, data);
        }

        public void InorderTraversal()
        {
            InorderRec(root);
            Console.WriteLine();
        }

        private void InorderRec(Node root)
        {
            if (root != null)
            {
                InorderRec(root.Left);
                Console.Write($"{root.Data} ");
                InorderRec(root.Right);
            }
        }
    }

    public class CustomStack<T>
    {
        private List<T> items;

        public CustomStack()
        {
            items = new List<T>();
        }

        public void Push(T item)
        {
            items.Add(item);
        }

        public T Pop()
        {
            if (items.Count == 0)
            {
                throw new InvalidOperationException("Stack is empty");
            }

            T item = items[items.Count - 1];
            items.RemoveAt(items.Count - 1);
            return item;
        }

        public T Peek()
        {
            if (items.Count == 0)
            {
                throw new InvalidOperationException("Stack is empty");
            }

            return items[items.Count - 1];
        }

        public int Size()
        {
            return items.Count;
        }

        public bool IsEmpty()
        {
            return items.Count == 0;
        }
    }
}`,

      // Sample 3: Graph algorithms
      `using System;
using System.Collections.Generic;

namespace GraphAlgorithms
{
    public class Program
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("Graph Algorithms Implementation");

            // Create a graph
            Graph graph = new Graph(6);
            graph.AddEdge(0, 1);
            graph.AddEdge(0, 2);
            graph.AddEdge(1, 3);
            graph.AddEdge(1, 4);
            graph.AddEdge(2, 4);
            graph.AddEdge(3, 5);
            graph.AddEdge(4, 5);

            // Test BFS
            Console.WriteLine("\\nBreadth-First Search starting from vertex 0:");
            graph.BFS(0);

            // Test DFS
            Console.WriteLine("\\nDepth-First Search starting from vertex 0:");
            graph.DFS(0);

            // Test Dijkstra's algorithm
            Console.WriteLine("\\nDijkstra's Algorithm:");
            WeightedGraph weightedGraph = new WeightedGraph(6);
            weightedGraph.AddEdge(0, 1, 4);
            weightedGraph.AddEdge(0, 2, 2);
            weightedGraph.AddEdge(1, 2, 5);
            weightedGraph.AddEdge(1, 3, 10);
            weightedGraph.AddEdge(2, 3, 3);
            weightedGraph.AddEdge(2, 4, 2);
            weightedGraph.AddEdge(3, 4, 7);
            weightedGraph.AddEdge(3, 5, 5);
            weightedGraph.AddEdge(4, 5, 6);

            weightedGraph.Dijkstra(0);
        }
    }

    public class Graph
    {
        private int vertices;
        private List<int>[] adjacencyList;

        public Graph(int vertices)
        {
            this.vertices = vertices;
            adjacencyList = new List<int>[vertices];

            for (int i = 0; i < vertices; i++)
            {
                adjacencyList[i] = new List<int>();
            }
        }

        public void AddEdge(int source, int destination)
        {
            adjacencyList[source].Add(destination);
            adjacencyList[destination].Add(source); // For undirected graph
        }

        public void BFS(int startVertex)
        {
            bool[] visited = new bool[vertices];
            Queue<int> queue = new Queue<int>();

            visited[startVertex] = true;
            queue.Enqueue(startVertex);

            while (queue.Count > 0)
            {
                int vertex = queue.Dequeue();
                Console.Write($"{vertex} ");

                foreach (int adjacent in adjacencyList[vertex])
                {
                    if (!visited[adjacent])
                    {
                        visited[adjacent] = true;
                        queue.Enqueue(adjacent);
                    }
                }
            }

            Console.WriteLine();
        }

        public void DFS(int startVertex)
        {
            bool[] visited = new bool[vertices];
            DFSUtil(startVertex, visited);
            Console.WriteLine();
        }

        private void DFSUtil(int vertex, bool[] visited)
        {
            visited[vertex] = true;
            Console.Write($"{vertex} ");

            foreach (int adjacent in adjacencyList[vertex])
            {
                if (!visited[adjacent])
                {
                    DFSUtil(adjacent, visited);
                }
            }
        }
    }

    public class WeightedGraph
    {
        private int vertices;
        private List<Edge>[] adjacencyList;

        public WeightedGraph(int vertices)
        {
            this.vertices = vertices;
            adjacencyList = new List<Edge>[vertices];

            for (int i = 0; i < vertices; i++)
            {
                adjacencyList[i] = new List<Edge>();
            }
        }

        public void AddEdge(int source, int destination, int weight)
        {
            adjacencyList[source].Add(new Edge(destination, weight));
            adjacencyList[destination].Add(new Edge(source, weight)); // For undirected graph
        }

        public void Dijkstra(int startVertex)
        {
            int[] distance = new int[vertices];
            bool[] visited = new bool[vertices];

            for (int i = 0; i < vertices; i++)
            {
                distance[i] = int.MaxValue;
                visited[i] = false;
            }

            distance[startVertex] = 0;

            for (int count = 0; count < vertices - 1; count++)
            {
                int u = MinimumDistance(distance, visited);
                visited[u] = true;

                foreach (Edge edge in adjacencyList[u])
                {
                    int v = edge.Destination;
                    int weight = edge.Weight;

                    if (!visited[v] && distance[u] != int.MaxValue && distance[u] + weight < distance[v])
                    {
                        distance[v] = distance[u] + weight;
                    }
                }
            }

            PrintDijkstra(distance, startVertex);
        }

        private int MinimumDistance(int[] distance, bool[] visited)
        {
            int min = int.MaxValue;
            int minIndex = -1;

            for (int v = 0; v < vertices; v++)
            {
                if (!visited[v] && distance[v] <= min)
                {
                    min = distance[v];
                    minIndex = v;
                }
            }

            return minIndex;
        }

        private void PrintDijkstra(int[] distance, int startVertex)
        {
            Console.WriteLine("Vertex \\t Distance from Source");
            for (int i = 0; i < vertices; i++)
            {
                Console.WriteLine($"{i} \\t {distance[i]}");
            }
        }
    }

    public class Edge
    {
        public int Destination { get; set; }
        public int Weight { get; set; }

        public Edge(int destination, int weight)
        {
            Destination = destination;
            Weight = weight;
        }
    }
}`
    ];

    // Function to get a random CS file content
    const getRandomCsContent = () => {
      return sampleCsContents[Math.floor(Math.random() * sampleCsContents.length)];
    };

    // Create submissions
    const submissions = [];
    const csFiles = [];
    const complexityAnalyses = [];

    // Get all students assigned to projects (either directly or via groups)
    const assignedStudents = new Set();

    // Add students directly assigned to projects
    projectStudents.forEach(ps => {
      assignedStudents.add(ps.studentId);
    });

    // Add students from groups assigned to projects
    for (const pg of projectGroups) {
      const groupStudents = groupMembers.filter(gm => gm.groupId === pg.groupId);
      groupStudents.forEach(gs => {
        assignedStudents.add(gs.studentId);
      });
    }

    // Create 1-3 submissions for each student-project pair
    for (const project of projects) {
      // Get students assigned to this project
      const projectStudentIds = projectStudents
        .filter(ps => ps.projectId === project.id)
        .map(ps => ps.studentId);

      // Get groups assigned to this project
      const projectGroupIds = projectGroups
        .filter(pg => pg.projectId === project.id)
        .map(pg => pg.groupId);

      // Get students from those groups
      const groupStudentIds = groupMembers
        .filter(gm => projectGroupIds.includes(gm.groupId))
        .map(gm => gm.studentId);

      // Combine all student IDs (remove duplicates)
      const allStudentIds = [...new Set([...projectStudentIds, ...groupStudentIds])];

      // Create submissions for each student
      for (const studentId of allStudentIds) {
        const numSubmissions = 1 + Math.floor(Math.random() * 3);

        for (let i = 0; i < numSubmissions; i++) {
          const submissionId = crypto.randomUUID();
          const fileName = `submission_${project.name.replace(/\s+/g, '_').toLowerCase()}_${i + 1}.zip`;
          const filePath = path.join('uploads', fileName);

          // Create a dummy file in the uploads directory
          fs.writeFileSync(filePath, 'Dummy file content');

          const csContent = getRandomCsContent();
          const hasCsFile = Math.random() > 0.3;
          const csFileName = hasCsFile ? 'Program.cs' : null;

          submissions.push({
            id: submissionId,
            projectId: project.id,
            studentId,
            filePath,
            fileSize: 1024 + Math.floor(Math.random() * 10000),
            originalFilename: fileName,
            csFileContent: hasCsFile ? csContent : null,
            csFileName: csFileName,
            hasComplexityAnalysis: Math.random() > 0.7,
            submittedAt: new Date(Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)) // Random time in the last week
          });

          // Add CS files for some submissions
          if (hasCsFile) {
            csFiles.push({
              id: crypto.randomUUID(),
              submissionId,
              fileName: csFileName,
              filePath: path.join('uploads', 'cs_files', `${submissionId}_Program.cs`),
              fileContent: csContent,
              createdAt: new Date()
            });
          }

          // Add complexity analysis for some submissions
          if (Math.random() > 0.7) {
            complexityAnalyses.push({
              id: crypto.randomUUID(),
              submissionId,
              tables: JSON.stringify([
                {
                  depth: [10, 100, 1000, 10000],
                  time: [0.001, 0.01, 0.1, 1.0]
                }
              ]),
              includedPages: JSON.stringify([1, 2]),
              excludedPages: JSON.stringify([3]),
              createdAt: new Date()
            });
          }
        }
      }
    }

    await db.insert(schema.submission).values(submissions);

    if (csFiles.length > 0) {
      await db.insert(schema.csFile).values(csFiles);
    }

    if (complexityAnalyses.length > 0) {
      await db.insert(schema.complexityAnalysis).values(complexityAnalyses);
    }

    console.log('Database seeding completed successfully!');
    console.log(`Created ${organizations.length} organizations`);
    console.log(`Created ${allUsers.length} users (${users.students.length} students, ${users.lecturers.length} lecturers, ${users.admins.length} admins, ${users.developers.length} developers)`);
    console.log(`Created ${groups.length} groups`);
    console.log(`Created ${groupMembers.length} group memberships`);
    console.log(`Created ${lectureTimes.length} lecture times`);
    console.log(`Created ${projects.length} projects`);
    console.log(`Created ${projectGroups.length} project-group assignments`);
    console.log(`Created ${projectStudents.length} project-student assignments`);
    console.log(`Created ${submissions.length} submissions`);
    console.log(`Created ${csFiles.length} CS files`);
    console.log(`Created ${complexityAnalyses.length} complexity analyses`);

  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    // Close the database connection
    await client.end();
  }
}

// Run the seed function
seedDatabase();
