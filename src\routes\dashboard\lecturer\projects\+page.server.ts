
import { redirect } from '@sveltejs/kit';
import { eq, count, inArray, sql } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;

  if (!user || user.role !== 'lecturer') {
    return redirect(303, '/auth/login');
  }

  if (!user.isApproved) {
    return redirect(303, '/pending');
  }

  const projects = await db
    .select({
      id: table.project.id,
      name: table.project.name,
      description: table.project.description,
      maxAttempts: table.project.maxAttempts,
      deadline: table.project.deadline,
      isHidden: table.project.isHidden,
      createdAt: table.project.createdAt,
      updatedAt: table.project.updatedAt
    })
    .from(table.project)
    .where(eq(table.project.createdBy, user.id))
    .orderBy(table.project.updatedAt);

  // If no projects, return early
  if (projects.length === 0) {
    return {
      user,
      projects: []
    };
  }

  // Get all project IDs
  const projectIds = projects.map(p => p.id);

  // Get submission counts for all projects in a single query
  const submissionCounts = await db
    .select({
      projectId: table.submission.projectId,
      count: count(table.submission.id)
    })
    .from(table.submission)
    .where(inArray(table.submission.projectId, projectIds))
    .groupBy(table.submission.projectId);

  // Create a map for quick lookup
  const countMap = new Map();
  submissionCounts.forEach(item => {
    countMap.set(item.projectId, item.count);
  });

  // Combine projects with their submission counts
  const projectsWithCounts = projects.map(project => ({
    ...project,
    submissionCount: countMap.get(project.id) || 0
  }));

  return {
    user,
    projects: projectsWithCounts
  };
};