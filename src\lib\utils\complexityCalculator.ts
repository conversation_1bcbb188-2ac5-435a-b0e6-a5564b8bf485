export function calculateComplexity(time: number[], depth: number[]) {
  if (!time || !depth || time.length < 2 || depth.length < 2 || time.length !== depth.length) {
    return {
      notation: 'Unknown',
      description: 'Insufficient data',
      bestModel: 'unknown',
      rSquared: 0,
      details: {}
    };
  }

  const ratios = [];
  for (let i = 1; i < time.length; i++) {
    ratios.push({
      fromDepth: depth[i-1],
      toDepth: depth[i],
      ratio: time[i] / time[i-1]
    });
  }

  const logTime = time.map(t => Math.log(t));

  const logDiffs = [];
  for (let i = 1; i < logTime.length; i++) {
    const depthDiff = depth[i] - depth[i-1];
    const logDiff = logTime[i] - logTime[i-1];
    const rate = logDiff / depthDiff;
    logDiffs.push({
      fromDepth: depth[i-1],
      toDepth: depth[i],
      rate: rate
    });
  }

  const models = fitModels(depth, time, logTime);
  const bestModel = findBestModel(models);
  const complexityInfo = getComplexityNotation(bestModel, models);

  return {
    notation: complexityInfo.notation,
    description: complexityInfo.description,
    bestModel: bestModel,
    rSquared: models[bestModel].rSquared,
    details: {
      ratios,
      logDiffs,
      models
    }
  };
}

function fitModels(depth: number[], time: number[], logTime: number[]): ComplexityModels {
  const X = depth.map(d => [1, d]);
  const Y = logTime.map(lt => [lt]);

  const XtX = multiplyMatrices(transposeMatrix(X), X);
  const XtY = multiplyMatrices(transposeMatrix(X), Y);

  const beta = multiplyMatrices(invertMatrix(XtX), XtY);

  const Yhat = multiplyMatrices(X, beta);
  const Ymean = mean(Y.map(y => y[0]));
  const SST = sum(Y.map(y => Math.pow(y[0] - Ymean, 2)));
  const SSE = sum(Y.map((y, i) => Math.pow(y[0] - Yhat[i][0], 2)));
  const RsquaredExp = 1 - SSE/SST;

  const factorialModel = depth.map(d => [1, d * Math.log(Math.max(d, 1))]);
  const betaFactorial = multiplyMatrices(
    invertMatrix(multiplyMatrices(transposeMatrix(factorialModel), factorialModel)),
    multiplyMatrices(transposeMatrix(factorialModel), Y)
  );

  const YhatFactorial = multiplyMatrices(factorialModel, betaFactorial);
  const SSEFactorial = sum(Y.map((y, i) => Math.pow(y[0] - YhatFactorial[i][0], 2)));
  const RsquaredFactorial = 1 - SSEFactorial/SST;

  const linearModel = depth.map(d => [1, d]);
  const betaLinear = multiplyMatrices(
    invertMatrix(multiplyMatrices(transposeMatrix(linearModel), linearModel)),
    multiplyMatrices(transposeMatrix(linearModel), Y)
  );

  const YhatLinear = multiplyMatrices(linearModel, betaLinear);
  const SSELinear = sum(Y.map((y, i) => Math.pow(y[0] - YhatLinear[i][0], 2)));
  const RsquaredLinear = 1 - SSELinear/SST;

  const nLogNModel = depth.map(d => [1, d * Math.log(Math.max(d, 2))]);
  const betaNLogN = multiplyMatrices(
    invertMatrix(multiplyMatrices(transposeMatrix(nLogNModel), nLogNModel)),
    multiplyMatrices(transposeMatrix(nLogNModel), Y)
  );

  const YhatNLogN = multiplyMatrices(nLogNModel, betaNLogN);
  const SSENLogN = sum(Y.map((y, i) => Math.pow(y[0] - YhatNLogN[i][0], 2)));
  const RsquaredNLogN = 1 - SSENLogN/SST;

  const quadraticModel = depth.map(d => [1, Math.pow(d, 2)]);
  const betaQuadratic = multiplyMatrices(
    invertMatrix(multiplyMatrices(transposeMatrix(quadraticModel), quadraticModel)),
    multiplyMatrices(transposeMatrix(quadraticModel), Y)
  );

  const YhatQuadratic = multiplyMatrices(quadraticModel, betaQuadratic);
  const SSEQuadratic = sum(Y.map((y, i) => Math.pow(y[0] - YhatQuadratic[i][0], 2)));
  const RsquaredQuadratic = 1 - SSEQuadratic/SST;

  const logModel = depth.map(d => [1, Math.log(Math.max(d, 2))]);
  const betaLog = multiplyMatrices(
    invertMatrix(multiplyMatrices(transposeMatrix(logModel), logModel)),
    multiplyMatrices(transposeMatrix(logModel), Y)
  );

  const YhatLog = multiplyMatrices(logModel, betaLog);
  const SSELog = sum(Y.map((y, i) => Math.pow(y[0] - YhatLog[i][0], 2)));
  const RsquaredLog = 1 - SSELog/SST;

  return {
    exponential: {
      name: 'exponential',
      formula: 'O(2^n)',
      description: 'Exponential',
      beta: beta.map(row => row[0]),
      rSquared: RsquaredExp
    },
    factorial: {
      name: 'factorial',
      formula: 'O(n!)',
      description: 'Factorial',
      beta: betaFactorial.map(row => row[0]),
      rSquared: RsquaredFactorial
    },
    linear: {
      name: 'linear',
      formula: 'O(n)',
      description: 'Linear',
      beta: betaLinear.map(row => row[0]),
      rSquared: RsquaredLinear
    },
    nlogn: {
      name: 'nlogn',
      formula: 'O(n log n)',
      description: 'Linearithmic',
      beta: betaNLogN.map(row => row[0]),
      rSquared: RsquaredNLogN
    },
    quadratic: {
      name: 'quadratic',
      formula: 'O(n²)',
      description: 'Quadratic',
      beta: betaQuadratic.map(row => row[0]),
      rSquared: RsquaredQuadratic
    },
    logarithmic: {
      name: 'logarithmic',
      formula: 'O(log n)',
      description: 'Logarithmic',
      beta: betaLog.map(row => row[0]),
      rSquared: RsquaredLog
    }
  };
}

interface ComplexityModel {
  name: string;
  formula: string;
  description: string;
  beta: number[];
  rSquared: number;
}

interface ComplexityModels {
  exponential: ComplexityModel;
  factorial: ComplexityModel;
  linear: ComplexityModel;
  nlogn: ComplexityModel;
  quadratic: ComplexityModel;
  logarithmic: ComplexityModel;
  [key: string]: ComplexityModel;
}

function findBestModel(models: ComplexityModels): string {
  let bestModel = 'unknown';
  let bestRSquared = 0;

  for (const [modelName, model] of Object.entries(models)) {
    if (model.rSquared > bestRSquared) {
      bestRSquared = model.rSquared;
      bestModel = modelName;
    }
  }

  return bestModel;
}

function getComplexityNotation(bestModel: string, models: ComplexityModels) {
  if (bestModel === 'unknown') {
    return {
      notation: 'Unknown',
      description: 'Could not determine complexity'
    };
  }

  return {
    notation: models[bestModel].formula,
    description: models[bestModel].description
  };
}

function transposeMatrix(matrix: number[][]) {
  const rows = matrix.length;
  const cols = matrix[0].length;
  const result = Array(cols).fill(0).map(() => Array(rows).fill(0));

  for (let i = 0; i < rows; i++) {
    for (let j = 0; j < cols; j++) {
      result[j][i] = matrix[i][j];
    }
  }

  return result;
}

function multiplyMatrices(a: number[][], b: number[][]) {
  const aRows = a.length;
  const aCols = a[0].length;
  const bRows = b.length;
  const bCols = b[0].length;

  if (aCols !== bRows) {
    throw new Error('Matrix dimensions do not match for multiplication');
  }

  const result = Array(aRows).fill(0).map(() => Array(bCols).fill(0));

  for (let i = 0; i < aRows; i++) {
    for (let j = 0; j < bCols; j++) {
      for (let k = 0; k < aCols; k++) {
        result[i][j] += a[i][k] * b[k][j];
      }
    }
  }

  return result;
}

function invertMatrix(matrix: number[][]) {
  if (matrix.length !== 2 || matrix[0].length !== 2) {
    throw new Error('Only 2x2 matrices are supported for inversion');
  }

  const det = matrix[0][0] * matrix[1][1] - matrix[0][1] * matrix[1][0];

  if (Math.abs(det) < 1e-10) {
    throw new Error('Matrix is singular and cannot be inverted');
  }

  return [
    [matrix[1][1] / det, -matrix[0][1] / det],
    [-matrix[1][0] / det, matrix[0][0] / det]
  ];
}

function mean(arr: number[]) {
  return sum(arr) / arr.length;
}

function sum(arr: number[]) {
  return arr.reduce((a, b) => a + b, 0);
}
