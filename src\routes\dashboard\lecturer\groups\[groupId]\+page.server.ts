import { error, fail, redirect } from '@sveltejs/kit';
import { and, eq, notInArray } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;
  const { groupId } = event.params;

  if (!user || user.role !== 'lecturer') {
    return redirect(303, '/auth/login');
  }

  if (!user.isApproved) {
    return redirect(303, '/pending');
  }

  const [group] = await db
    .select({
      id: table.group.id,
      name: table.group.name,
      description: table.group.description,
      createdBy: table.group.createdBy,
      createdAt: table.group.createdAt,
      updatedAt: table.group.updatedAt,
      isCreator: eq(table.group.createdBy, user.id),
      joinEnabled: table.group.joinEnabled,
      joinCode: table.group.joinCode,
      joinCodeExpiry: table.group.joinCodeExpiry
    })
    .from(table.group)
    .where(eq(table.group.id, groupId));

  if (!group) {
    throw error(404, 'Group not found');
  }

  if (!group.isCreator) {
    const [manager] = await db
      .select()
      .from(table.groupManager)
      .where(
        and(
          eq(table.groupManager.groupId, groupId),
          eq(table.groupManager.lecturerId, user.id)
        )
      );

    if (!manager) {
      throw error(403, 'You do not have permission to manage this group');
    }
  }

  const groupMembers = await db
    .select({
      id: table.groupMember.id,
      studentId: table.groupMember.studentId,
      addedAt: table.groupMember.addedAt,
      student: {
        username: table.user.username,
        name: table.user.name,
        email: table.user.email
      }
    })
    .from(table.groupMember)
    .innerJoin(
      table.user,
      eq(table.groupMember.studentId, table.user.id)
    )
    .where(eq(table.groupMember.groupId, groupId));

  const groupManagers = await db
    .select({
      id: table.groupManager.id,
      lecturerId: table.groupManager.lecturerId,
      addedAt: table.groupManager.addedAt,
      lecturer: {
        username: table.user.username,
        name: table.user.name,
        email: table.user.email
      }
    })
    .from(table.groupManager)
    .innerJoin(
      table.user,
      eq(table.groupManager.lecturerId, table.user.id)
    )
    .where(eq(table.groupManager.groupId, groupId));

  const lectureTimes = await db
    .select({
      id: table.lectureTime.id,
      weekday: table.lectureTime.weekday,
      startTime: table.lectureTime.startTime,
      endTime: table.lectureTime.endTime,
      location: table.lectureTime.location,
      createdAt: table.lectureTime.createdAt
    })
    .from(table.lectureTime)
    .where(eq(table.lectureTime.groupId, groupId))
    .orderBy(table.lectureTime.weekday, table.lectureTime.startTime);

  // Only show students from the same institution as the lecturer
  const allStudents = await db
    .select({
      id: table.user.id,
      username: table.user.username,
      name: table.user.name,
      email: table.user.email
    })
    .from(table.user)
    .where(
      and(
        eq(table.user.role, 'student'),
        user.organization ? eq(table.user.organization, user.organization) : undefined
      )
    );

  const studentIds = groupMembers.map(m => m.studentId);
  const availableStudents = allStudents.filter(s => !studentIds.includes(s.id));

  // Only show lecturers from the same institution as the current lecturer
  const allLecturers = await db
    .select({
      id: table.user.id,
      username: table.user.username,
      name: table.user.name,
      email: table.user.email
    })
    .from(table.user)
    .where(
      and(
        eq(table.user.role, 'lecturer'),
        user.organization ? eq(table.user.organization, user.organization) : undefined
      )
    );

  const managerIds = groupManagers.map(m => m.lecturerId);
  const availableLecturers = allLecturers.filter(l => !managerIds.includes(l.id) && l.id !== user.id);

  const projectGroups = await db
    .select({
      id: table.projectGroup.id,
      projectId: table.projectGroup.projectId,
      project: {
        name: table.project.name,
        description: table.project.description,
        isHidden: table.project.isHidden
      }
    })
    .from(table.projectGroup)
    .innerJoin(
      table.project,
      eq(table.projectGroup.projectId, table.project.id)
    )
    .where(eq(table.projectGroup.groupId, groupId));

  const createdProjects = await db
    .select({
      id: table.project.id,
      name: table.project.name,
      description: table.project.description
    })
    .from(table.project)
    .where(
      and(
        eq(table.project.createdBy, user.id),
        projectGroups.length > 0 ?
          notInArray(
            table.project.id,
            projectGroups.map(pg => pg.projectId)
          ) :
          undefined
      )
    );

  return {
    user,
    group,
    groupMembers,
    groupManagers,
    lectureTimes,
    availableStudents,
    availableLecturers,
    projectGroups,
    availableProjects: createdProjects
  };
};

export const actions: Actions = {
  generateJoinCode: async (event) => {
    const { user } = event.locals;
    const { groupId } = event.params;

    if (!user || user.role !== 'lecturer' || !user.isApproved) {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const canManage = await checkGroupAccess(user.id, groupId);
    if (!canManage) {
      return fail(403, { success: false, message: 'You do not have permission to manage this group' });
    }

    try {
      const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
      let code = '';
      for (let i = 0; i < 6; i++) {
        code += characters.charAt(Math.floor(Math.random() * characters.length));
      }

      const expiryTime = new Date();
      expiryTime.setMinutes(expiryTime.getMinutes() + 15);

      await db
        .update(table.group)
        .set({
          joinEnabled: true,
          joinCode: code,
          joinCodeExpiry: expiryTime
        })
        .where(eq(table.group.id, groupId));

      return {
        success: true,
        message: 'Join code generated successfully',
        joinCode: code,
        expiryTime
      };
    } catch (error) {
      console.error('Error generating join code:', error);
      return fail(500, { success: false, message: 'An error occurred while generating the join code' });
    }
  },

  disableJoinCode: async (event) => {
    const { user } = event.locals;
    const { groupId } = event.params;

    if (!user || user.role !== 'lecturer' || !user.isApproved) {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const canManage = await checkGroupAccess(user.id, groupId);
    if (!canManage) {
      return fail(403, { success: false, message: 'You do not have permission to manage this group' });
    }

    try {
      await db
        .update(table.group)
        .set({
          joinEnabled: false,
          joinCode: null,
          joinCodeExpiry: null
        })
        .where(eq(table.group.id, groupId));

      return {
        success: true,
        message: 'Join code disabled successfully'
      };
    } catch (error) {
      console.error('Error disabling join code:', error);
      return fail(500, { success: false, message: 'An error occurred while disabling the join code' });
    }
  },


  addStudents: async (event) => {
    const { user } = event.locals;
    const { groupId } = event.params;

    if (!user || user.role !== 'lecturer' || !user.isApproved) {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const canManage = await checkGroupAccess(user.id, groupId);
    if (!canManage) {
      return fail(403, { success: false, message: 'You do not have permission to manage this group' });
    }

    const formData = await event.request.formData();
    const studentIds = formData.getAll('students[]').map(s => s.toString());

    if (studentIds.length === 0) {
      return fail(400, { success: false, message: 'Please select at least one student' });
    }

    try {
      await Promise.all(
        studentIds.map(async (studentId) => {
          await db.insert(table.groupMember).values({
            id: crypto.randomUUID(),
            groupId,
            studentId,
            addedBy: user.id,
            addedAt: new Date()
          });
        })
      );

      return {
        success: true,
        message: `Added ${studentIds.length} student${studentIds.length === 1 ? '' : 's'} to the group`
      };
    } catch (error) {
      console.error('Error adding students to group:', error);
      return fail(500, { success: false, message: 'An error occurred while adding students to the group' });
    }
  },

  removeStudent: async (event) => {
    const { user } = event.locals;
    const { groupId } = event.params;

    if (!user || user.role !== 'lecturer' || !user.isApproved) {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const canManage = await checkGroupAccess(user.id, groupId);
    if (!canManage) {
      return fail(403, { success: false, message: 'You do not have permission to manage this group' });
    }

    const formData = await event.request.formData();
    const membershipId = formData.get('membershipId')?.toString();

    if (!membershipId) {
      return fail(400, { success: false, message: 'Invalid request' });
    }

    try {
      await db
        .delete(table.groupMember)
        .where(
          and(
            eq(table.groupMember.id, membershipId),
            eq(table.groupMember.groupId, groupId)
          )
        );

      return {
        success: true,
        message: 'Student removed from the group'
      };
    } catch (error) {
      console.error('Error removing student from group:', error);
      return fail(500, { success: false, message: 'An error occurred while removing the student' });
    }
  },

  addManagers: async (event) => {
    const { user } = event.locals;
    const { groupId } = event.params;

    if (!user || user.role !== 'lecturer' || !user.isApproved) {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const canManage = await checkGroupAccess(user.id, groupId);
    if (!canManage) {
      return fail(403, { success: false, message: 'You do not have permission to manage this group' });
    }

    const formData = await event.request.formData();
    const lecturerIds = formData.getAll('lecturers[]').map(l => l.toString());

    if (lecturerIds.length === 0) {
      return fail(400, { success: false, message: 'Please select at least one lecturer' });
    }

    try {
      await Promise.all(
        lecturerIds.map(async (lecturerId) => {
          await db.insert(table.groupManager).values({
            id: crypto.randomUUID(),
            groupId,
            lecturerId,
            addedBy: user.id,
            addedAt: new Date()
          });
        })
      );

      return {
        success: true,
        message: `Added ${lecturerIds.length} manager${lecturerIds.length === 1 ? '' : 's'} to the group`
      };
    } catch (error) {
      console.error('Error adding managers to group:', error);
      return fail(500, { success: false, message: 'An error occurred while adding managers to the group' });
    }
  },

  removeManager: async (event) => {
    const { user } = event.locals;
    const { groupId } = event.params;

    if (!user || user.role !== 'lecturer' || !user.isApproved) {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const canManage = await checkGroupAccess(user.id, groupId);
    if (!canManage) {
      return fail(403, { success: false, message: 'You do not have permission to manage this group' });
    }

    const formData = await event.request.formData();
    const managershipId = formData.get('managershipId')?.toString();

    if (!managershipId) {
      return fail(400, { success: false, message: 'Invalid request' });
    }

    try {
      await db
        .delete(table.groupManager)
        .where(
          and(
            eq(table.groupManager.id, managershipId),
            eq(table.groupManager.groupId, groupId)
          )
        );

      return {
        success: true,
        message: 'Manager removed from the group'
      };
    } catch (error) {
      console.error('Error removing manager from group:', error);
      return fail(500, { success: false, message: 'An error occurred while removing the manager' });
    }
  },

  addLectureTime: async (event) => {
    const { user } = event.locals;
    const { groupId } = event.params;

    if (!user || user.role !== 'lecturer' || !user.isApproved) {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const canManage = await checkGroupAccess(user.id, groupId);
    if (!canManage) {
      return fail(403, { success: false, message: 'You do not have permission to manage this group' });
    }

    const formData = await event.request.formData();
    const weekday = formData.get('weekday')?.toString();
    const startTime = formData.get('startTime')?.toString();
    const endTime = formData.get('endTime')?.toString();
    const location = formData.get('location')?.toString() || null;

    if (!weekday || !startTime || !endTime) {
      return fail(400, { success: false, message: 'Please provide all required lecture time details' });
    }

    try {
      await db.insert(table.lectureTime).values({
        id: crypto.randomUUID(),
        groupId,
        weekday: weekday as any, // hacky fixas
        startTime,
        endTime,
        location,
        createdBy: user.id,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      return {
        success: true,
        message: 'Lecture time added successfully'
      };
    } catch (error) {
      console.error('Error adding lecture time:', error);
      return fail(500, { success: false, message: 'An error occurred while adding the lecture time' });
    }
  },

  removeLectureTime: async (event) => {
    const { user } = event.locals;
    const { groupId } = event.params;

    if (!user || user.role !== 'lecturer' || !user.isApproved) {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const canManage = await checkGroupAccess(user.id, groupId);
    if (!canManage) {
      return fail(403, { success: false, message: 'You do not have permission to manage this group' });
    }

    const formData = await event.request.formData();
    const lectureTimeId = formData.get('lectureTimeId')?.toString();

    if (!lectureTimeId) {
      return fail(400, { success: false, message: 'Invalid request' });
    }

    try {
      await db
        .delete(table.lectureTime)
        .where(
          and(
            eq(table.lectureTime.id, lectureTimeId),
            eq(table.lectureTime.groupId, groupId)
          )
        );

      return {
        success: true,
        message: 'Lecture time removed successfully'
      };
    } catch (error) {
      console.error('Error removing lecture time:', error);
      return fail(500, { success: false, message: 'An error occurred while removing the lecture time' });
    }
  },

  addProject: async (event) => {
    const { user } = event.locals;
    const { groupId } = event.params;

    if (!user || user.role !== 'lecturer' || !user.isApproved) {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    // Check if user can manage this group
    const canManage = await checkGroupAccess(user.id, groupId);
    if (!canManage) {
      return fail(403, { success: false, message: 'You do not have permission to manage this group' });
    }

    const formData = await event.request.formData();
    const projectId = formData.get('projectId')?.toString();

    if (!projectId) {
      return fail(400, { success: false, message: 'Please select a project' });
    }

    try {
      const [project] = await db
        .select()
        .from(table.project)
        .where(
          and(
            eq(table.project.id, projectId),
            eq(table.project.createdBy, user.id)
          )
        );

      if (!project) {
        return fail(403, { success: false, message: 'You do not have permission to associate this project' });
      }

      const [existingAssociation] = await db
        .select()
        .from(table.projectGroup)
        .where(
          and(
            eq(table.projectGroup.projectId, projectId),
            eq(table.projectGroup.groupId, groupId)
          )
        );

      if (existingAssociation) {
        return fail(400, { success: false, message: 'This project is already associated with the group' });
      }

      await db.insert(table.projectGroup).values({
        id: crypto.randomUUID(),
        projectId,
        groupId,
        createdBy: user.id,
        createdAt: new Date()
      });

      return {
        success: true,
        message: 'Project associated with the group successfully'
      };
    } catch (error) {
      console.error('Error associating project with group:', error);
      return fail(500, { success: false, message: 'An error occurred while associating the project' });
    }
  },

  removeProject: async (event) => {
    const { user } = event.locals;
    const { groupId } = event.params;

    if (!user || user.role !== 'lecturer' || !user.isApproved) {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const canManage = await checkGroupAccess(user.id, groupId);
    if (!canManage) {
      return fail(403, { success: false, message: 'You do not have permission to manage this group' });
    }

    const formData = await event.request.formData();
    const associationId = formData.get('associationId')?.toString();

    if (!associationId) {
      return fail(400, { success: false, message: 'Invalid request' });
    }

    try {
      await db
        .delete(table.projectGroup)
        .where(
          and(
            eq(table.projectGroup.id, associationId),
            eq(table.projectGroup.groupId, groupId)
          )
        );

      return {
        success: true,
        message: 'Project disassociated from the group successfully'
      };
    } catch (error) {
      console.error('Error removing project association:', error);
      return fail(500, { success: false, message: 'An error occurred while removing the project association' });
    }
  },

  updateGroup: async (event) => {
    const { user } = event.locals;
    const { groupId } = event.params;

    if (!user || user.role !== 'lecturer' || !user.isApproved) {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const canManage = await checkGroupAccess(user.id, groupId);
    if (!canManage) {
      return fail(403, { success: false, message: 'You do not have permission to manage this group' });
    }

    const formData = await event.request.formData();
    const name = formData.get('name')?.toString().trim();
    const description = formData.get('description')?.toString().trim() || null;

    if (!name) {
      return fail(400, { success: false, message: 'Group name is required' });
    }

    try {
      await db
        .update(table.group)
        .set({
          name,
          description,
          updatedAt: new Date()
        })
        .where(eq(table.group.id, groupId));

      return {
        success: true,
        message: 'Group updated successfully'
      };
    } catch (error) {
      console.error('Error updating group:', error);
      return fail(500, { success: false, message: 'An error occurred while updating the group' });
    }
  }
};

async function checkGroupAccess(userId: string, groupId: string): Promise<boolean> {
  const [group] = await db
    .select()
    .from(table.group)
    .where(
      and(
        eq(table.group.id, groupId),
        eq(table.group.createdBy, userId)
      )
    );

  if (group) {
    return true;
  }

  const [manager] = await db
    .select()
    .from(table.groupManager)
    .where(
      and(
        eq(table.groupManager.groupId, groupId),
        eq(table.groupManager.lecturerId, userId)
      )
    );

  return !!manager;
}
