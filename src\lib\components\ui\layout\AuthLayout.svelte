<script lang="ts">
  import { BackButton, LanguageSelector } from '$lib/components/ui';

  const {
    title = '',
    subtitle = '',
    backHref = '/',
    showLanguageSelector = true,
    class: className = '',
    children
  } = $props<{
    title: string,
    subtitle?: string,
    backHref?: string,
    showLanguageSelector?: boolean,
    class?: string,
    children: any
  }>();
  const containerClass = $derived(`
    max-w-lg w-full space-y-10 p-10
    bg-white dark:bg-gray-800
    rounded-xl shadow-lg
    border border-gray-200 dark:border-gray-700
    ${className}
  `);
</script>

<div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-16 px-6 sm:px-8 lg:px-10 relative">
  <div class={containerClass}>
    <div class="flex justify-between items-center w-full mb-10">
      <BackButton href={backHref} />

      {#if showLanguageSelector}
        <LanguageSelector />
      {/if}
    </div>

    <div>
      <h2 class="mt-6 text-center text-4xl font-extrabold text-gray-900 dark:text-white">{title}</h2>
      {#if subtitle}
        <p class="mt-2 text-center text-base text-gray-600 dark:text-gray-400">
          {subtitle}
        </p>
      {/if}
    </div>

    {@render children()}
  </div>
</div>
