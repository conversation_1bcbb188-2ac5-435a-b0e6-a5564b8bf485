<script lang="ts">
  const {
    title = '',
    subtitle = '',
    class: className = '',
    action = null,
    children
  } = $props<{
    title: string,
    subtitle?: string,
    class?: string,
    action?: any,
    children?: any
  }>();
  const headerClass = $derived(`
    flex flex-col sm:flex-row sm:items-center sm:justify-between
    mb-6 ${className}
  `);
</script>

<div class={headerClass}>
  <div>
    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{title}</h1>
    {#if subtitle}
      <p class="text-gray-500 dark:text-gray-400 mt-1">{subtitle}</p>
    {/if}
    {#if children}
      {@render children()}
    {/if}
  </div>

  {#if action}
    <div class="mt-4 sm:mt-0">
      {action}
    </div>
  {/if}
</div>
