import { error, fail, redirect } from '@sveltejs/kit';
import { eq, and } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;
  const { organizationId } = event.params;

  if (!user || user.role !== 'developer') {
    return redirect(303, '/auth/login');
  }

  const organizations = await db
    .select()
    .from(table.organization)
    .where(eq(table.organization.id, organizationId));

  const organization = organizations[0];

  if (!organization) {
    throw error(404, 'Organization not found');
  }

  const admins = await db
    .select({
      id: table.user.id,
      username: table.user.username,
      email: table.user.email,
      isActive: table.user.isActive
    })
    .from(table.user)
    .where(
      and(
        eq(table.user.role, 'admin'),
        eq(table.user.organization, organizationId)
      )
    )
    .orderBy(table.user.username);

  let createdByUser = null;
  if (organization.createdBy) {
    const users = await db
      .select({
        username: table.user.username,
        email: table.user.email
      })
      .from(table.user)
      .where(eq(table.user.id, organization.createdBy));

    if (users.length > 0) {
      createdByUser = users[0];
    }
  }

  return {
    user,
    organization,
    admins,
    createdByUser
  };
};

export const actions: Actions = {
  updateOrganization: async (event) => {
    const { user } = event.locals;
    const { organizationId } = event.params;

    if (!user || user.role !== 'developer') {
      return fail(403, { message: 'Unauthorized' });
    }

    const formData = await event.request.formData();
    const name = formData.get('name')?.toString();
    const description = formData.get('description')?.toString() || null;
    const isActive = formData.has('isActive');

    if (!name) {
      return fail(400, { message: 'Name is required' });
    }

    try {
      const [organization] = await db
        .select({ id: table.organization.id })
        .from(table.organization)
        .where(eq(table.organization.id, organizationId));

      if (!organization) {
        return fail(404, { message: 'Organization not found' });
      }

      await db
        .update(table.organization)
        .set({
          name,
          description,
          isActive
        })
        .where(eq(table.organization.id, organizationId));

      return {
        success: true,
        message: 'Organization updated successfully'
      };
    } catch (error) {
      console.error('Organization update error:', error);
      return fail(500, { message: 'An error occurred while updating the organization' });
    }
  }
};
