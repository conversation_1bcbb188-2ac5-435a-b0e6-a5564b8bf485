import { error, fail, redirect } from '@sveltejs/kit';
import { and, eq, desc, inArray, sql } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { getAbsoluteFilePath } from '$lib/server/storage/fileStorage';
import fs from 'fs';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;
  const { projectId } = event.params;

  if (!user || user.role !== 'lecturer') {
    return redirect(303, '/auth/login');
  }

  if (!user.isApproved) {
    return redirect(303, '/pending');
  }

  const [project] = await db
    .select({
      id: table.project.id,
      name: table.project.name,
      description: table.project.description,
      maxAttempts: table.project.maxAttempts,
      deadline: table.project.deadline,
      isHidden: table.project.isHidden,
      createdAt: table.project.createdAt,
      updatedAt: table.project.updatedAt
    })
    .from(table.project)
    .where(
      and(
        eq(table.project.id, projectId),
        eq(table.project.createdBy, user.id)
      )
    );

  if (!project) {
    throw error(404, 'Project not found');
  }

  const submissions = await db
    .select({
      id: table.submission.id,
      studentId: table.submission.studentId,
      studentName: table.user.username,
      filePath: table.submission.filePath,
      fileSize: table.submission.fileSize,
      originalFilename: table.submission.originalFilename,
      submittedAt: table.submission.submittedAt,
      hasComplexityAnalysis: table.submission.hasComplexityAnalysis
    })
    .from(table.submission)
    .innerJoin(
      table.user,
      eq(table.submission.studentId, table.user.id)
    )
    .where(eq(table.submission.projectId, projectId))
    .orderBy(desc(table.submission.submittedAt));

  // Get all project groups
  const projectGroups = await db
    .select({
      id: table.projectGroup.id,
      groupId: table.projectGroup.groupId,
      group: {
        name: table.group.name,
        description: table.group.description
      }
    })
    .from(table.projectGroup)
    .innerJoin(
      table.group,
      eq(table.projectGroup.groupId, table.group.id)
    )
    .where(eq(table.projectGroup.projectId, projectId));

  // Get groups created by the user
  const createdGroups = await db
    .select({
      id: table.group.id,
      name: table.group.name,
      description: table.group.description,
      isCreated: sql`true`,
      isManaged: sql`false`
    })
    .from(table.group)
    .where(eq(table.group.createdBy, user.id));

  // Get groups where the user is a manager
  const managedGroups = await db
    .select({
      id: table.group.id,
      name: table.group.name,
      description: table.group.description,
      isCreated: sql`false`,
      isManaged: sql`true`
    })
    .from(table.group)
    .innerJoin(
      table.groupManager,
      eq(table.group.id, table.groupManager.groupId)
    )
    .where(eq(table.groupManager.lecturerId, user.id));

  // Combine the results, removing duplicates
  const groupMap = new Map();

  // Add created groups first
  createdGroups.forEach(group => {
    groupMap.set(group.id, group);
  });

  // Add managed groups if not already added
  managedGroups.forEach(group => {
    if (!groupMap.has(group.id)) {
      groupMap.set(group.id, group);
    } else {
      // If the group is both created and managed, update isManaged
      const existingGroup = groupMap.get(group.id);
      existingGroup.isManaged = true;
    }
  });

  const accessibleGroups = Array.from(groupMap.values());

  // Filter out groups that are assigned to project
  const groupIds = new Set(projectGroups.map(pg => pg.groupId));
  const availableGroups = accessibleGroups.filter(group => !groupIds.has(group.id));

  // Combine all groups for student lookup
  const allGroups = [
    ...projectGroups.map(pg => ({ id: pg.groupId, name: pg.group.name })),
    ...availableGroups
  ];

  // Get all group IDs
  const allGroupIds = allGroups.map(group => group.id);

  // Get all students from all relevant groups in a single query
  const allGroupStudents = await db
    .select({
      groupId: table.groupMember.groupId,
      student: {
        id: table.user.id,
        username: table.user.username,
        name: table.user.name,
        email: table.user.email,
        membershipId: table.groupMember.id
      }
    })
    .from(table.groupMember)
    .innerJoin(
      table.user,
      eq(table.groupMember.studentId, table.user.id)
    )
    .where(inArray(table.groupMember.groupId, allGroupIds));

  // Organize students by group
  const groupStudentsMap = new Map();
  allGroupStudents.forEach(item => {
    if (!groupStudentsMap.has(item.groupId)) {
      groupStudentsMap.set(item.groupId, []);
    }
    groupStudentsMap.get(item.groupId).push(item.student);
  });

  // Create the final groupStudents array
  const groupStudents = allGroups.map(group => ({
    groupId: group.id,
    groupName: group.name,
    students: groupStudentsMap.get(group.id) || []
  }));

  // Get assigned students
  const assignedStudents = await db
    .select({
      id: table.projectStudent.id,
      studentId: table.projectStudent.studentId,
      student: {
        id: table.user.id,
        username: table.user.username,
        name: table.user.name,
        email: table.user.email
      },
      createdAt: table.projectStudent.createdAt
    })
    .from(table.projectStudent)
    .innerJoin(
      table.user,
      eq(table.projectStudent.studentId, table.user.id)
    )
    .where(eq(table.projectStudent.projectId, projectId));

  return {
    user,
    project,
    submissions,
    projectGroups,
    availableGroups,
    groupStudents,
    assignedStudents
  };
};

export const actions: Actions = {
  deleteProject: async (event) => {
    const { user } = event.locals;
    const { projectId } = event.params;

    if (!user || user.role !== 'lecturer' || !user.isApproved) {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const [project] = await db
      .select()
      .from(table.project)
      .where(
        and(
          eq(table.project.id, projectId),
          eq(table.project.createdBy, user.id)
        )
      );

    if (!project) {
      return fail(404, { success: false, message: 'Project not found' });
    }

    // This part is complcated to understand, so to the one reading this,
    // I made it easier to understand with appropriate comments ;) Wish you a nice day!
    try {
      // 1. Get all submissions for this project
      const submissions = await db
        .select({
          id: table.submission.id,
          filePath: table.submission.filePath
        })
        .from(table.submission)
        .where(eq(table.submission.projectId, projectId));

      console.log(`Found ${submissions.length} submissions to delete for project ${projectId}`);

      // 2. Delete associated data for each submission
      for (const submission of submissions) {
        // Delete CS files
        await db
          .delete(table.csFile)
          .where(eq(table.csFile.submissionId, submission.id));

        // Delete complexity analysis if exists
        await db
          .delete(table.complexityAnalysis)
          .where(eq(table.complexityAnalysis.submissionId, submission.id));

        // Delete submission attempts
        await db
          .delete(table.submissionAttempt)
          .where(eq(table.submissionAttempt.submissionId, submission.id));

        // Delete the physical file
        try {
          const absolutePath = getAbsoluteFilePath(submission.filePath);
          if (fs.existsSync(absolutePath)) {
            fs.unlinkSync(absolutePath);
            console.log(`Deleted file: ${absolutePath}`);
          }
        } catch (fileError) {
          console.error(`Error deleting file for submission ${submission.id}:`, fileError);
        }
      }

      // 3. Delete all submissions from database
      if (submissions.length > 0) {
        await db
          .delete(table.submission)
          .where(eq(table.submission.projectId, projectId));

        console.log(`Deleted ${submissions.length} submissions from database`);
      }

      // 4. Delete all project-group associations
      await db
        .delete(table.projectGroup)
        .where(eq(table.projectGroup.projectId, projectId));

      // 5. Delete al project and student associations
      await db
        .delete(table.projectStudent)
        .where(eq(table.projectStudent.projectId, projectId));

      // 6. Delete the project
      await db
        .delete(table.project)
        .where(eq(table.project.id, projectId));

      return {
        success: true,
        message: 'Project and all associated data deleted successfully',
        redirect: '/dashboard/lecturer/projects'
      };
    } catch (error) {
      console.error('Error deleting project:', error);
      return fail(500, { success: false, message: 'An error occurred while deleting the project' });
    }
  },

  updateProject: async (event) => {
    const { user } = event.locals;
    const { projectId } = event.params;

    if (!user || user.role !== 'lecturer' || !user.isApproved) {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const [project] = await db
      .select()
      .from(table.project)
      .where(
        and(
          eq(table.project.id, projectId),
          eq(table.project.createdBy, user.id)
        )
      );

    if (!project) {
      return fail(404, { success: false, message: 'Project not found' });
    }

    const formData = await event.request.formData();
    const name = formData.get('name')?.toString().trim();
    const description = formData.get('description')?.toString().trim() || null;
    const maxAttemptsStr = formData.get('maxAttempts')?.toString();
    const maxAttempts = maxAttemptsStr ? parseInt(maxAttemptsStr) : 0;
    const deadlineStr = formData.get('deadline')?.toString();
    const deadline = deadlineStr && deadlineStr.trim() !== '' ? new Date(deadlineStr) : null;
    const selectedGroups = formData.getAll('groups[]').map(g => g.toString());
    const selectedStudents = formData.getAll('students[]').map(s => s.toString());

    if (!name) {
      return fail(400, { success: false, message: 'Project name is required' });
    }

    try {
      await db
        .update(table.project)
        .set({
          name,
          description,
          maxAttempts,
          deadline,
          updatedAt: new Date()
        })
        .where(eq(table.project.id, projectId));

      const currentGroups = await db
        .select({
          id: table.projectGroup.id,
          groupId: table.projectGroup.groupId
        })
        .from(table.projectGroup)
        .where(eq(table.projectGroup.projectId, projectId));

      const currentGroupIds = currentGroups.map(g => g.groupId);
      const groupsToRemove = currentGroupIds.filter(id => !selectedGroups.includes(id));

      if (groupsToRemove.length > 0) {
        await db
          .delete(table.projectGroup)
          .where(
            and(
              eq(table.projectGroup.projectId, projectId),
              inArray(table.projectGroup.groupId, groupsToRemove)
            )
          );
      }

      const groupsToAdd = selectedGroups.filter(id => !currentGroupIds.includes(id));

      if (groupsToAdd.length > 0) {
        await Promise.all(
          groupsToAdd.map(async (groupIdToAdd) => {
            await db.insert(table.projectGroup).values({
              id: crypto.randomUUID(),
              projectId,
              groupId: groupIdToAdd,
              createdBy: user.id,
              createdAt: new Date()
            });
          })
        );
      }

      await db
        .delete(table.projectStudent)
        .where(eq(table.projectStudent.projectId, projectId));

      if (selectedStudents.length > 0) {
        await Promise.all(
          selectedStudents.map(async (studentId) => {
            await db.insert(table.projectStudent).values({
              id: crypto.randomUUID(),
              projectId,
              studentId,
              createdBy: user.id,
              createdAt: new Date()
            });
          })
        );
      }

      return {
        success: true,
        message: 'Project updated successfully'
      };
    } catch (error) {
      console.error('Error updating project:', error);
      return fail(500, { success: false, message: 'An error occurred while updating the project' });
    }
  },

  addGroup: async (event) => {
    const { user } = event.locals;
    const { projectId } = event.params;

    if (!user || user.role !== 'lecturer' || !user.isApproved) {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const [project] = await db
      .select()
      .from(table.project)
      .where(
        and(
          eq(table.project.id, projectId),
          eq(table.project.createdBy, user.id)
        )
      );

    if (!project) {
      return fail(404, { success: false, message: 'Project not found' });
    }

    const formData = await event.request.formData();
    const groupId = formData.get('groupId')?.toString();

    if (!groupId) {
      return fail(400, { success: false, message: 'Group ID is required' });
    }

    try {
      const [existingAssociation] = await db
        .select()
        .from(table.projectGroup)
        .where(
          and(
            eq(table.projectGroup.projectId, projectId),
            eq(table.projectGroup.groupId, groupId)
          )
        );

      if (existingAssociation) {
        return fail(400, { success: false, message: 'This group is already associated with the project' });
      }

      await db.insert(table.projectGroup).values({
        id: crypto.randomUUID(),
        projectId,
        groupId,
        createdBy: user.id,
        createdAt: new Date()
      });

      return {
        success: true,
        message: 'Group added to project successfully'
      };
    } catch (error) {
      console.error('Error adding group to project:', error);
      return fail(500, { success: false, message: 'An error occurred while adding the group to the project' });
    }
  },

  removeGroup: async (event) => {
    const { user } = event.locals;
    const { projectId } = event.params;

    if (!user || user.role !== 'lecturer' || !user.isApproved) {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const [project] = await db
      .select()
      .from(table.project)
      .where(
        and(
          eq(table.project.id, projectId),
          eq(table.project.createdBy, user.id)
        )
      );

    if (!project) {
      return fail(404, { success: false, message: 'Project not found' });
    }

    const formData = await event.request.formData();
    const associationId = formData.get('associationId')?.toString();

    if (!associationId) {
      return fail(400, { success: false, message: 'Association ID is required' });
    }

    try {
      await db
        .delete(table.projectGroup)
        .where(
          and(
            eq(table.projectGroup.id, associationId),
            eq(table.projectGroup.projectId, projectId)
          )
        );

      return {
        success: true,
        message: 'Group removed from project successfully'
      };
    } catch (error) {
      console.error('Error removing group from project:', error);
      return fail(500, { success: false, message: 'An error occurred while removing the group from the project' });
    }
  },

  toggleVisibility: async (event) => {
    const { user } = event.locals;
    const { projectId } = event.params;

    if (!user || user.role !== 'lecturer' || !user.isApproved) {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const [project] = await db
      .select({
        id: table.project.id,
        isHidden: table.project.isHidden
      })
      .from(table.project)
      .where(
        and(
          eq(table.project.id, projectId),
          eq(table.project.createdBy, user.id)
        )
      );

    if (!project) {
      return fail(404, { success: false, message: 'Project not found' });
    }

    try {
      const newVisibility = !project.isHidden;

      await db
        .update(table.project)
        .set({
          isHidden: newVisibility,
          updatedAt: new Date()
        })
        .where(eq(table.project.id, projectId));

      return {
        success: true,
        message: `Project is now ${newVisibility ? 'hidden from' : 'visible to'} students`,
        isHidden: newVisibility
      };
    } catch (error) {
      console.error('Error toggling project visibility:', error);
      return fail(500, { success: false, message: 'An error occurred while updating project visibility' });
    }
  }
};