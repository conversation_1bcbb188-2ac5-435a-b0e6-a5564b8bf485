import { error, redirect, fail } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { eq } from 'drizzle-orm';
import * as table from '$lib/server/db/schema';
import type { PageServerLoad, Actions } from './$types';

export const load: PageServerLoad = async ({ params, locals }) => {
  if (!locals.user || locals.user.role !== 'admin') {
    throw redirect(302, '/auth/login');
  }

  try {
    const [userData] = await db
      .select()
      .from(table.user)
      .where(eq(table.user.id, params.userId));

    if (!userData) {
      throw error(404, 'User not found');
    }

    let organization = null;
    if (userData.organization) {
      const [orgData] = await db
        .select()
        .from(table.organization)
        .where(eq(table.organization.id, userData.organization));

      organization = orgData;
    }

    return {
      user: userData,
      organization
    };
  } catch (e) {
    console.error('Error fetching user:', e);
    throw error(500, 'Failed to load user data');
  }
};

export const actions: Actions = {
  updateUser: async ({ request, params }) => {
    const formData = await request.formData();
    const email = formData.get('email')?.toString();
    const role = formData.get('role')?.toString() as 'student' | 'lecturer' | 'admin' | 'developer';
    const isActive = formData.has('isActive');
    const isApproved = formData.has('isApproved');

    if (!email) {
      return fail(400, { success: false, message: 'Email is required' });
    }

    try {
      await db.update(table.user)
        .set({
          email,
          role,
          isActive,
          isApproved
        })
        .where(eq(table.user.id, params.userId));

      return { success: true, message: 'User updated successfully' };
    } catch (e) {
      console.error('Error updating user:', e);
      return fail(500, { success: false, message: 'Failed to update user' });
    }
  },

};