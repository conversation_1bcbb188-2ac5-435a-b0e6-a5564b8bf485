import { redirect, fail } from '@sveltejs/kit';
import { eq, and, inArray } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { saveFile } from '$lib/server/storage/fileStorage';
import crypto from 'crypto';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;

  if (!user) {
    throw redirect(302, '/login');
  }

  if (user.role !== 'student') {
    throw redirect(302, '/dashboard');
  }

  const assignedProjects = await db
    .select({
      id: table.projectStudent.projectId,
      project: {
        id: table.project.id,
        name: table.project.name,
        description: table.project.description,
        isHidden: table.project.isHidden,
        deadline: table.project.deadline,
        maxAttempts: table.project.maxAttempts
      }
    })
    .from(table.projectStudent)
    .innerJoin(table.project, eq(table.projectStudent.projectId, table.project.id))
    .where(eq(table.projectStudent.studentId, user.id));

  const studentGroups = await db
    .select({
      groupId: table.groupMember.groupId
    })
    .from(table.groupMember)
    .where(eq(table.groupMember.studentId, user.id));

  const studentGroupIds = studentGroups.map(g => g.groupId);

  let groupProjects: Array<{
    id: string;
    name: string;
    description: string | null;
    isHidden: boolean;
    deadline: Date | null;
    maxAttempts: number | null;
  }> = [];
  if (studentGroupIds.length > 0) {
    const projectGroups = await db
      .select({
        projectId: table.projectGroup.projectId,
        groupId: table.projectGroup.groupId
      })
      .from(table.projectGroup)
      .where(inArray(table.projectGroup.groupId, studentGroupIds));

    const projectsToInclude = [];

    for (const pg of projectGroups) {
      const assignedStudents = await db
        .select({
          studentId: table.projectStudent.studentId
        })
        .from(table.projectStudent)
        .where(eq(table.projectStudent.projectId, pg.projectId));

      const assignedStudentIds = new Set(assignedStudents.map(s => s.studentId));

      if (assignedStudentIds.has(user.id)) {
        projectsToInclude.push(pg.projectId);
      }
    }

    if (projectsToInclude.length > 0) {
      groupProjects = await db
        .select({
          id: table.project.id,
          name: table.project.name,
          description: table.project.description,
          isHidden: table.project.isHidden,
          deadline: table.project.deadline,
          maxAttempts: table.project.maxAttempts
        })
        .from(table.project)
        .where(inArray(table.project.id, projectsToInclude));
    }
  }

  const visibleAssignedProjects = assignedProjects.filter(p => !p.project.isHidden);
  const visibleGroupProjects = groupProjects.filter(p => !p.isHidden);

  const uniqueProjects = new Map();

  // Add assigned projects first
  visibleAssignedProjects.forEach(p => {
    if (!uniqueProjects.has(p.project.id)) {
      uniqueProjects.set(p.project.id, p.project);
    }
  });

  // Add group projects if not already added
  visibleGroupProjects.forEach(p => {
    if (!uniqueProjects.has(p.id)) {
      uniqueProjects.set(p.id, p);
    }
  });

  // Convert Map values to array
  const allProjects = Array.from(uniqueProjects.values());

  return {
    user,
    projects: allProjects
  };
};

export const actions: Actions = {
  submitFile: async (event) => {
    const { user } = event.locals;

    if (!user || user.role !== 'student') {
      return fail(403, { message: 'Unauthorized' });
    }

    try {
      const formData = await event.request.formData();
      const projectId = formData.get('projectId')?.toString();
      const file = formData.get('file') as File;

      if (!projectId) {
        return fail(400, { message: 'Project ID is required' });
      }

      if (!file || file.size === 0) {
        return fail(400, { message: 'Please select a file to upload' });
      }

      const [project] = await db
        .select()
        .from(table.project)
        .where(eq(table.project.id, projectId));

      if (!project) {
        return fail(404, { message: 'Project not found' });
      }

      const [projectStudent] = await db
        .select()
        .from(table.projectStudent)
        .where(
          and(
            eq(table.projectStudent.projectId, projectId),
            eq(table.projectStudent.studentId, user.id)
          )
        );

      if (!projectStudent) {
        const studentGroups = await db
          .select({
            groupId: table.groupMember.groupId
          })
          .from(table.groupMember)
          .where(eq(table.groupMember.studentId, user.id));

        const studentGroupIds = studentGroups.map(g => g.groupId);

        if (studentGroupIds.length === 0) {
          return fail(403, { message: 'You are not assigned to this project' });
        }

        const [projectGroup] = await db
          .select()
          .from(table.projectGroup)
          .where(
            and(
              eq(table.projectGroup.projectId, projectId),
              inArray(table.projectGroup.groupId, studentGroupIds)
            )
          );

        if (!projectGroup) {
          return fail(403, { message: 'You are not assigned to this project' });
        }

        // Check if this specific student is assigned to the project
        const [assignedStudent] = await db
          .select({
            studentId: table.projectStudent.studentId
          })
          .from(table.projectStudent)
          .where(
            and(
              eq(table.projectStudent.projectId, projectId),
              eq(table.projectStudent.studentId, user.id)
            )
          );

        if (!assignedStudent) {
          return fail(403, { message: 'You are not assigned to this project' });
        }
      }

      const filePath = await saveFile(user.id, file, file.name);

      const submissionId = crypto.randomUUID();
      await db.insert(table.submission).values({
        id: submissionId,
        projectId,
        studentId: user.id,
        filePath,
        fileSize: file.size,
        originalFilename: file.name
      });

      // Record the submission attempt
      await db.insert(table.submissionAttempt).values({
        id: crypto.randomUUID(),
        projectId,
        studentId: user.id,
        submissionId,
        attemptedAt: new Date()
      });

      return {
        success: true,
        message: 'Your submission has been received successfully',
        submitted: true,
        submissionId
      };
    } catch (error: any) {
      console.error('Submission error:', error);
      const errorMessage = error.message ? `${error.message}` : 'Unknown error';
      return fail(500, { message: `An error occurred during file upload: ${errorMessage}. Please try again.` });
    }
  }
};
