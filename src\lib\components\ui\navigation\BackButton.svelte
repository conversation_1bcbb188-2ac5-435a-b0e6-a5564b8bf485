<script lang="ts">
  import { locale, t } from '$lib/stores/locale';

  const {
    href = '/',
    label = '',
    class: className = ''
  } = $props<{
    href: string,
    label?: string,
    class?: string
  }>();

  // Use reactive store access instead of manual subscription
  const currentLocale = $derived($locale);
  const buttonLabel = $derived(label || t('common.back', currentLocale));
  const buttonClass = $derived(`
    inline-flex items-center px-3 py-2 text-base
    text-gray-700 dark:text-gray-300
    hover:text-gray-900 dark:hover:text-white
    transition-colors
    ${className}
  `);
</script>

<a {href} class={buttonClass}>
  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
  </svg>
  {buttonLabel}
</a>
