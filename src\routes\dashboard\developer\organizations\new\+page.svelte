<script lang="ts">
  import { enhance } from '$app/forms';
  import {
    PageHeader,
    FormLayout,
    FormInput,
    AlertMessage,
    Button,
    BackButton
  } from '$lib/components/ui';

  interface PageData {
    user: {
      id: string;
      username: string;
      role: "student" | "lecturer" | "admin" | "developer";
    };
  }

  let { form } = $props<{ data: PageData, form: any }>();
  let isActive = $state(true);
</script>

<div>
  <div class="mb-6 flex items-center space-x-3">
    <BackButton href="/dashboard/developer/organizations" label="Back to Organizations" />
    <PageHeader title="Create New Organization" />
  </div>

  {#if form?.success}
    <AlertMessage
      type="success"
      message={form.message}
      actionText="Go back to organization management"
      actionHref="/dashboard/developer/organizations"
    />
  {:else if form?.message}
    <AlertMessage
      type="error"
      message={form.message}
    />
  {/if}

  <FormLayout>
    <form method="POST" action="?/createOrganization" use:enhance class="space-y-6">
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <FormInput
          id="name"
          name="name"
          label="Organization Name"
          placeholder="Example University"
          required
        />

        <FormInput
          id="description"
          name="description"
          label="Description"
          placeholder="Brief description of the organization"
        />

        <div>
          <div class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-2">Status</div>
          <div class="flex items-center space-x-4">
            <label class="inline-flex items-center" for="isActive">
              <input
                type="checkbox"
                id="isActive"
                name="isActive"
                checked={isActive}
                oninput={(e: Event) => isActive = (e.target as HTMLInputElement).checked}
                class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Active</span>
            </label>
          </div>
        </div>
      </div>

      <div>
        <Button
          type="submit"
          variant="primary"
        >
          Create Organization
        </Button>
      </div>
    </form>
  </FormLayout>
</div>
