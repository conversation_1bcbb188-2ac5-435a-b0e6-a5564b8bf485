import { fail, redirect } from '@sveltejs/kit';
import { eq, and, sql } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { verifyPassword, hashPassword } from '$lib/server/auth/password';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;

  if (!user) {
    return redirect(302, '/auth/login');
  }

  let organizations = [];
  try {
    organizations = await db
      .select({
        id: table.organization.id,
        name: table.organization.name,
        description: table.organization.description
      })
      .from(table.organization)
      .where(eq(table.organization.isActive, true));
  } catch (error) {
    console.error('Error getting active organizations, falling back to all:', error);
    organizations = await db
      .select({
        id: table.organization.id,
        name: table.organization.name,
        description: table.organization.description
      })
      .from(table.organization);
  }

  const [userData] = await db
    .select()
    .from(table.user)
    .where(eq(table.user.id, user.id));

  return {
    user: userData,
    organizations
  };
};

export const actions: Actions = {
  updateProfile: async (event) => {
    const { user } = event.locals;

    if (!user) {
      return redirect(302, '/auth/login');
    }

    const formData = await event.request.formData();
    const name = formData.get('name')?.toString().trim();
    const email = formData.get('email')?.toString().trim();
    const organization = formData.get('organization')?.toString();

    // Validate inputs
    if (!name || name.length < 2 || name.length > 100) {
      return fail(400, { success: false, message: 'Name must be between 2 and 100 characters' });
    }

    if (!email || !/^[^@]+@[^@]+\.[^@]+$/.test(email)) {
      return fail(400, { success: false, message: 'Please enter a valid email address' });
    }

    try {
      if (email !== user.email) {
        const existingUsers = await db
          .select({ id: table.user.id })
          .from(table.user)
          .where(
            and(
              eq(table.user.email, email),
              sql`"id" != ${user.id}`
            )
          );

        if (existingUsers.length > 0) {
          return fail(400, { success: false, message: 'Email is already in use by another account' });
        }
      }

      if (organization && user.role !== 'admin' && user.role !== 'developer') {
        const [org] = await db
          .select()
          .from(table.organization)
          .where(
            and(
              eq(table.organization.id, organization),
              eq(table.organization.isActive, true)
            )
          );

        if (!org) {
          return fail(400, { success: false, message: 'Selected organization is invalid or inactive' });
        }

        await db
          .update(table.user)
          .set({
            name,
            email,
            organization
          })
          .where(eq(table.user.id, user.id));
      } else {
        await db
          .update(table.user)
          .set({
            name,
            email
          })
          .where(eq(table.user.id, user.id));
      }

      return { success: true, message: 'Profile updated successfully' };
    } catch (error) {
      console.error('Error updating profile:', error);
      return fail(500, { success: false, message: 'An error occurred while updating your profile' });
    }
  },

  changePassword: async (event) => {
    const { user } = event.locals;

    if (!user) {
      return redirect(302, '/auth/login');
    }

    const formData = await event.request.formData();
    const currentPassword = formData.get('currentPassword')?.toString();
    const newPassword = formData.get('newPassword')?.toString();
    const confirmPassword = formData.get('confirmPassword')?.toString();

    if (!currentPassword) {
      return fail(400, { passwordSuccess: false, passwordMessage: 'Current password is required' });
    }

    if (!newPassword || newPassword.length < 6 || newPassword.length > 255) {
      return fail(400, { passwordSuccess: false, passwordMessage: 'New password must be between 6 and 255 characters' });
    }

    if (newPassword !== confirmPassword) {
      return fail(400, { passwordSuccess: false, passwordMessage: 'New passwords do not match' });
    }

    try {
      const [userData] = await db
        .select({ passwordHash: table.user.passwordHash })
        .from(table.user)
        .where(eq(table.user.id, user.id));

      if (!userData) {
        return fail(400, { passwordSuccess: false, passwordMessage: 'User not found' });
      }

      const isPasswordValid = await verifyPassword(userData.passwordHash, currentPassword);

      if (!isPasswordValid) {
        return fail(400, { passwordSuccess: false, passwordMessage: 'Current password is incorrect' });
      }

      const newPasswordHash = await hashPassword(newPassword);

      await db
        .update(table.user)
        .set({ passwordHash: newPasswordHash })
        .where(eq(table.user.id, user.id));

      return { passwordSuccess: true, passwordMessage: 'Password updated successfully' };
    } catch (error) {
      console.error('Error changing password:', error);
      return fail(500, { passwordSuccess: false, passwordMessage: 'An error occurred while changing your password' });
    }
  }
};