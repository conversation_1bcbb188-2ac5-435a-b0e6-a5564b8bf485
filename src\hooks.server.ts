import type { Handle } from '@sveltejs/kit';
import * as auth from '$lib/server/auth';

// Session cache with LRU-like behavior
const SESSION_CACHE = new Map<string, {
  expires: number;
  user: any;
  session: any;
}>();

const CACHE_TTL = 15 * 60 * 1000; // 15 minutes
const CACHE_CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes

// Clean expired sessions periodically
const cleanupInterval = setInterval(() => {
  const now = Date.now();
  let expiredCount = 0;

  for (const [key, value] of SESSION_CACHE.entries()) {
    if (now > value.expires) {
      SESSION_CACHE.delete(key);
      expiredCount++;
    }
  }

  if (expiredCount > 0) {
    console.log(`Cleaned up ${expiredCount} expired sessions from cache`);
  }
}, CACHE_CLEANUP_INTERVAL);

// Ensure cleanup interval is cleared if the server is restarted
if (typeof process !== 'undefined') {
  process.on('SIGTERM', () => clearInterval(cleanupInterval));
}

export const handle: Handle = async ({ event, resolve }) => {
  // Handle authentication
  const sessionToken = event.cookies.get(auth.sessionCookieName);

  if (sessionToken) {
    // Try to get session from cache first
    const cachedSession = SESSION_CACHE.get(sessionToken);

    if (cachedSession && cachedSession.expires > Date.now()) {
      // Use cached session data
      event.locals.user = cachedSession.user;
      event.locals.session = cachedSession.session;
    } else {
      // Validate from database
      const { session, user } = await auth.validateSessionToken(sessionToken);

      if (session) {
        // Update cookie and cache valid session
        auth.setSessionTokenCookie(event, sessionToken, session.expiresAt);
        SESSION_CACHE.set(sessionToken, {
          expires: Date.now() + CACHE_TTL,
          user,
          session
        });
      } else {
        // Clear invalid session
        auth.deleteSessionTokenCookie(event);
        SESSION_CACHE.delete(sessionToken);
      }

      // Set locals regardless of validation result
      event.locals.user = user;
      event.locals.session = session;
    }
  } else {
    // No session token
    event.locals.user = null;
    event.locals.session = null;
  }

  // Then handle response
  const locale = event.cookies.get('locale') || 'en';
  const response = await resolve(event, {
    transformPageChunk: ({ html }) => html.replace('<html', `<html lang="${locale}"`)
  });

  // Set cache headers for static assets
  const path = event.url.pathname;
  if ((path.startsWith('/favicon.') || path.startsWith('/_app/')) ||
      (path.includes('.') && !path.endsWith('.html'))) {
    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
  }

  return response;
};
