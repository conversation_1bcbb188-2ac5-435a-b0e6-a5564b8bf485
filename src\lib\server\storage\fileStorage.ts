import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const UPLOADS_BASE_DIR = path.resolve(__dirname, '../../../../uploads');
const TEMP_DIR = path.resolve(__dirname, '../../../../temp');

// Security constants
const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB
const ALLOWED_MIME_TYPES = {
  'application/pdf': [0x25, 0x50, 0x44, 0x46], // %PDF
  'application/zip': [0x50, 0x4B, 0x03, 0x04], // PK..
  'application/x-rar-compressed': [0x52, 0x61, 0x72, 0x21], // Rar!
  'application/x-7z-compressed': [0x37, 0x7A, 0xBC, 0xAF] // 7z..
};

if (!fs.existsSync(UPLOADS_BASE_DIR)) {
  fs.mkdirSync(UPLOADS_BASE_DIR, { recursive: true });
}

if (!fs.existsSync(TEMP_DIR)) {
  fs.mkdirSync(TEMP_DIR, { recursive: true });
}

export function ensureUserUploadDir(userId: string): string {
  const userDir = path.join(UPLOADS_BASE_DIR, userId);
  if (!fs.existsSync(userDir)) {
    fs.mkdirSync(userDir, { recursive: true });
  }
  return userDir;
}

/**
 * Validates file type using magic numbers (file signatures)
 */
function validateFileType(buffer: Buffer, mimeType: string): boolean {
  const signature = ALLOWED_MIME_TYPES[mimeType as keyof typeof ALLOWED_MIME_TYPES];
  if (!signature) return false;

  return signature.every((byte, index) => buffer[index] === byte);
}

/**
 * Sanitizes filename to prevent path traversal and other attacks
 */
function sanitizeFilename(filename: string): string {
  // Remove path separators and dangerous characters
  const sanitized = filename
    .replace(/[\/\\:*?"<>|]/g, '_')
    .replace(/\.\./g, '_')
    .replace(/^\.+/, '')
    .substring(0, 255); // Limit length

  // Ensure filename is not empty
  return sanitized || 'unnamed_file';
}

/**
 * Generates a secure random filename
 */
function generateSecureFilename(originalFilename: string): string {
  const ext = path.extname(originalFilename);
  const randomBytes = crypto.randomBytes(16).toString('hex');
  const timestamp = Date.now();
  const sanitizedName = sanitizeFilename(path.basename(originalFilename, ext));

  return `${timestamp}_${randomBytes}_${sanitizedName}${ext}`;
}

export async function saveFile(userId: string, file: any, originalFilename: string): Promise<string> {
  const buffer = await getBufferFromFile(file);

  // Validate file size
  if (buffer.length > MAX_FILE_SIZE) {
    throw new Error(`File size exceeds maximum allowed size of ${MAX_FILE_SIZE / (1024 * 1024)}MB`);
  }

  // Validate file type using magic numbers
  const mimeType = file.type || 'application/octet-stream';
  if (!validateFileType(buffer, mimeType)) {
    throw new Error(`Invalid file type. File signature does not match declared MIME type: ${mimeType}`);
  }

  const userDir = ensureUserUploadDir(userId);
  const filename = generateSecureFilename(originalFilename);
  const filePath = path.join(userDir, filename);

  // Ensure the file path is within the user directory (prevent path traversal)
  const resolvedPath = path.resolve(filePath);
  const resolvedUserDir = path.resolve(userDir);
  if (!resolvedPath.startsWith(resolvedUserDir)) {
    throw new Error('Invalid file path detected');
  }

  fs.writeFileSync(filePath, buffer);
  return `/uploads/${userId}/${filename}`;
}

async function getBufferFromFile(file: any): Promise<Buffer> {
  if (file.arrayBuffer) return Buffer.from(await file.arrayBuffer());
  if (Buffer.isBuffer(file)) return file;
  if (file.buffer) return Buffer.from(file.buffer);
  return Buffer.from(file);
}

export function getAbsoluteFilePath(relativePath: string): string {
  return path.join(
    path.dirname(UPLOADS_BASE_DIR),
    relativePath.startsWith('/') ? relativePath.substring(1) : relativePath
  );
}

export function fileExists(relativePath: string): boolean {
  return fs.existsSync(getAbsoluteFilePath(relativePath));
}
