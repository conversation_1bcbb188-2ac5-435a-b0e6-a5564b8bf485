<script lang="ts">
  import {
    Card,
    Countdown,
    PageHeader,
    AlertMessage,
    Button
  } from '$lib/components/ui';
  import { locale, t } from '$lib/stores/locale';

  let { data } = $props();

  // Use reactive store access instead of manual state management
  const currentLocale = $derived($locale);

  function formatDateTime(dateString: string | Date | null): string {
    if (!dateString) return t('common.noDeadline', currentLocale);
    return new Date(dateString).toLocaleString();
  }

  function isDeadlinePassed(deadline: string | Date | null): boolean {
    if (!deadline) return false;
    return new Date() > new Date(deadline);
  }
</script>

<div>
  <PageHeader
    title={t('dashboard.student.submit.title', currentLocale)}
    class="mb-6"
  />

  <div>
      {#if data.projects.length === 0}
        <AlertMessage
          type="warning"
          message={`${t('dashboard.student.submit.noProjectsAvailable', currentLocale)}: ${t('dashboard.student.submit.noProjectsDesc', currentLocale)}`}
          class="mt-4"
        />
      {:else}
        <Card>
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">{t('dashboard.student.submit.projectName', currentLocale)}</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">{t('dashboard.student.submit.projectDescription', currentLocale)}</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">{t('dashboard.student.submit.deadline', currentLocale)}</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">{t('dashboard.student.submit.actions', currentLocale)}</th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              {#each data.projects as project}
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{project.name}</div>
                  </td>
                  <td class="px-6 py-4">
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                      {#if project.description}
                        {project.description}
                      {:else}
                        <span class="text-gray-400 dark:text-gray-600 italic">{t('dashboard.student.submit.noDescription', currentLocale)}</span>
                      {/if}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                      {#if project.deadline}
                        {#if isDeadlinePassed(project.deadline)}
                          <span class="text-red-600 font-medium">{t('common.expired', currentLocale)} ({formatDateTime(project.deadline)})</span>
                        {:else}
                          <span>
                            {formatDateTime(project.deadline)}
                            <div class="text-sm font-medium">
                              <Countdown deadline={project.deadline} showSeconds={false} warningThreshold={60} />
                            </div>
                          </span>
                        {/if}
                      {:else}
                        <span class="text-gray-400 dark:text-gray-600 italic">{t('common.noDeadline', currentLocale)}</span>
                      {/if}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <Button
                      variant="light"
                      size="sm"
                      onClick={() => window.location.href = `/dashboard/student/projects/${project.id}`}
                    >
                      {t('dashboard.student.submit.viewAndSubmit', currentLocale)}
                    </Button>
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </Card>
      {/if}
      </div>
</div>
