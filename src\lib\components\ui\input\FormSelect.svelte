<script lang="ts">
  const {
    id = '',
    name = '',
    label = '',
    value = '',
    options = [],
    placeholder = 'Select an option',
    required = false,
    disabled = false,
    helpText = '',
    class: className = '',
    onChange = undefined,
  } = $props<{
    id: string,
    name: string,
    label?: string,
    value?: string,
    options: Array<{value: string, label: string}>,
    placeholder?: string,
    required?: boolean,
    disabled?: boolean,
    helpText?: string,
    class?: string,
    onChange?: (e: Event) => void,
  }>();

  const selectClass = $derived(`
    appearance-none relative block w-full px-4 py-3 
    border border-gray-300 dark:border-gray-600 
    placeholder-gray-500 dark:placeholder-gray-400 
    text-gray-900 dark:text-white dark:bg-gray-700 
    rounded-md focus:outline-none focus:ring-blue-500 
    focus:border-blue-500 focus:z-10 text-base
    ${disabled ? 'opacity-60 cursor-not-allowed' : ''}
    ${className}
  `);
</script>

<div>
  {#if label}
    <label for={id} class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-2">
      {label}
      {#if required}<span class="text-red-500">*</span>{/if}
    </label>
  {/if}
  
  <div class="relative">
    <select
      {id}
      {name}
      {required}
      {disabled}
      value={value}
      class={selectClass}
      oninput={onChange}
    >
      <option value="" disabled selected={!value}>{placeholder}</option>
      {#each options as option}
        <option value={option.value}>{option.label}</option>
      {/each}
    </select>
    
    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-gray-700 dark:text-gray-300">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
      </svg>
    </div>
  </div>
  
  {#if helpText}
    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
      {helpText}
    </p>
  {/if}
</div>
