<script lang="ts">
  import { <PERSON>, PageHeader, Button, EmptyState } from '$lib/components/ui';

  let { data } = $props();

  function formatDate(dateString: string | Date): string {
    return new Date(dateString).toLocaleDateString();
  }

  function formatTime(timeString: string): string {
    if (!timeString) return '';
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  }

  function getWeekdayName(weekday: string): string {
    const weekdays = {
      monday: 'Monday',
      tuesday: 'Tuesday',
      wednesday: 'Wednesday',
      thursday: 'Thursday',
      friday: 'Friday',
      saturday: 'Saturday',
      sunday: 'Sunday'
    };
    return weekdays[weekday as keyof typeof weekdays] || weekday;
  }
</script>

<div>
  <div class="flex justify-between items-center mb-6">
    <PageHeader title="My Groups" />
    <Button
      variant="primary"
      onClick={() => window.location.href = '/dashboard/student/groups/join'}
    >
      <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
      </svg>
      Join Group with Code
    </Button>
  </div>

  {#if data.groups.length === 0}
    <Card>
      <EmptyState
        icon="users"
        message="No groups. You are not a member of any groups yet."
      />
    </Card>
  {:else}
    <div class="grid grid-cols-1 gap-6">
      {#each data.groups as group}
        <Card>
          <div>
            <h2 class="text-xl font-semibold text-gray-900">{group.group.name}</h2>
            {#if group.group.description}
              <p class="mt-1 text-sm text-gray-600">{group.group.description}</p>
            {/if}
            <p class="text-xs text-gray-500 mt-1">Added: {formatDate(group.addedAt)}</p>
          </div>

          {#if group.lectureTimes.length > 0}
            <div class="mt-4">
              <h3 class="text-sm font-medium text-gray-700 mb-2">Lecture Schedule</h3>
              <div class="overflow-x-auto">
                <div class="min-w-full">
                  <table class="w-full border-collapse">
                    <thead>
                      <tr>
                        {#each ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as day}
                          <th class="p-2 text-center font-medium bg-gray-100 border border-gray-200">{day}</th>
                        {/each}
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        {#each ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] as day}
                          <td class="p-2 border border-gray-200 align-top min-h-[100px] h-[100px] min-w-[100px] w-[100px] relative">
                            {#each group.lectureTimes.filter((lt: { weekday: string }) => lt.weekday === day) as lecture}
                              <div class="mb-2 p-2 bg-blue-50 border border-blue-100 rounded text-xs">
                                <p class="font-medium">{formatTime(lecture.startTime)} - {formatTime(lecture.endTime)}</p>
                                {#if lecture.location}
                                  <p class="text-gray-600">{lecture.location}</p>
                                {/if}
                              </div>
                            {/each}
                          </td>
                        {/each}
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <div class="mt-4">
                <h3 class="text-sm font-medium text-gray-700 mb-2">All Scheduled Times</h3>
                <ul class="space-y-2">
                  {#each group.lectureTimes as lecture}
                    <li class="text-sm">
                      <span class="font-medium">{getWeekdayName(lecture.weekday)}:</span>
                      <span>{formatTime(lecture.startTime)} - {formatTime(lecture.endTime)}</span>
                      {#if lecture.location}
                        <span class="text-gray-500"> ({lecture.location})</span>
                      {/if}
                    </li>
                  {/each}
                </ul>
              </div>
            </div>
          {:else}
            <div class="mt-4">
              <p class="text-sm text-gray-500 italic">No lecture times scheduled for this group.</p>
            </div>
          {/if}

          {#if group.projects.length > 0}
            <div class="mt-4">
              <h3 class="text-sm font-medium text-gray-700 mb-2">Associated Projects</h3>
              <ul class="space-y-2">
                {#each group.projects as projectGroup}
                  <li class="text-sm">
                    <a
                      href={`/dashboard/student/submit?projectId=${projectGroup.projectId}`}
                      class="font-medium text-blue-600 hover:text-blue-800"
                    >
                      {projectGroup.project.name}
                    </a>
                    {#if projectGroup.project.description}
                      <p class="text-xs text-gray-500">{projectGroup.project.description}</p>
                    {/if}
                  </li>
                {/each}
              </ul>
            </div>
          {/if}
        </Card>
      {/each}
    </div>
  {/if}
</div>
