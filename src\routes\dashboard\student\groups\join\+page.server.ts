import { fail, redirect } from '@sveltejs/kit';
import { and, eq } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;

  if (!user || user.role !== 'student') {
    return redirect(303, '/auth/login');
  }

  return { user };
};

export const actions: Actions = {
  joinGroup: async (event) => {
    const { user } = event.locals;
    
    if (!user || user.role !== 'student') {
      return fail(403, { success: false, message: 'Unauthorized' });
    }
    
    const formData = await event.request.formData();
    const joinCode = formData.get('joinCode')?.toString().trim().toUpperCase();
    
    if (!joinCode) {
      return fail(400, { success: false, message: 'Join code is required' });
    }
    
    try {
      const [group] = await db
        .select()
        .from(table.group)
        .where(
          and(
            eq(table.group.joinCode, joinCode),
            eq(table.group.joinEnabled, true)
          )
        );
        
      if (!group) {
        return fail(400, { success: false, message: 'Invalid join code' });
      }
      
      if (group.joinCodeExpiry && new Date(group.joinCodeExpiry) < new Date()) {
        return fail(400, { success: false, message: 'Join code has expired' });
      }
      
      const [existingMembership] = await db
        .select()
        .from(table.groupMember)
        .where(
          and(
            eq(table.groupMember.groupId, group.id),
            eq(table.groupMember.studentId, user.id)
          )
        );
        
      if (existingMembership) {
        return fail(400, { success: false, message: 'You are already a member of this group' });
      }
      
      await db.insert(table.groupMember).values({
        id: crypto.randomUUID(),
        groupId: group.id,
        studentId: user.id,
        addedBy: group.createdBy,
        addedAt: new Date()
      });
      
      return {
        success: true,
        message: `Successfully joined group: ${group.name}`,
        groupId: group.id
      };
    } catch (error) {
      console.error('Error joining group:', error);
      return fail(500, { success: false, message: 'An error occurred while joining the group' });
    }
  }
};
