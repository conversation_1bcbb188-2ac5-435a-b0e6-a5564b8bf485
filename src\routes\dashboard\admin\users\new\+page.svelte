<script lang="ts">
  import { enhance } from '$app/forms';
  import { PageHeader, FormLayout, FormInput, FormSelect, AlertMessage, Button, BackButton } from '$lib/components/ui';

  let { form } = $props();

  let username = $state('');
  let email = $state('');
  let password = $state('');
  let role = $state('student');
  let isSubmitting = $state(false);

  const roleOptions = [
    { value: 'student', label: 'Student' },
    { value: 'lecturer', label: 'Lecturer' }
  ];

  function handleSubmit() {
    isSubmitting = true;
    return ({ update }: { update: () => Promise<void> }) => {
      isSubmitting = false;
      update();
    };
  }
</script>

<div>
  <PageHeader title="Create New User" />

  {#if form?.success}
    <AlertMessage
      type="success"
      message="User created successfully!"
      actionText="Return to user list"
      actionHref="/dashboard/admin/users"
    />
  {/if}

  <FormLayout title="User Details">
    <form
      method="POST"
      action="?/createUser"
      use:enhance={handleSubmit}
      class="space-y-4"
    >
      <FormInput
        id="username"
        name="username"
        label="Username"
        value={username}
        onChange={(e: Event) => username = (e.target as HTMLInputElement).value}
        required
      />

      <FormInput
        id="email"
        name="email"
        type="email"
        label="Email"
        value={email}
        onChange={(e: Event) => email = (e.target as HTMLInputElement).value}
        required
      />

      <FormInput
        id="password"
        name="password"
        type="password"
        label="Password"
        value={password}
        onChange={(e: Event) => password = (e.target as HTMLInputElement).value}
        required
        minlength={8}
      />

      <FormSelect
        id="role"
        name="role"
        label="Role"
        value={role}
        options={roleOptions}
        onChange={(e: Event) => role = (e.target as HTMLSelectElement).value}
        required
      />

      {#if form?.message && !form?.success}
        <AlertMessage
          type="error"
          message={form.message}
        />
      {/if}

      <div class="mt-6 flex items-center justify-end">
        <BackButton
          href="/dashboard/admin/users"
          label="Cancel"
          class="mr-4"
        />
        <Button
          type="submit"
          variant="primary"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Creating...' : 'Create User'}
        </Button>
      </div>
    </form>
  </FormLayout>
</div>