import { redirect, fail } from '@sveltejs/kit';
import { eq, and } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;

  if (!user || user.role !== 'developer') {
    return redirect(303, '/auth/login');
  }

  let organizations = [];

  organizations = await db
    .select({
      id: table.organization.id,
      name: table.organization.name,
      description: table.organization.description,
      isActive: table.organization.isActive,
      isFrozen: table.organization.isFrozen,
      createdAt: table.organization.createdAt
    })
    .from(table.organization)
    .orderBy(table.organization.name);

  return {
    user,
    organizations,
    settings: {
      maxProjectsPerLecturer: 10,
      systemVersion: '1.0.0',
      lastBackup: new Date().toISOString()
    }
  };
};

export const actions: Actions = {
  updateSettings: async (event) => {
    const { user } = event.locals;

    if (!user || user.role !== 'developer') {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const formData = await event.request.formData();

    return {
      success: true,
      message: 'Settings updated successfully'
    };
  },

  createOrganization: async (event) => {
    const { user } = event.locals;

    if (!user || user.role !== 'developer') {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const formData = await event.request.formData();
    const name = formData.get('name')?.toString();
    const description = formData.get('description')?.toString();

    if (!name) {
      return fail(400, { success: false, message: 'Organization name is required' });
    }

    try {
      await db.insert(table.organization).values({
        id: crypto.randomUUID(),
        name,
        description: description || '',
        isActive: true,
        isFrozen: false,
        createdBy: user.id
      });

      return {
        success: true,
        message: 'Organization created successfully'
      };
    } catch (error) {
      console.error('Error creating organization:', error);
      return fail(500, { success: false, message: 'Failed to create organization' });
    }
  },

  updateOrganization: async (event) => {
    const { user } = event.locals;

    if (!user || user.role !== 'developer') {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const formData = await event.request.formData();
    const orgId = formData.get('organizationId')?.toString();
    const name = formData.get('name')?.toString();
    const description = formData.get('description')?.toString();

    if (!orgId || !name) {
      return fail(400, { success: false, message: 'Organization ID and name are required' });
    }

    try {
      const [org] = await db
        .select()
        .from(table.organization)
        .where(eq(table.organization.id, orgId));

      if (!org) {
        return fail(404, { success: false, message: 'Organization not found' });
      }

      await db
        .update(table.organization)
        .set({
          name,
          description: description || ''
        })
        .where(eq(table.organization.id, orgId));

      return {
        success: true,
        message: 'Organization updated successfully'
      };
    } catch (error) {
      console.error('Error updating organization:', error);
      return fail(500, { success: false, message: 'Failed to update organization' });
    }
  },

  toggleOrganizationStatus: async (event) => {
    const { user } = event.locals;

    if (!user || user.role !== 'developer') {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const formData = await event.request.formData();
    const orgId = formData.get('organizationId')?.toString();
    const action = formData.get('action')?.toString();

    if (!orgId || !action) {
      return fail(400, { success: false, message: 'Organization ID and action are required' });
    }

    try {
      const [org] = await db
        .select()
        .from(table.organization)
        .where(eq(table.organization.id, orgId));

      if (!org) {
        return fail(404, { success: false, message: 'Organization not found' });
      }

      let updateData = {};
      let successMessage = '';

      switch (action) {
        case 'activate':
          updateData = { isActive: true };
          successMessage = 'Organization activated successfully';
          break;
        case 'deactivate':
          updateData = { isActive: false };
          successMessage = 'Organization deactivated successfully';
          break;
        case 'freeze':
          updateData = { isFrozen: true };
          successMessage = 'Organization frozen successfully';
          break;
        case 'unfreeze':
          updateData = { isFrozen: false };
          successMessage = 'Organization unfrozen successfully';
          break;
        default:
          return fail(400, { success: false, message: 'Invalid action' });
      }

      await db
        .update(table.organization)
        .set(updateData)
        .where(eq(table.organization.id, orgId));

      return {
        success: true,
        message: successMessage
      };
    } catch (error) {
      console.error('Error toggling organization status:', error);
      return fail(500, { success: false, message: 'Failed to update organization status' });
    }
  },

  deleteOrganization: async (event) => {
    const { user } = event.locals;

    if (!user || user.role !== 'developer') {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const formData = await event.request.formData();
    const orgId = formData.get('organizationId')?.toString();

    if (!orgId) {
      return fail(400, { success: false, message: 'Organization ID is required' });
    }

    try {
      const userCount = await db
        .select({ count: { value: table.user.id } })
        .from(table.user)
        .where(eq(table.user.organization, orgId));

      if (userCount.length > 0 && Number(userCount[0].count.value) > 0) {
        return fail(400, {
          success: false,
          message: 'Cannot delete organization with active users. Deactivate the organization instead.'
        });
      }

      await db
        .delete(table.organization)
        .where(eq(table.organization.id, orgId));

      return {
        success: true,
        message: 'Organization deleted successfully'
      };
    } catch (error) {
      console.error('Error deleting organization:', error);
      return fail(500, { success: false, message: 'Failed to delete organization' });
    }
  }
};