<script lang="ts">
  import {
    <PERSON>,
    PageHeader,
    Button,
    EmptyState
  } from '$lib/components/ui';

  let { data } = $props();

  function formatDate(dateString: string | Date): string {
    return new Date(dateString).toLocaleDateString();
  }

  function formatTime(timeString: string): string {
    if (!timeString) return '';
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  }

  function getWeekdayName(weekday: string): string {
    const weekdays = {
      monday: 'Monday',
      tuesday: 'Tuesday',
      wednesday: 'Wednesday',
      thursday: 'Thursday',
      friday: 'Friday',
      saturday: 'Saturday',
      sunday: 'Sunday'
    };
    return weekdays[weekday as keyof typeof weekdays] || weekday;
  }
</script>

<div>
  <div class="flex justify-between items-center mb-6">
    <PageHeader title="Student Groups" />
    <Button
      variant="primary"
      onClick={() => window.location.href = '/dashboard/lecturer/groups/new'}
    >
      <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
      </svg>
      Create New Group
    </Button>
  </div>

  {#if data.groups.length === 0}
    <Card>
      <EmptyState
        icon="users"
        message="No groups"
        description="Get started by creating a new student group."
      />
      <div class="mt-6 text-center">
        <Button
          variant="primary"
          onClick={() => window.location.href = '/dashboard/lecturer/groups/new'}
        >
          <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
          </svg>
          Create New Group
        </Button>
      </div>
    </Card>
  {:else}
    <div class="grid grid-cols-1 gap-6">
      {#each data.groups as group}
        <Card>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h2 class="text-xl font-semibold text-gray-900">{group.name}</h2>
              {#if group.description}
                <p class="mt-1 text-sm text-gray-600">{group.description}</p>
              {/if}
            </div>
            <div class="flex justify-end items-start">
              <Button
                variant="light"
                onClick={() => window.location.href = `/dashboard/lecturer/groups/${group.id}`}
              >
                Manage Group
              </Button>
            </div>
          </div>

          <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <span class="text-sm text-gray-500">Students</span>
              <p class="text-sm font-medium text-gray-900">{group.studentCount}</p>
            </div>
            <div>
              <span class="text-sm text-gray-500">Co-managers</span>
              <p class="text-sm font-medium text-gray-900">{group.managerCount}</p>
            </div>
            <div>
              <span class="text-sm text-gray-500">Created</span>
              <p class="text-sm font-medium text-gray-900">{formatDate(group.createdAt)}</p>
            </div>
          </div>

          {#if group.lectureTimes.length > 0}
            <div class="mt-4">
              <h3 class="text-sm font-medium text-gray-700 mb-2">Lecture Schedule</h3>
              <div class="flex flex-wrap gap-2">
                {#each group.lectureTimes as lecture}
                  <div class="bg-gray-100 rounded-md px-3 py-1 text-xs">
                    <span class="font-medium">{getWeekdayName(lecture.weekday)}</span>
                    <span>{formatTime(lecture.startTime)} - {formatTime(lecture.endTime)}</span>
                    {#if lecture.location}
                      <span class="ml-1 text-gray-500">({lecture.location})</span>
                    {/if}
                  </div>
                {/each}
              </div>
            </div>
          {/if}
        </Card>
      {/each}
    </div>
  {/if}
</div>
