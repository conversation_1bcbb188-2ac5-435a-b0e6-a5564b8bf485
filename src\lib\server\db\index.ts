import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

const databaseUrl = process.env.DATABASE_URL || 'postgres://root:mysecretpassword@localhost:5432/local';

console.log(`Connecting to database: ${databaseUrl.replace(/\/\/.*?@/, '//****:****@')}`);
console.log('Environment variables available:', Object.keys(process.env).filter(key => !key.includes('SECRET') && !key.includes('KEY')).join(', '));

let client;
try {
  client = postgres(databaseUrl, {
    max: 8,
    idle_timeout: 20,
    prepare: true
  });
  console.log('Database client created successfully');
} catch (error: any) {
  console.error('Error creating database client:', error);
  throw new Error(`Failed to connect to database: ${error?.message || 'Unknown error'}.
    Please check that:
    1. The DATABASE_URL environment variable is set correctly in .env
    2. The database server is running
    3. The database credentials are correct`);
}

export const db = drizzle(client, { schema });
