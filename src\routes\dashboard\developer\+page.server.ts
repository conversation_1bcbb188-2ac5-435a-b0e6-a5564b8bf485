import { eq, count, and } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { PageServerLoad } from './$types';
import { requireRole } from '$lib/utils/auth';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;
  requireRole(user, 'developer');

  // Get total counts
  const [totalUsersResult] = await db
    .select({ count: count() })
    .from(table.user);

  const [totalStudentsResult] = await db
    .select({ count: count() })
    .from(table.user)
    .where(eq(table.user.role, 'student'));

  const [totalLecturersResult] = await db
    .select({ count: count() })
    .from(table.user)
    .where(eq(table.user.role, 'lecturer'));

  const [totalAdminsResult] = await db
    .select({ count: count() })
    .from(table.user)
    .where(eq(table.user.role, 'admin'));

  const [activeAdminsResult] = await db
    .select({ count: count() })
    .from(table.user)
    .where(
      and(
        eq(table.user.role, 'admin'),
        eq(table.user.isActive, true)
      )
    );

  const [totalProjectsResult] = await db
    .select({ count: count() })
    .from(table.project);

  const [totalSubmissionsResult] = await db
    .select({ count: count() })
    .from(table.submission);

  const organizations = await db
    .select({
      id: table.organization.id,
      name: table.organization.name,
      isActive: table.organization.isActive
    })
    .from(table.organization)
    .orderBy(table.organization.name);

  return {
    user,
    stats: {
      activeAdmins: activeAdminsResult?.count || 0,
      totalUsers: totalUsersResult?.count || 0,
      totalStudents: totalStudentsResult?.count || 0,
      totalLecturers: totalLecturersResult?.count || 0,
      totalAdmins: totalAdminsResult?.count || 0,
      totalProjects: totalProjectsResult?.count || 0,
      totalSubmissions: totalSubmissionsResult?.count || 0
    },
    organizations
  };
};