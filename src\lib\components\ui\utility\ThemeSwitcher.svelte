<script lang="ts">
  import { theme, toggleTheme } from '$lib/stores/theme';
  import { t, locale } from '$lib/stores/locale';
  import { scale } from 'svelte/transition';

  type SizeType = 'sm' | 'md' | 'lg';
  type VariantType = 'default' | 'minimal' | 'icon-only';
  const {
    size = 'md',
    variant = 'default',
    showText = false,
    class: className = ''
  } = $props<{
    size?: SizeType,
    variant?: VariantType,
    showText?: boolean,
    class?: string
  }>();

  const sizeClasses = {
    sm: 'h-8 px-2 py-1 text-xs',
    md: 'h-10 px-2.5 py-1.5 text-sm',
    lg: 'h-12 px-3 py-2 text-base'
  };

  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };
  const variantClasses = {
    default: 'border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700',
    minimal: 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700',
    'icon-only': 'p-1 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
  };

  const buttonClass = $derived(`
    inline-flex items-center justify-center rounded-md
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800
    transition-colors duration-200
    ${sizeClasses[size as SizeType]}
    ${variantClasses[variant as VariantType]}
    ${className}
  `);

  const iconClass = $derived(iconSizes[size as SizeType]);
  const ariaLabel = $derived(
    $theme === 'dark'
      ? t('theme.switchToLight', $locale)
      : t('theme.switchToDark', $locale)
  );

  const labelText = $derived(
    $theme === 'dark'
      ? t('theme.light', $locale)
      : t('theme.dark', $locale)
  );
</script>

<button
  type="button"
  class={buttonClass}
  onclick={toggleTheme}
  aria-label={ariaLabel}
>
  {#if $theme === 'dark'}
    <div in:scale={{ duration: 200, start: 0.8 }}>
      <svg xmlns="http://www.w3.org/2000/svg" class={iconClass} viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd" />
      </svg>
    </div>
  {:else}
    <div in:scale={{ duration: 200, start: 0.8 }}>
      <svg xmlns="http://www.w3.org/2000/svg" class={iconClass} viewBox="0 0 20 20" fill="currentColor">
        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
      </svg>
    </div>
  {/if}

  {#if showText}
    <span class="ml-2">{labelText}</span>
  {/if}
</button>
