export function validateUsername(username: unknown): username is string {
  return (
    typeof username === 'string' &&
    username.length >= 3 &&
    username.length <= 31 &&
    /^[a-z0-9_-]+$/.test(username)
  );
}

export function validateEmail(email: unknown): email is string {
  return (
    typeof email === 'string' &&
    email.length >= 3 &&
    email.length <= 255 &&
    /^[^@]+@[^@]+\.[^@]+$/.test(email)
  );
}

export function validatePassword(password: unknown): password is string {
  return (
    typeof password === 'string' &&
    password.length >= 6 &&
    password.length <= 255
  );
}

export function validateRole(role: unknown): role is string {
  return (
    typeof role === 'string' &&
    ['student', 'lecturer', 'admin', 'developer'].includes(role)
  );
}

export function validateName(name: unknown): name is string {
  return (
    typeof name === 'string' &&
    name.trim().length >= 2 &&
    name.trim().length <= 100
  );
}

export function checkPasswordStrength(password: string): number {
  if (!password) {
    return 0;
  }

  let strength = 0;

  // Length check
  if (password.length >= 8) {
    strength += 1;
  }
  if (password.length >= 12) {
    strength += 0.5;
  }

  // Complexity checks
  if (/[a-z]/.test(password) && /[A-Z]/.test(password)) {
    strength += 1;
  }
  if (/[0-9]/.test(password)) {
    strength += 1;
  }
  if (/[^a-zA-Z0-9]/.test(password)) {
    strength += 1;
  }

  // common patterns
  const commonSequences = [
    '123', '234', '345', '456', '567', '678', '789',
    'abc', 'bcd', 'cde', 'def', 'efg', 'fgh', 'ghi',
    'hij', 'ijk', 'jkl', 'klm', 'lmn', 'mno', 'nop',
    'opq', 'pqr', 'qrs', 'rst', 'stu', 'tuv', 'uvw',
    'vwx', 'wxy', 'xyz'
  ];

  if (commonSequences.some(seq => password.toLowerCase().includes(seq))) {
    strength -= 0.5;
  }

  return Math.max(0, Math.min(4, Math.round(strength)));
}

export async function generateSHA1Hash(text: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(text);
  const hashBuffer = await crypto.subtle.digest('SHA-1', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}
