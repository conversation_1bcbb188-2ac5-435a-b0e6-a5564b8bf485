// Type declarations for external modules that don't have their own type definitions

// SvelteKit environment modules
declare module '$env/dynamic/private' {
  export const env: {
    [key: string]: string | undefined;
  };
}

declare module '$env/dynamic/public' {
  export const env: {
    [key: string]: string | undefined;
  };
}

declare module 'pdf-parse' {
  export interface PDFParseOptions {
    pagerender?: (pageData: any) => Promise<string>;
    max?: number;
    version?: string;
  }

  export interface PDFParseResult {
    numpages: number;
    numrender: number;
    info: {
      PDFFormatVersion?: string;
      IsAcroFormPresent?: boolean;
      IsXFAPresent?: boolean;
      IsCollectionPresent?: boolean;
      Title?: string;
      Author?: string;
      Subject?: string;
      Keywords?: string;
      Creator?: string;
      Producer?: string;
      CreationDate?: string;
      ModDate?: string;
      Trapped?: string;
    };
    metadata: any;
    text: string;
    version: string;
  }

  function parse(dataBuffer: Buffer, options?: PDFParseOptions): Promise<PDFParseResult>;
  export default parse;
}

declare module 'node-7z' {
  export interface SevenZipOptions {
    $bin?: string;
    $progress?: boolean;
    recursive?: boolean;
    [key: string]: any;
  }

  export interface Seven {
    extract(archivePath: string, destPath: string, options?: SevenZipOptions): Promise<any>;
    extractFull(archivePath: string, destPath: string, options?: SevenZipOptions): Promise<any>;
  }

  const Seven: {
    extract(archivePath: string, destPath: string, options?: SevenZipOptions): Promise<any>;
    extractFull(archivePath: string, destPath: string, options?: SevenZipOptions): Promise<any>;
  };
  export default Seven;
}

declare module 'node-unrar-js' {
  export interface ExtractorOptions {
    data: Uint8Array;
  }

  export interface FileHeader {
    name: string;
    // Add other properties as needed
  }

  export interface ExtractedFile {
    fileHeader: FileHeader;
    extraction?: Uint8Array;
  }

  export interface ExtractOptions {
    files?: string[] | ((fileHeader: FileHeader) => boolean);
  }

  export interface ExtractResult {
    files: ExtractedFile[];
  }

  export interface Extractor {
    extract(options: ExtractOptions): ExtractResult;
  }

  export function createExtractorFromData(options: ExtractorOptions): Promise<Extractor>;
}
