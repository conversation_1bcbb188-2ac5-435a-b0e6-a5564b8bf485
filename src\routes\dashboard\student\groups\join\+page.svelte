<script lang="ts">
  import { enhance } from '$app/forms';
  import {
    Card,
    PageHeader,
    Button,
    FormInput,
    AlertMessage,
    BackButton
  } from '$lib/components/ui';

  let { form } = $props();

  let joinCode = $state('');
  let isSubmitting = $state(false);

  function handleSubmit() {
    isSubmitting = true;
    return async ({ result, update }: { result: any; update: () => void }) => {
      isSubmitting = false;

      if (result.type === 'success' && result.data?.success) {
        setTimeout(() => {
          window.location.href = '/dashboard/student/groups';
        }, 2000);
      }

      update();
    };
  }
</script>

<div class="max-w-md mx-auto">
  <PageHeader title="Join a Group" class="mb-6" />

  <Card>
    <form method="POST" action="?/joinGroup" use:enhance={handleSubmit} class="space-y-6">
      <FormInput
        id="joinCode"
        name="joinCode"
        label="Enter Group Join Code"
        value={joinCode}
        onChange={(e: Event) => joinCode = (e.target as HTMLInputElement).value}
        placeholder="Enter 6-character code"
        maxlength={6}

        helpText="Enter the 6-character code provided by your lecturer to join a group."
        required
      />

      {#if form?.message}
        <AlertMessage
          type={form.success ? "success" : "error"}
          message={form.success ? `${form.message}. Redirecting to your groups page...` : form.message}
        />
      {/if}

      <div class="flex justify-between">
        <BackButton href="/dashboard/student/groups" label="Back to Groups" />
        <Button
          type="submit"
          variant="primary"
          disabled={isSubmitting || !joinCode}
        >
          {isSubmitting ? 'Joining...' : 'Join Group'}
        </Button>
      </div>
    </form>
  </Card>
</div>
