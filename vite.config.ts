import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';
import { loadEnv } from 'vite';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  process.env = { ...process.env, ...env };

  return {
    plugins: [sveltekit()],
    server: {
      host: '0.0.0.0',
      port: 5173,
      allowedHosts: ['pdfbankas.kurkas.com']
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@use "src/variables.scss" as *;',
        },
      },
      devSourcemap: true,
    },
    build: {
      target: 'esnext',
      reportCompressedSize: false,
    },
    optimizeDeps: {
      exclude: [
        'node-unrar-js',
        'pdf-parse',
        'node-7z',
        'adm-zip'
      ]
    }
  };
});