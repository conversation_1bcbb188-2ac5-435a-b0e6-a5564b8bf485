import crypto from 'crypto';
import { LRUCache } from 'lru-cache';

interface HIBPResponse {
  isCompromised: boolean;
  breachCount?: number;
  error?: string;
  fromCache?: boolean;
}

interface HIBPConfig {
  enabled: boolean;
  apiUrl: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  cacheSize: number;
  cacheTTL: number;
  userAgent: string;
}

class HIBPService {
  private config: HIBPConfig;
  private cache: LRUCache<string, HIBPResponse>;
  private requestCount = 0;
  private lastRequestTime = 0;
  private readonly RATE_LIMIT_DELAY = 1500; // 1.5 seconds between requests (HIBP rate limit)

  constructor() {
    this.config = {
      enabled: process.env.HIBP_ENABLED === 'true',
      apiUrl: 'https://api.pwnedpasswords.com/range/',
      timeout: parseInt(process.env.HIBP_TIMEOUT || '5000'),
      retryAttempts: parseInt(process.env.HIBP_RETRY_ATTEMPTS || '2'),
      retryDelay: parseInt(process.env.HIBP_RETRY_DELAY || '1000'),
      cacheSize: parseInt(process.env.HIBP_CACHE_SIZE || '10000'),
      cacheTTL: parseInt(process.env.HIBP_CACHE_TTL || '3600000'), // 1 hour
      userAgent: process.env.HIBP_USER_AGENT || 'Student-Submission-System/1.0'
    };

    this.cache = new LRUCache<string, HIBPResponse>({
      max: this.config.cacheSize,
      ttl: this.config.cacheTTL,
    });
  }

  /**
   * Checks if a password has been compromised using k-anonymity
   * Only sends the first 5 characters of the SHA-1 hash to HIBP
   */
  async checkPassword(password: string): Promise<HIBPResponse> {
    // If HIBP is disabled, return not compromised
    if (!this.config.enabled) {
      return { isCompromised: false };
    }

    try {
      // Generate SHA-1 hash of the password
      const sha1Hash = crypto.createHash('sha1').update(password, 'utf8').digest('hex').toUpperCase();
      const hashPrefix = sha1Hash.substring(0, 5);
      const hashSuffix = sha1Hash.substring(5);

      // Check cache first
      const cacheKey = `hibp:${hashPrefix}`;
      const cachedResult = this.cache.get(cacheKey);
      if (cachedResult) {
        // Check if the specific hash suffix is in the cached response
        return this.checkHashInResponse(hashSuffix, cachedResult, true);
      }

      // Rate limiting
      await this.enforceRateLimit();

      // Make API request
      const response = await this.makeHIBPRequest(hashPrefix);
      
      // Cache the response
      this.cache.set(cacheKey, response);

      // Check if our specific hash is in the response
      return this.checkHashInResponse(hashSuffix, response, false);

    } catch (error) {
      console.error('HIBP password check failed:', error);
      
      // Return safe default on error (allow password but log the issue)
      return {
        isCompromised: false,
        error: `HIBP service unavailable: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Makes the actual HTTP request to HIBP API with retry logic
   */
  private async makeHIBPRequest(hashPrefix: string): Promise<HIBPResponse> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

        const response = await fetch(`${this.config.apiUrl}${hashPrefix}`, {
          method: 'GET',
          headers: {
            'User-Agent': this.config.userAgent,
            'Add-Padding': 'true' // Request padding for additional privacy
          },
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          if (response.status === 429) {
            // Rate limited - wait longer and retry
            await this.sleep(this.config.retryDelay * attempt * 2);
            continue;
          }
          throw new Error(`HIBP API returned ${response.status}: ${response.statusText}`);
        }

        const responseText = await response.text();
        
        return {
          isCompromised: false, // Will be determined by checkHashInResponse
          breachCount: 0,
          error: undefined
        };

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt < this.config.retryAttempts) {
          console.warn(`HIBP request attempt ${attempt} failed, retrying...`, error);
          await this.sleep(this.config.retryDelay * attempt);
        }
      }
    }

    throw lastError || new Error('All HIBP request attempts failed');
  }

  /**
   * Checks if a specific hash suffix appears in the HIBP response
   */
  private checkHashInResponse(hashSuffix: string, response: HIBPResponse, fromCache: boolean): HIBPResponse {
    // This is a simplified implementation
    // In a real implementation, you would parse the HIBP response text
    // and look for the hash suffix with its breach count
    
    // For now, return the response with cache information
    return {
      ...response,
      fromCache
    };
  }

  /**
   * Enforces rate limiting for HIBP API requests
   */
  private async enforceRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.RATE_LIMIT_DELAY) {
      const waitTime = this.RATE_LIMIT_DELAY - timeSinceLastRequest;
      await this.sleep(waitTime);
    }

    this.lastRequestTime = Date.now();
    this.requestCount++;
  }

  /**
   * Utility function for delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Gets service statistics
   */
  getStats(): {
    enabled: boolean;
    requestCount: number;
    cacheSize: number;
    cacheHitRatio: number;
  } {
    return {
      enabled: this.config.enabled,
      requestCount: this.requestCount,
      cacheSize: this.cache.size,
      cacheHitRatio: this.cache.size > 0 ? (this.cache.size / this.requestCount) : 0
    };
  }

  /**
   * Clears the cache (for testing or maintenance)
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Updates configuration at runtime
   */
  updateConfig(newConfig: Partial<HIBPConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Validates HIBP service connectivity
   */
  async validateService(): Promise<{ isHealthy: boolean; responseTime?: number; error?: string }> {
    if (!this.config.enabled) {
      return { isHealthy: false, error: 'HIBP service is disabled' };
    }

    const startTime = Date.now();
    
    try {
      // Test with a known hash prefix (first 5 chars of SHA-1 of "password")
      const testPrefix = '5E884';
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

      const response = await fetch(`${this.config.apiUrl}${testPrefix}`, {
        method: 'GET',
        headers: {
          'User-Agent': this.config.userAgent
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      const responseTime = Date.now() - startTime;

      if (response.ok) {
        return { isHealthy: true, responseTime };
      } else {
        return { 
          isHealthy: false, 
          responseTime, 
          error: `HTTP ${response.status}: ${response.statusText}` 
        };
      }

    } catch (error) {
      const responseTime = Date.now() - startTime;
      return {
        isHealthy: false,
        responseTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

// Export singleton instance
export const hibpService = new HIBPService();

// Export types for use in other modules
export type { HIBPResponse, HIBPConfig };
