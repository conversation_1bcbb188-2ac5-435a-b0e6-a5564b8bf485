import { db } from '$lib/server/db';
import { sql } from 'drizzle-orm';

interface QueryPerformanceMetrics {
  query: string;
  executionTime: number;
  rowsAffected?: number;
  planCost?: number;
  indexUsage?: string[];
}

interface DatabasePerformanceStats {
  connectionPoolSize: number;
  activeConnections: number;
  avgQueryTime: number;
  slowQueries: QueryPerformanceMetrics[];
  indexEfficiency: IndexEfficiencyStats[];
  cacheHitRatio: number;
}

interface IndexEfficiencyStats {
  tableName: string;
  indexName: string;
  scans: number;
  tupleReads: number;
  efficiency: number;
}

class DatabasePerformanceMonitor {
  private queryMetrics: Map<string, QueryPerformanceMetrics[]> = new Map();
  private slowQueryThreshold = 100; // ms
  private maxMetricsHistory = 1000;

  /**
   * Wraps a database query with performance monitoring
   */
  async monitorQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>,
    enableExplain: boolean = false
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await queryFn();
      const executionTime = Date.now() - startTime;
      
      // Record metrics
      this.recordQueryMetrics(queryName, executionTime);
      
      // Log slow queries
      if (executionTime > this.slowQueryThreshold) {
        console.warn(`Slow query detected: ${queryName} took ${executionTime}ms`);
        
        if (enableExplain && process.env.NODE_ENV === 'development') {
          await this.explainQuery(queryName);
        }
      }
      
      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      console.error(`Query failed: ${queryName} after ${executionTime}ms`, error);
      throw error;
    }
  }

  /**
   * Records query performance metrics
   */
  private recordQueryMetrics(queryName: string, executionTime: number): void {
    if (!this.queryMetrics.has(queryName)) {
      this.queryMetrics.set(queryName, []);
    }
    
    const metrics = this.queryMetrics.get(queryName)!;
    metrics.push({
      query: queryName,
      executionTime,
    });
    
    // Keep only recent metrics
    if (metrics.length > this.maxMetricsHistory) {
      metrics.shift();
    }
  }

  /**
   * Gets performance statistics for security queries
   */
  async getSecurityQueryStats(): Promise<DatabasePerformanceStats> {
    try {
      const [connectionStats] = await db.execute(sql`
        SELECT 
          setting as max_connections,
          (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections
        FROM pg_settings WHERE name = 'max_connections'
      `);

      const indexStats = await this.getIndexEfficiencyStats();
      const slowQueries = this.getSlowQueries();
      const avgQueryTime = this.getAverageQueryTime();
      const cacheHitRatio = await this.getCacheHitRatio();

      return {
        connectionPoolSize: parseInt(connectionStats.max_connections) || 0,
        activeConnections: parseInt(connectionStats.active_connections) || 0,
        avgQueryTime,
        slowQueries,
        indexEfficiency: indexStats,
        cacheHitRatio
      };
    } catch (error) {
      console.error('Error getting database performance stats:', error);
      return {
        connectionPoolSize: 0,
        activeConnections: 0,
        avgQueryTime: 0,
        slowQueries: [],
        indexEfficiency: [],
        cacheHitRatio: 0
      };
    }
  }

  /**
   * Gets index efficiency statistics for security tables
   */
  private async getIndexEfficiencyStats(): Promise<IndexEfficiencyStats[]> {
    try {
      const results = await db.execute(sql`
        SELECT 
          schemaname,
          tablename,
          indexname,
          idx_scan as scans,
          idx_tup_read as tuple_reads,
          CASE 
            WHEN idx_scan = 0 THEN 0
            ELSE ROUND((idx_tup_read::numeric / idx_scan::numeric), 2)
          END as efficiency
        FROM pg_stat_user_indexes 
        WHERE tablename IN ('login_attempt', 'account_lockout', 'user', 'session')
        ORDER BY idx_scan DESC
      `);

      return results.map(row => ({
        tableName: row.tablename,
        indexName: row.indexname,
        scans: parseInt(row.scans) || 0,
        tupleReads: parseInt(row.tuple_reads) || 0,
        efficiency: parseFloat(row.efficiency) || 0
      }));
    } catch (error) {
      console.error('Error getting index efficiency stats:', error);
      return [];
    }
  }

  /**
   * Gets cache hit ratio for performance monitoring
   */
  private async getCacheHitRatio(): Promise<number> {
    try {
      const [result] = await db.execute(sql`
        SELECT 
          ROUND(
            (sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read))) * 100, 
            2
          ) as cache_hit_ratio
        FROM pg_statio_user_tables
        WHERE schemaname = 'public'
      `);

      return parseFloat(result.cache_hit_ratio) || 0;
    } catch (error) {
      console.error('Error getting cache hit ratio:', error);
      return 0;
    }
  }

  /**
   * Explains a query for performance analysis
   */
  private async explainQuery(queryName: string): Promise<void> {
    // This would need to be implemented based on specific queries
    // For now, just log that explain was requested
    console.log(`EXPLAIN analysis requested for query: ${queryName}`);
  }

  /**
   * Gets slow queries from metrics
   */
  private getSlowQueries(): QueryPerformanceMetrics[] {
    const slowQueries: QueryPerformanceMetrics[] = [];
    
    for (const [queryName, metrics] of this.queryMetrics.entries()) {
      const recentSlow = metrics
        .filter(m => m.executionTime > this.slowQueryThreshold)
        .slice(-10); // Last 10 slow executions
      
      slowQueries.push(...recentSlow);
    }
    
    return slowQueries.sort((a, b) => b.executionTime - a.executionTime);
  }

  /**
   * Calculates average query time across all monitored queries
   */
  private getAverageQueryTime(): number {
    let totalTime = 0;
    let totalQueries = 0;
    
    for (const metrics of this.queryMetrics.values()) {
      totalTime += metrics.reduce((sum, m) => sum + m.executionTime, 0);
      totalQueries += metrics.length;
    }
    
    return totalQueries > 0 ? totalTime / totalQueries : 0;
  }

  /**
   * Optimizes security table queries by analyzing and suggesting improvements
   */
  async optimizeSecurityQueries(): Promise<string[]> {
    const suggestions: string[] = [];
    
    try {
      // Check for missing indexes on security tables
      const missingIndexes = await db.execute(sql`
        SELECT 
          schemaname,
          tablename,
          attname,
          n_distinct,
          correlation
        FROM pg_stats 
        WHERE tablename IN ('login_attempt', 'account_lockout')
        AND n_distinct > 100
        ORDER BY n_distinct DESC
      `);

      for (const row of missingIndexes) {
        suggestions.push(
          `Consider adding index on ${row.tablename}.${row.attname} (${row.n_distinct} distinct values)`
        );
      }

      // Check for unused indexes
      const unusedIndexes = await db.execute(sql`
        SELECT 
          schemaname,
          tablename,
          indexname,
          idx_scan
        FROM pg_stat_user_indexes 
        WHERE tablename IN ('login_attempt', 'account_lockout')
        AND idx_scan < 10
        ORDER BY idx_scan ASC
      `);

      for (const row of unusedIndexes) {
        suggestions.push(
          `Index ${row.indexname} on ${row.tablename} has low usage (${row.idx_scan} scans)`
        );
      }

      return suggestions;
    } catch (error) {
      console.error('Error analyzing query optimization:', error);
      return ['Error analyzing query optimization'];
    }
  }

  /**
   * Clears performance metrics (for memory management)
   */
  clearMetrics(): void {
    this.queryMetrics.clear();
  }

  /**
   * Gets current metrics summary
   */
  getMetricsSummary(): { [queryName: string]: { count: number; avgTime: number; maxTime: number } } {
    const summary: { [queryName: string]: { count: number; avgTime: number; maxTime: number } } = {};
    
    for (const [queryName, metrics] of this.queryMetrics.entries()) {
      const times = metrics.map(m => m.executionTime);
      summary[queryName] = {
        count: metrics.length,
        avgTime: times.reduce((sum, time) => sum + time, 0) / times.length,
        maxTime: Math.max(...times)
      };
    }
    
    return summary;
  }
}

// Export singleton instance
export const dbPerformanceMonitor = new DatabasePerformanceMonitor();

// Helper function to wrap security queries with monitoring
export async function monitorSecurityQuery<T>(
  queryName: string,
  queryFn: () => Promise<T>
): Promise<T> {
  return dbPerformanceMonitor.monitorQuery(queryName, queryFn, true);
}
