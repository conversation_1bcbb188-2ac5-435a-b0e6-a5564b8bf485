<script lang="ts">
  // types
  type StatusType = 'approved' | 'rejected' | 'pending' | 'active' | 'inactive' | 'success' | 'error' | 'warning' | 'info';

  // Passed values
  let { status = '', customText = '' } = $props<{ status: string, customText?: string }>();

  const statusMap: Record<StatusType, string> = {
    approved: 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300',
    rejected: 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-300',
    pending: 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-300',
    active: 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300',
    inactive: 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300',
    success: 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300',
    error: 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-300',
    warning: 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-300',
    info: 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300'
  };

  const badgeStyle = statusMap[status.toLowerCase() as StatusType] || statusMap.pending;

  const displayText = customText || status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
</script>

<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {badgeStyle}">
  {displayText}
</span>
