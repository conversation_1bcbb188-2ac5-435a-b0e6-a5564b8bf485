<script lang="ts">

  // Passed values
  let { deadline, showSeconds = true, warningThreshold = 60 } = $props<{
    deadline: Date | string;
    showSeconds?: boolean;
    warningThreshold?: number; // in minutes
  }>();

  const deadlineDate = typeof deadline === 'string' ? new Date(deadline) : deadline;

  let timeRemaining = $state({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    total: 0
  });

  let intervalId: ReturnType<typeof setInterval> | undefined;

  // remaining time
  function calculateTimeRemaining() {
    const now = new Date();
    const diff = deadlineDate.getTime() - now.getTime();

    if (diff <= 0) {
      timeRemaining = {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
        total: 0
      };

      if (intervalId) {
        clearInterval(intervalId);
      }

      return;
    }

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    timeRemaining = {
      days,
      hours,
      minutes,
      seconds,
      total: diff
    };
  }

  function formatCountdown() {
    if (timeRemaining.total <= 0) {
      return "Expired";
    }

    let result = '';

    if (timeRemaining.days > 0) {
      result += `${timeRemaining.days}d `;
    }

    if (timeRemaining.hours > 0 || timeRemaining.days > 0) {
      result += `${timeRemaining.hours}h `;
    }

    result += `${timeRemaining.minutes}m`;

    if (showSeconds) {
      result += ` ${timeRemaining.seconds}s`;
    }

    return result;
  }

  function isTimeRunningLow() {
    return timeRemaining.total > 0 &&
           timeRemaining.total <= warningThreshold * 60 * 1000;
  }

  // Modern effect pattern for interval management
  $effect(() => {
    calculateTimeRemaining();
    intervalId = setInterval(calculateTimeRemaining, 1000);

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  });
</script>

<span class={`font-medium ${isTimeRunningLow() ? 'text-red-600' : ''}`}>
  {formatCountdown()}
</span>
