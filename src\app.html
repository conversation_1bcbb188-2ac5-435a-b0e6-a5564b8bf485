<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="%sveltekit.assets%/favicon.png" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<script>
			(function() {
				function getThemePreference() {
					const savedTheme = localStorage.getItem('theme');
					if (savedTheme === 'dark' || savedTheme === 'light') {
						return savedTheme;
					}
					return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
				}
				
				const theme = getThemePreference();
				document.documentElement.classList.toggle('dark', theme === 'dark');
				
				if (!localStorage.getItem('theme')) {
					window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
						document.documentElement.classList.toggle('dark', e.matches);
					});
				}
			})();
		</script>
		%sveltekit.head%
	</head>
	<body class="transition-colors duration-200" data-sveltekit-preload-data="hover">
		<div style="display: contents">%sveltekit.body%</div>
	</body>
</html>