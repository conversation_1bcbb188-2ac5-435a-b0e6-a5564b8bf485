<script lang="ts">
  import { enhance } from '$app/forms';
  import {
    PageHeader,
    Card,
    FormInput,
    FormSelect,
    AlertMessage,
    Button
  } from '$lib/components/ui';

  let { data, form } = $props();

  function formatDate(dateString: string | Date | null | undefined): string {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString();
  }

  let searchQuery = $state('');
  let filterRole = $state('all');
  let filterStatus = $state('all');

  interface AdminUser {
    id: string;
    username: string;
    email: string;
    role: string;
    isActive: boolean;
    isApproved: boolean;
    organization: string | null;
    createdAt: string | Date;
  }

  let filteredUsers = $derived(data.users.filter((user: AdminUser) => {
    const matchesSearch =
      searchQuery === '' ||
      user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesRole = filterRole === 'all' || user.role === filterRole;

    const matchesStatus =
      filterStatus === 'all' ||
      (filterStatus === 'active' && user.isActive) ||
      (filterStatus === 'inactive' && !user.isActive);

    return matchesSearch && matchesRole && matchesStatus;
  }));
</script>

<div>
  <PageHeader title="User Management" />

  {#if form?.success}
    <AlertMessage
      type="success"
      message={form.message}
      class="mb-6"
    />
  {/if}

  <Card>
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
      <h2 class="text-xl font-semibold mb-2 md:mb-0">Users</h2>
    </div>

    <div class="mb-6 flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 items-end">
      <div class="md:w-1/3">
        <FormInput
          id="search"
          name="search"
          label="Search"
          value={searchQuery}
          onChange={(e: Event) => searchQuery = (e.target as HTMLInputElement).value}
          placeholder="Search by username or email"
        />
      </div>

      <div class="md:w-1/3">
        <FormSelect
          id="role"
          name="role"
          label="Role"
          value={filterRole}
          onChange={(e: Event) => filterRole = (e.target as HTMLSelectElement).value}
          options={[
            { value: 'all', label: 'All Roles' },
            { value: 'student', label: 'Students' },
            { value: 'lecturer', label: 'Lecturers' },
            { value: 'admin', label: 'Admins' }
          ]}
        />
      </div>

      <div class="md:w-1/3">
        <FormSelect
          id="status"
          name="status"
          label="Status"
          value={filterStatus}
          onChange={(e: Event) => filterStatus = (e.target as HTMLSelectElement).value}
          options={[
            { value: 'all', label: 'All Status' },
            { value: 'active', label: 'Active' },
            { value: 'inactive', label: 'Inactive' }
          ]}
        />
      </div>
    </div>

    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              User
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Organization
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Role
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>

            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Created
            </th>
            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          {#if filteredUsers.length === 0}
            <tr>
              <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                No users matching the current filters
              </td>
            </tr>
          {:else}
            {#each filteredUsers as user}
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{user.username}</div>
                  <div class="text-sm text-gray-500">{user.email}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {#if user.organization && data.organizationMap && data.organizationMap[user.organization]}
                    {data.organizationMap[user.organization]}
                  {:else}
                    <span class="text-gray-400">None</span>
                  {/if}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    user.role === 'admin' ? 'bg-purple-100 text-purple-800' :
                    user.role === 'lecturer' ? 'bg-blue-100 text-blue-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {user.role}
                  </span>
                  {#if user.role === 'lecturer' && !user.isApproved}
                    <span class="ml-1 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      pending
                    </span>
                  {/if}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {user.isActive ? 'active' : 'inactive'}
                  </span>
                </td>

                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500">{formatDate(user.createdAt)}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex justify-end space-x-3">
                    <Button
                      variant="light"
                      size="sm"
                      onClick={() => window.location.href = `/dashboard/admin/users/${user.id}`}
                    >
                      Edit
                    </Button>
                    <form method="POST" action="?/toggleUserStatus" use:enhance>
                      <input type="hidden" name="userId" value={user.id} />
                      <input type="hidden" name="currentStatus" value={user.isActive.toString()} />
                      <Button
                        type="submit"
                        variant={user.isActive ? "danger" : "success"}
                        size="sm"
                      >
                        {user.isActive ? 'Deactivate' : 'Activate'}
                      </Button>
                    </form>
                  </div>
                </td>
              </tr>
            {/each}
          {/if}
        </tbody>
      </table>
    </div>
  </Card>
</div>