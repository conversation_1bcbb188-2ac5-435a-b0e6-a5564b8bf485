<script lang="ts">
  import { enhance } from '$app/forms';
  import { page } from '$app/state';
  import { locale, t, type Locale } from '$lib/stores/locale';
  import {
    <PERSON><PERSON>,
    Card,
    PageHeader,
    DashboardSection,
    ActionButton
  } from '$lib/components/ui';

  // Define the type for the data prop
  type DashboardData = {
    user?: {
      id: string;
      username: string;
      role: 'student' | 'lecturer' | 'admin' | 'developer';
      email: string;
      isActive: boolean;
    };
    stats?: {
      submissions: number;
      projects: number;
      users: number;
      groups: number;
    };
  };

  let { data } = $props<{ data: DashboardData }>();

  // Use reactive store access instead of manual state management
  const currentLocale = $derived($locale);

  function getNavItems(role: string, locale: Locale) {
    const commonItems = [
      { href: '/settings', label: t('common.settings', locale), icon: 'user' }
    ];

    const roleSpecificItems: Record<string, Array<{ href: string; label: string; icon: string }>> = {
      student: [
        { href: '/dashboard/student', label: t('header.dashboard', locale), icon: 'home' },
        { href: '/dashboard/student/submissions', label: t('header.submissions', locale), icon: 'file' }
      ],
      lecturer: [
        { href: '/dashboard/lecturer', label: t('header.dashboard', locale), icon: 'home' },
        { href: '/dashboard/lecturer/projects', label: t('header.projects', locale), icon: 'folder' },
        { href: '/dashboard/lecturer/groups', label: t('header.groups', locale), icon: 'users' },
        { href: '/dashboard/lecturer/submissions', label: t('header.submissions', locale), icon: 'file' }
      ],
      admin: [
        { href: '/dashboard/admin', label: t('header.dashboard', locale), icon: 'home' },
        { href: '/dashboard/admin/projects', label: t('header.projects', locale), icon: 'folder' },
        { href: '/dashboard/admin/users', label: t('header.users', locale), icon: 'users' },
        { href: '/dashboard/admin/approvals', label: t('header.approvals', locale), icon: 'check-circle' }
      ],
      developer: [
        { href: '/dashboard/developer', label: t('header.dashboard', locale), icon: 'home' },
        { href: '/dashboard/developer/admins', label: t('header.adminManagement', locale), icon: 'users' },
        { href: '/dashboard/developer/organizations', label: 'Organizations', icon: 'folder' },
        { href: '/dashboard/developer/pdf-analysis', label: 'PDF Analysis', icon: 'file' },
        { href: '/dashboard/developer/system/settings', label: t('header.system', locale), icon: 'settings' }
      ]
    };

    return [...(roleSpecificItems[role] || []), ...commonItems];
  }

  type NavItem = { href: string; label: string; icon: string };
  let navItems = $state<NavItem[]>([]);

  $effect(() => {
    navItems = getNavItems(data?.user?.role || 'student', currentLocale);
  });

  function isActive(href: string): boolean {
    return page.url.pathname === href || page.url.pathname.startsWith(href + '/');
  }

  function getIconSvg(iconName: string): string {
    const icons: Record<string, string> = {
      home: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />',
      folder: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />',
      key: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />',
      file: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />',
      users: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />',
      'check-circle': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />',
      user: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />',
      settings: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />'
    };

    return icons[iconName] || '';
  }

  function getStatTiles(locale: Locale) {
    return [
      {
        label: t('dashboard.stats.submissions', locale),
        value: data.stats?.submissions || 0,
        icon: "file",
        color: "bg-green-500"
      },
      {
        label: t('dashboard.stats.projects', locale),
        value: data.stats?.projects || 0,
        icon: "folder",
        color: "bg-blue-500"
      },
      {
        label: t('dashboard.stats.users', locale),
        value: data.stats?.users || 0,
        icon: "users",
        color: "bg-purple-500"
      },
      {
        label: t('dashboard.stats.groups', locale),
        value: data.stats?.groups || 0,
        icon: "users",
        color: "bg-indigo-500"
      }
    ];
  }

  type StatTile = { label: string; value: number; icon: string; color: string };
  let statTiles = $state<StatTile[]>([]);

  $effect(() => {
    statTiles = getStatTiles(currentLocale);
  });
</script>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex flex-col md:flex-row">
        <div class="hidden md:flex md:flex-col md:w-64 md:mr-8">
          <nav class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 space-y-1 sticky top-24 border border-gray-200 dark:border-gray-700">
            {#each navItems as item}
              <a
                href={item.href}
                class={isActive(item.href)
                  ? "bg-indigo-50 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300 group flex items-center px-3 py-2 text-sm font-medium rounded-md"
                  : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 group flex items-center px-3 py-2 text-sm font-medium rounded-md"}
              >
                <svg class={isActive(item.href)
                    ? "text-indigo-500 dark:text-indigo-400 mr-3 flex-shrink-0 h-6 w-6"
                    : "text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400 mr-3 flex-shrink-0 h-6 w-6"}
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  {@html getIconSvg(item.icon)}
                </svg>
                {item.label}
              </a>
            {/each}

            <!-- Logout button -->
            <div class="pt-4 mt-4 border-t border-gray-200 dark:border-gray-700">
              <form action="/auth/logout" method="POST" use:enhance>
                <Button
                  type="submit"
                  variant="light"
                  class="w-full justify-start"
                >
                  <svg class="text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400 mr-3 flex-shrink-0 h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  Sign out
                </Button>
              </form>
            </div>
          </nav>
        </div>

        <main class="flex-1">
          <PageHeader
            title="{t('common.welcome', currentLocale)}, {data?.user?.username || t('common.user', currentLocale)}"
            subtitle={
              data?.user?.role === 'student' ? t('dashboard.student.message', currentLocale) :
              data?.user?.role === 'lecturer' ? t('dashboard.lecturer.message', currentLocale) :
              data?.user?.role === 'admin' ? t('dashboard.admin.message', currentLocale) :
              data?.user?.role === 'developer' ? t('dashboard.developer.message', currentLocale) :
              t('dashboard.default.message', currentLocale)
            }
            class="mb-6"
          />

          <DashboardSection cols="4" gap="6" marginBottom={true}>
            {#each statTiles as stat}
              <Card>
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="{stat.color} text-white p-3 rounded-md">
                      <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        {@html getIconSvg(stat.icon)}
                      </svg>
                    </div>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{stat.label}</dt>
                      <dd>
                        <div class="text-lg font-semibold text-gray-900 dark:text-white">{stat.value}</div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </Card>
            {/each}
          </DashboardSection>

          <Card title={t('dashboard.quickActions', currentLocale)} class="mb-8">
            <div class="p-6">
              <DashboardSection cols="3" gap="6" marginBottom={true}>
                <ActionButton
                  href="/dashboard/{data?.user?.role || 'student'}/groups"
                  color="purple"
                  title={t('dashboard.manageGroups', currentLocale)}
                  description={t('dashboard.manageGroupsDesc', currentLocale)}
                />

                <ActionButton
                  href="/dashboard/{data?.user?.role || 'student'}/submissions"
                  color="blue"
                  title={t('dashboard.submissionManagement', currentLocale)}
                  description={t('dashboard.submissionManagementDesc', currentLocale)}
                />

                <ActionButton
                  href="/dashboard/{data?.user?.role || 'student'}/projects/new"
                  color="green"
                  title={t('dashboard.projectCreation', currentLocale)}
                  description={t('dashboard.projectCreationDesc', currentLocale)}
                />
              </DashboardSection>

              <div class="flex justify-center">
                <Button
                  variant="light"
                  onClick={() => window.location.href = '/dashboard/help'}
                >
                  {t('dashboard.viewAllActions', currentLocale)}
                  <svg class="ml-2 -mr-1 h-4 w-4 text-gray-400 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10l-3.293-3.293a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                </Button>
              </div>
            </div>
          </Card>

          <Card>
            <!-- Empty card for future content -->
          </Card>
        </main>
      </div>
    </div>
  </div>
</div>