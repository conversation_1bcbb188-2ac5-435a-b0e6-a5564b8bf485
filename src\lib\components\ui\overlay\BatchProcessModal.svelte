<script lang="ts">
  import { <PERSON><PERSON>, <PERSON><PERSON>, AlertMessage } from '$lib/components/ui';

  let {
    show = false,
    onclose = () => {},
    onprocess = () => {}
  } = $props<{
    show: boolean;
    onclose?: () => void;
    onprocess?: (formData: FormData) => Promise<any>;
  }>();

  let fileInput = $state<HTMLInputElement | null>(null);
  let selectedFile = $state<File | null>(null);
  let isProcessing = $state(false);
  let processingResult = $state<{success: boolean; message: string; downloadUrl?: string; fileCount?: number; fileSize?: number; data?: any} | null>(null);
  let errorMessage = $state('');

  const isProcessButtonDisabled = $derived(!selectedFile || isProcessing);

  function handleClose() {
    if (!isProcessing) {
      selectedFile = null;
      processingResult = null;
      errorMessage = '';
      onclose();
    }
  }

  function handleFileChange(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      selectedFile = input.files[0];
      errorMessage = '';
    } else {
      selectedFile = null;
    }
  }

  async function handleProcess() {
    if (!selectedFile) {
      errorMessage = 'Please select a file to process';
      return;
    }

    const fileExt = selectedFile.name.split('.').pop()?.toLowerCase();
    if (fileExt !== 'zip' && fileExt !== 'rar' && fileExt !== '7z') {
      errorMessage = 'Only ZIP, RAR, or 7Z archives are supported';
      return;
    }

    isProcessing = true;
    errorMessage = '';
    processingResult = null;

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);

      const result = await onprocess(formData);

      console.log('Batch process result:', result);

      if (result && typeof result === 'object') {
        processingResult = {
          success: result.success === true,
          message: result.message || (result.success ? 'Processing completed successfully' : 'Processing failed'),
          downloadUrl: result.downloadUrl || (result.data?.downloadUrl),
          fileCount: result.fileCount || (result.data?.fileCount),
          fileSize: result.fileSize || (result.data?.fileSize),
          data: result.data
        };
      } else {
        console.error('Unexpected response format:', result);
        errorMessage = 'Received an invalid response from the server';
      }
    } catch (error) {
      console.error('Error processing batch:', error);
      errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    } finally {
      isProcessing = false;
    }
  }
</script>

<Modal
  {show}
  title="Batch Process Files"
  size="lg"
  onClose={handleClose}
>
  <div class="p-4">
    {#if processingResult}
      <AlertMessage
        type={processingResult.success ? "success" : "error"}
        message={processingResult.message}
        class="mb-4"
      />

      {#if processingResult.success && processingResult.downloadUrl}
        <div class="flex flex-col items-center mt-4">
          {#if processingResult.fileCount !== undefined && processingResult.fileSize !== undefined}
            <p class="text-sm text-gray-600 dark:text-gray-300 mb-2">
              ZIP file contains {processingResult.fileCount} files ({(processingResult.fileSize / 1024).toFixed(2)} KB)
            </p>
          {/if}
          <a
            href={processingResult.downloadUrl}
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none"
            download
          >
            Download Text Files
          </a>
        </div>
      {:else if processingResult.success && !processingResult.downloadUrl}
        <div class="flex flex-col items-center mt-4">
          <p class="text-sm text-gray-600 dark:text-gray-300 mb-2">
            Processing completed, but no download URL was provided.
          </p>
        </div>
      {/if}

      <div class="flex justify-end mt-4">
        <Button onClick={handleClose}>Close</Button>
      </div>
    {:else}
      <div class="space-y-4">
        <div>
          <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">
            Upload a ZIP archive containing student submissions:
          </p>
        </div>

        <div
          role="button"
          tabindex="0"
          aria-label="Drop files here or click to upload"
          class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center"
          ondragover={(e) => {
            e.preventDefault();
            e.stopPropagation();
            e.currentTarget.classList.add('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/10');
          }}
          ondragleave={(e) => {
            e.preventDefault();
            e.stopPropagation();
            e.currentTarget.classList.remove('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/10');
          }}
          ondrop={(e) => {
            e.preventDefault();
            e.stopPropagation();
            e.currentTarget.classList.remove('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/10');

            if (e.dataTransfer?.files && e.dataTransfer.files.length > 0) {
              const file = e.dataTransfer.files[0];
              const fileExt = file.name.split('.').pop()?.toLowerCase();

              if (fileExt === 'zip' || fileExt === 'rar' || fileExt === '7z') {
                selectedFile = file;
                errorMessage = '';
              } else {
                errorMessage = 'Only ZIP, RAR, or 7Z archives are supported';
              }
            }
          }}
          onkeydown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              fileInput?.click();
            }
          }}
          onclick={() => fileInput?.click()}
        >
          <input
            type="file"
            id="file-upload"
            class="hidden"
            accept=".zip,.rar,.7z"
            onchange={handleFileChange}
            bind:this={fileInput}
          />

          <div class="space-y-2">
            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            </svg>

            <div class="flex justify-center text-sm text-gray-600 dark:text-gray-300">
              <label
                for="file-upload"
                class="relative cursor-pointer rounded-md font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 focus-within:outline-none"
              >
                <span>Upload a file</span>
              </label>
            </div>

            <p class="text-xs text-gray-500 dark:text-gray-400">
              ZIP, RAR, or 7Z up to 50MB
            </p>
          </div>

          {#if selectedFile}
            <div class="mt-4 text-sm text-gray-900 dark:text-gray-100">
              Selected: {selectedFile.name} ({(selectedFile.size / (1024 * 1024)).toFixed(2)} MB)
            </div>
          {/if}
        </div>

        {#if errorMessage}
          <AlertMessage
            type="error"
            message={errorMessage}
            class="mt-2"
          />
        {/if}

        <div class="flex justify-end mt-4">
          <Button
            onClick={handleClose}
            variant="light"
            class="mr-2"
          >
            Cancel
          </Button>

          <Button
            onClick={handleProcess}
            variant="primary"
            disabled={isProcessButtonDisabled}
          >
            {isProcessing ? 'Processing...' : 'Process Files'}
          </Button>
        </div>
      </div>
    {/if}
  </div>
</Modal>
