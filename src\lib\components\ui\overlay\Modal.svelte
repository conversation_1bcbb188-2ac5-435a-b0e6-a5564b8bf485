<script lang="ts">
  import { fade } from 'svelte/transition';
  import { browser } from '$app/environment';

  const {
    show = false,
    title = '',
    size = 'md',
    closeOnEscape = true,
    closeOnBackdropClick = true,
    onClose = () => {},
    children
  } = $props<{
    show: boolean,
    title?: string,
    size?: 'sm' | 'md' | 'lg' | 'xl' | 'full',
    closeOnEscape?: boolean,
    closeOnBackdropClick?: boolean,
    onClose?: () => void,
    children: any
  }>();

  const sizeClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    full: 'max-w-screen-xl max-h-screen'
  };

  function handleClose() {
    onClose();
  }

  function handleBackdropClick(event: MouseEvent) {
    if (closeOnBackdropClick && event.target === event.currentTarget) {
      handleClose();
    }
  }

  function handleKeydown(event: KeyboardEvent) {
    if (closeOnEscape && event.key === 'Escape' && show) {
      handleClose();
    }
  }

  // Modern effect pattern for keyboard and body overflow handling
  $effect(() => {
    if (!browser) return;

    // Handle body overflow
    document.body.style.overflow = show ? 'hidden' : '';

    // Handle keyboard events
    if (show) {
      window.addEventListener('keydown', handleKeydown);
      return () => {
        window.removeEventListener('keydown', handleKeydown);
        document.body.style.overflow = '';
      };
    }
  });
</script>

{#if show}
  <div class="fixed inset-0 z-50 flex items-center justify-center overflow-hidden" transition:fade={{ duration: 200 }}>
    <div class="fixed inset-0 bg-black bg-opacity-50" onclick={handleBackdropClick} tabindex="-1" role="presentation"></div>

    <div class="relative w-full {sizeClasses[size as keyof typeof sizeClasses]} p-6 mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-xl z-10"
         role="dialog" aria-modal="true" aria-labelledby="modal-title">

      {#if title}
        <div class="flex justify-between items-center mb-4">
          <h3 id="modal-title" class="text-lg font-medium text-gray-900 dark:text-white">{title}</h3>

          <button
            class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none"
            onclick={handleClose}
            aria-label="Close modal"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      {/if}

      <div>
        {@render children()}
      </div>
    </div>
  </div>
{/if}
