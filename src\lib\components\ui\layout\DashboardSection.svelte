<script lang="ts">
  type ColsOption = '1' | '2' | '3' | '4';
  type GapOption = '4' | '6' | '8';

  const {
    cols = '1',
    gap = '6',
    class: className = '',
    marginBottom = true,
    children
  } = $props<{
    cols?: ColsOption,
    gap?: GapOption,
    class?: string,
    marginBottom?: boolean,
    children: any
  }>();
  function getColsClass(colsValue: ColsOption): string {
    switch(colsValue) {
      case '1': return 'grid-cols-1';
      case '2': return 'grid-cols-1 md:grid-cols-2';
      case '3': return 'grid-cols-1 md:grid-cols-3';
      case '4': return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4';
      default: return 'grid-cols-1';
    }
  }

  function getGapClass(gapValue: GapOption): string {
    switch(gapValue) {
      case '4': return 'gap-4';
      case '6': return 'gap-6';
      case '8': return 'gap-8';
      default: return 'gap-6';
    }
  }

  const sectionClass = $derived(`
    grid ${getColsClass(cols as ColsOption)} ${getGapClass(gap as GapOption)}
    ${marginBottom ? 'mb-6' : ''}
    ${className}
  `);
</script>

<div class={sectionClass}>
  {@render children()}
</div>
