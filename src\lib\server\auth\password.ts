import * as argon2 from '@node-rs/argon2';
import crypto from 'crypto';
import { hibpService, type HIBPResponse } from '$lib/server/security/hibpService';

const ARGON2ID = 2;

// Enhanced Argon2 configuration for better security
const CONFIG = {
  memoryCost: parseInt(process.env.ARGON2_MEMORY_COST || '') || 65536, // Increased from 19456 to 64MB
  timeCost: parseInt(process.env.ARGON2_TIME_COST || '') || 3, // Increased from 2 to 3 iterations
  outputLen: parseInt(process.env.ARGON2_OUTPUT_LENGTH || '') || 32,
  parallelism: parseInt(process.env.ARGON2_PARALLELISM || '') || 1,
  saltLength: parseInt(process.env.ARGON2_SALT_LENGTH || '') || 32 // Increased from 16 to 32 bytes
};

// Minimum delay for password verification to prevent timing attacks
const MIN_VERIFICATION_TIME_MS = 100;

export async function hashPassword(password: string): Promise<string> {
  const salt = crypto.randomBytes(CONFIG.saltLength);

  return argon2.hash(password, {
    memoryCost: CONFIG.memoryCost,
    timeCost: CONFIG.timeCost,
    parallelism: CONFIG.parallelism,
    outputLen: CONFIG.outputLen,
    salt: salt,
    algorithm: ARGON2ID
  });
}

/**
 * Verifies password with timing attack protection
 */
export async function verifyPassword(hash: string, password: string): Promise<boolean> {
  const startTime = Date.now();

  try {
    // Perform password verification
    const isValid = await argon2.verify(hash, password);

    // Ensure minimum verification time to prevent timing attacks
    const elapsedTime = Date.now() - startTime;
    if (elapsedTime < MIN_VERIFICATION_TIME_MS) {
      await new Promise(resolve => setTimeout(resolve, MIN_VERIFICATION_TIME_MS - elapsedTime));
    }

    return isValid;
  } catch (error) {
    console.error('Password verification error:', error);

    // Ensure consistent timing even on error
    const elapsedTime = Date.now() - startTime;
    if (elapsedTime < MIN_VERIFICATION_TIME_MS) {
      await new Promise(resolve => setTimeout(resolve, MIN_VERIFICATION_TIME_MS - elapsedTime));
    }

    return false;
  }
}

/**
 * Validates password strength according to security policy (synchronous checks only)
 */
export function validatePasswordStrength(password: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }

  if (password.length > 255) {
    errors.push('Password must not exceed 255 characters');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/[0-9]/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (!/[^a-zA-Z0-9]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  // Check for common patterns
  const commonPatterns = [
    /(.)\1{2,}/, // Repeated characters (aaa, 111, etc.)
    /123|234|345|456|567|678|789|890/, // Sequential numbers
    /abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz/i, // Sequential letters
    /password|123456|qwerty|admin|login|user/i // Common weak passwords
  ];

  for (const pattern of commonPatterns) {
    if (pattern.test(password)) {
      errors.push('Password contains common patterns and is too predictable');
      break;
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Comprehensive password validation including HIBP breach check
 */
export async function validatePasswordComprehensive(password: string): Promise<{
  isValid: boolean;
  errors: string[];
  warnings: string[];
  hibpResult?: HIBPResponse;
}> {
  const warnings: string[] = [];

  // First, run synchronous validation
  const basicValidation = validatePasswordStrength(password);

  // If basic validation fails, don't bother with HIBP check
  if (!basicValidation.isValid) {
    return {
      isValid: false,
      errors: basicValidation.errors,
      warnings
    };
  }

  // Check against Have I Been Pwned database
  let hibpResult: HIBPResponse | undefined;
  try {
    hibpResult = await hibpService.checkPassword(password);

    if (hibpResult.isCompromised) {
      basicValidation.errors.push(
        hibpResult.breachCount && hibpResult.breachCount > 1
          ? `This password has been found in ${hibpResult.breachCount} data breaches and is not secure`
          : 'This password has been found in a data breach and is not secure'
      );
    } else if (hibpResult.error) {
      // HIBP service had an error, but we don't fail validation
      warnings.push(`Password breach check unavailable: ${hibpResult.error}`);
    } else if (hibpResult.fromCache) {
      warnings.push('Password checked against cached breach data');
    }
  } catch (error) {
    // HIBP check failed, but we don't fail validation
    warnings.push(`Password breach check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return {
    isValid: basicValidation.errors.length === 0,
    errors: basicValidation.errors,
    warnings,
    hibpResult
  };
}

/**
 * Validates password for registration with comprehensive checks
 */
export async function validatePasswordForRegistration(password: string): Promise<{
  isValid: boolean;
  message: string;
  details?: {
    errors: string[];
    warnings: string[];
    hibpChecked: boolean;
  };
}> {
  const validation = await validatePasswordComprehensive(password);

  if (!validation.isValid) {
    return {
      isValid: false,
      message: validation.errors[0] || 'Password does not meet security requirements',
      details: {
        errors: validation.errors,
        warnings: validation.warnings,
        hibpChecked: !!validation.hibpResult
      }
    };
  }

  // Password is valid, but check for warnings
  if (validation.warnings.length > 0) {
    return {
      isValid: true,
      message: 'Password accepted with warnings',
      details: {
        errors: [],
        warnings: validation.warnings,
        hibpChecked: !!validation.hibpResult
      }
    };
  }

  return {
    isValid: true,
    message: 'Password meets all security requirements'
  };
}

/**
 * Validates password for password reset with comprehensive checks
 */
export async function validatePasswordForReset(password: string, userId: string): Promise<{
  isValid: boolean;
  message: string;
  details?: {
    errors: string[];
    warnings: string[];
    hibpChecked: boolean;
  };
}> {
  // For password reset, we use the same validation as registration
  // In the future, we could add additional checks like:
  // - Ensuring the new password is different from the old one
  // - Checking against password history

  return validatePasswordForRegistration(password);
}

/**
 * Gets password validation configuration for frontend
 */
export function getPasswordValidationConfig(): {
  minLength: number;
  maxLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  hibpEnabled: boolean;
} {
  return {
    minLength: 8,
    maxLength: 255,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    hibpEnabled: process.env.HIBP_ENABLED === 'true'
  };
}
