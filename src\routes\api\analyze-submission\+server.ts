import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import { getAbsoluteFilePath } from '$lib/server/storage/fileStorage';
import fs from 'fs';
import crypto from 'crypto';
import type { ExtractedTable } from '$lib/types';
import { analyzePdfContent } from '$lib/server/api/claude-api';
import { extractTextFromPDF, filterPages } from '$lib/server/pdf/pdfAnalysis';
import { successResponse, errorResponse, forbiddenResponse, unauthorizedResponse, notFoundResponse } from '$lib/server/api/responseUtils';

export const POST: RequestHandler = async ({ request, locals }) => {
  const { user } = locals;
  if (!user) return unauthorizedResponse();

  try {
    const { submissionId, manualTableData } = await request.json();
    if (!submissionId) return errorResponse('Submission ID is required');

    // Get submission
    const [submission] = await db.select().from(table.submission).where(eq(table.submission.id, submissionId));
    if (!submission) return notFoundResponse('Submission not found');

    // Handle manual data submission (students only)
    if (manualTableData) {
      if (user.role !== 'student') return forbiddenResponse('Only students can submit manual data');
      if (submission.studentId !== user.id) return forbiddenResponse('You can only analyze your own submissions');

      if (manualTableData.depth && manualTableData.time) {
        // Validate manual data
        if (!Array.isArray(manualTableData.depth) || !Array.isArray(manualTableData.time))
          return errorResponse('Invalid manual table data format');

        if (manualTableData.depth.length !== manualTableData.time.length)
          return errorResponse('Depth and time arrays must have the same length');

        if (manualTableData.depth.length === 0)
          return errorResponse('Manual table data cannot be empty');

        // Check for 3+ identical time values
        const timeCounts = new Map<number, number>();
        for (const time of manualTableData.time)
          timeCounts.set(time, (timeCounts.get(time) || 0) + 1);

        if ([...timeCounts.values()].some(count => count >= 3))
          return errorResponse('The table has 3 or more identical time values, which is invalid');

        // Create table from manual data
        const manualTable: ExtractedTable = {
          name: 'Manual Entry',
          depth: manualTableData.depth,
          time: manualTableData.time
        };

        try {
          const [existingAnalysis] = await db.select().from(table.complexityAnalysis)
            .where(eq(table.complexityAnalysis.submissionId, submission.id));

          if (existingAnalysis) {
            await db.update(table.complexityAnalysis)
              .set({ tables: JSON.stringify([manualTable]) })
              .where(eq(table.complexityAnalysis.id, existingAnalysis.id));
          } else {
            await db.insert(table.complexityAnalysis).values({
              id: crypto.randomUUID(),
              submissionId: submission.id,
              tables: JSON.stringify([manualTable]),
              includedPages: JSON.stringify([]),
              excludedPages: JSON.stringify([])
            });
          }

          await db.update(table.submission)
            .set({ hasComplexityAnalysis: true })
            .where(eq(table.submission.id, submission.id));

          return successResponse({
            tables: [manualTable],
            includedPages: [],
            excludedPages: []
          }, 'Manual complexity analysis saved');
        } catch (error) {
          console.error('Error saving manual table data:', error);
          return errorResponse('Failed to save manual table data', 500);
        }
      }
    }

    // Return existing analysis if available
    if (submission.hasComplexityAnalysis && !manualTableData) {
      const [existingAnalysis] = await db.select().from(table.complexityAnalysis)
        .where(eq(table.complexityAnalysis.submissionId, submissionId));

      if (existingAnalysis) {
        console.log('Found existing complexity analysis for submission:', submissionId);

        try {
          const tables = JSON.parse(existingAnalysis.tables);
          const includedPages = JSON.parse(existingAnalysis.includedPages);
          const excludedPages = existingAnalysis.excludedPages ? JSON.parse(existingAnalysis.excludedPages) : [];

          console.log('Parsed tables from database:', tables);

          return successResponse({
            tables,
            includedPages,
            excludedPages
          }, 'Complexity analysis already exists');
        } catch (parseError) {
          console.error('Error parsing complexity analysis data:', parseError);
        }
      }
    }

    try {
      const absoluteFilePath = getAbsoluteFilePath(submission.filePath);
      if (!fs.existsSync(absoluteFilePath)) return notFoundResponse('Submission file not found');
      if (!submission.originalFilename.toLowerCase().endsWith('.pdf'))
        return errorResponse('Only PDF files can be analyzed');

      console.log(`Extracting text from PDF: ${absoluteFilePath}`);
      const extractedPages = await extractTextFromPDF(absoluteFilePath);
      const processedPages = filterPages(extractedPages);
      const includedPages = processedPages.filter(page => !page.isExcluded);
      const excludedPages = processedPages.filter(page => page.isExcluded);

      let extractedTables: ExtractedTable[] = [];
      if (includedPages.length > 0) {
        try {
          const pdfBuffer = fs.readFileSync(absoluteFilePath);
          const includedPageNumbers = includedPages.map(page => page.pageNumber);
          console.log(`Sending ${includedPageNumbers.length} filtered pages to Claude API: ${includedPageNumbers.join(', ')}`);
          const analysisResult = await analyzePdfContent(pdfBuffer, extractedPages.length, includedPageNumbers);
          extractedTables = analysisResult.tables || [];
        } catch (error: any) {
          console.warn('Error using Claude API:', error?.message || 'Unknown error');
        }
      }

      await db.insert(table.complexityAnalysis).values({
        id: crypto.randomUUID(),
        submissionId: submission.id,
        tables: JSON.stringify(extractedTables),
        includedPages: JSON.stringify(includedPages),
        excludedPages: JSON.stringify(excludedPages)
      });

      await db.update(table.submission)
        .set({ hasComplexityAnalysis: true })
        .where(eq(table.submission.id, submission.id));

      return successResponse({
        tables: extractedTables,
        includedPages,
        excludedPages
      }, 'Complexity analysis completed');
    } catch (pdfError: any) {
      console.error('Error processing PDF file:', pdfError);

      const emptyTable: ExtractedTable = {
        name: 'Error Processing PDF',
        depth: [],
        time: []
      };

      await db.insert(table.complexityAnalysis).values({
        id: crypto.randomUUID(),
        submissionId: submission.id,
        tables: JSON.stringify([emptyTable]),
        includedPages: JSON.stringify([]),
        excludedPages: JSON.stringify([])
      });

      await db.update(table.submission)
        .set({ hasComplexityAnalysis: true })
        .where(eq(table.submission.id, submission.id));

      return errorResponse('Error processing PDF file. Please use manual table entry instead.', 500);
    }
  } catch (err: any) {
    console.error('Error analyzing submission:', err);
    return errorResponse(err.message || 'An error occurred during analysis', 500);
  }
};
