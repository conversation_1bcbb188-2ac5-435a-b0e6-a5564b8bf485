<script lang="ts">
  import { enhance } from '$app/forms';
  import {
    Card,
    Countdown,
    Button,
    PageHeader,
    FormInput,
    FormSelect,
    Modal,
    BackButton,
    AlertMessage
  } from '$lib/components/ui';

  let { data, form } = $props();

  let isEditingGroup = $state(false);
  let groupName = $state(data.group.name);
  let groupDescription = $state(data.group.description || '');

  let newLectureTime = $state({
    weekday: 'monday',
    startTime: '',
    endTime: '',
    location: ''
  });

  import type { SimpleUser, SimpleLectureTime } from '$lib/types';

  let selectedStudents = $state<string[]>([]);
  let selectedManagers = $state<string[]>([]);
  let selectedProject = $state('');

  let studentSearchTerm = $state('');
  let managerSearchTerm = $state('');
  let filteredStudents = $state<SimpleUser[]>(data.availableStudents);
  let filteredLecturers = $state<SimpleUser[]>(data.availableLecturers);

  // Filter students based on search
  $effect(() => {
    if (!studentSearchTerm.trim()) {
      filteredStudents = data.availableStudents;
    } else {
      const term = studentSearchTerm.toLowerCase().trim();
      filteredStudents = data.availableStudents.filter((student: SimpleUser) =>
        (student.name || '').toLowerCase().includes(term) ||
        student.username.toLowerCase().includes(term) ||
        student.email.toLowerCase().includes(term)
      );
    }
  });

  // Filter lecturers based on search
  $effect(() => {
    if (!managerSearchTerm.trim()) {
      filteredLecturers = data.availableLecturers;
    } else {
      const term = managerSearchTerm.toLowerCase().trim();
      filteredLecturers = data.availableLecturers.filter((lecturer: SimpleUser) =>
        (lecturer.name || '').toLowerCase().includes(term) ||
        lecturer.username.toLowerCase().includes(term) ||
        lecturer.email.toLowerCase().includes(term)
      );
    }
  });

  // Constants for UI limiting
  const SCROLL_THRESHOLD = 5;

  function formatDate(dateString: string | Date): string {
    return new Date(dateString).toLocaleDateString();
  }

  function formatTime(timeString: string): string {
    if (!timeString) return '';
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  }

  function getWeekdayName(weekday: string): string {
    const weekdays = {
      monday: 'Monday',
      tuesday: 'Tuesday',
      wednesday: 'Wednesday',
      thursday: 'Thursday',
      friday: 'Friday',
      saturday: 'Saturday',
      sunday: 'Sunday'
    };
    return weekdays[weekday as keyof typeof weekdays] || weekday;
  }

  function handleSubmit() {
    return async ({ result, update }: { result: any; update: () => void }) => {
      update();

      if (result.type === 'success' && result.data?.success) {
        window.location.reload();
      }
    };
  }
</script>

<div>
  <div class="flex justify-between items-start mb-6">
    <div>
      <PageHeader
        title={data.group.name}
        subtitle={data.group.description || undefined}
        class="mb-0"
      />

      <!-- Join code  -->
      {#if data.group.joinEnabled && data.group.joinCode && data.group.joinCodeExpiry && new Date(data.group.joinCodeExpiry) > new Date()}
        <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <div class="flex justify-between items-center">
            <div>
              <h3 class="text-sm font-medium text-blue-800">Group Join Code</h3>
              <p class="text-2xl font-bold font-mono mt-1">{data.group.joinCode}</p>
              <p class="text-xs text-blue-600 mt-1">
                Expires in: <Countdown deadline={data.group.joinCodeExpiry} warningThreshold={5} />
              </p>
            </div>
            <form method="POST" action="?/disableJoinCode" use:enhance={handleSubmit}>
              <Button
                type="submit"
                variant="danger"
                size="sm"
              >
                Disable Code
              </Button>
            </form>
          </div>
          <p class="text-sm text-blue-700 mt-2">
            Students can use this code to join the group. The code will expire in 15 minutes or when you disable it.
          </p>
        </div>
      {:else}
        <div class="mt-4">
          <form method="POST" action="?/generateJoinCode" use:enhance={handleSubmit}>
            <Button
              type="submit"
              variant="primary"
              size="md"
            >
              Generate Join Code
            </Button>
          </form>
        </div>
      {/if}
    </div>
    <div class="flex space-x-2">
      <Button
        onClick={() => isEditingGroup = true}
        size="md"
      >
        Edit Group
      </Button>
      <BackButton href="/dashboard/lecturer/groups" label="Back to Groups" />
    </div>
  </div>

  <Modal
    show={isEditingGroup}
    title="Edit Group"
    onClose={() => isEditingGroup = false}
    size="md"
  >
    <form method="POST" action="?/updateGroup" use:enhance={handleSubmit} class="space-y-4">
      <FormInput
        id="name"
        name="name"
        label="Group Name"
        value={groupName}
        onChange={(e: Event) => groupName = (e.target as HTMLInputElement).value}
        required
      />

      <div>
        <label for="description" class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-2">
          Description (Optional)
        </label>
        <textarea
          id="description"
          name="description"
          bind:value={groupDescription}
          rows="3"
          class="appearance-none relative block w-full px-4 py-3 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white dark:bg-gray-700 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 text-base"
        ></textarea>
      </div>

      <div class="flex justify-end space-x-3 pt-4">
        <Button
          onClick={() => isEditingGroup = false}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="primary"
        >
          Save Changes
        </Button>
      </div>
    </form>
  </Modal>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <Card title="Students" subtitle={`${data.groupMembers.length} student${data.groupMembers.length !== 1 ? 's' : ''}`}>
      <div class="mb-4">
        <form method="POST" action="?/addStudents" use:enhance={handleSubmit} class="space-y-4">
          <div>
            <label for="students" class="block text-sm font-medium text-gray-700">Add Students</label>

            <!-- Search input for students -->
            <div class="mt-1 mb-2">
              <input
                type="text"
                placeholder="Search students by name, username or email..."
                bind:value={studentSearchTerm}
                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
            </div>

            <div class="mt-1 flex rounded-md shadow-sm">
              <select
                id="students"
                name="students[]"
                multiple
                bind:value={selectedStudents}
                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                size={filteredStudents.length > 0 ? Math.min(6, filteredStudents.length) : 3}
              >
                {#each filteredStudents as student}
                  <option value={student.id}>{student.name || student.username} ({student.email})</option>
                {/each}

                {#if filteredStudents.length === 0}
                  <option disabled>No matching students found</option>
                {/if}
              </select>
              <Button
                type="submit"
                variant="primary"
                size="sm"
                class="ml-3"
              >
                Add
              </Button>
            </div>

            {#if filteredStudents.length > 0 && studentSearchTerm.trim() !== ''}
              <p class="mt-1 text-xs text-gray-500">Showing {filteredStudents.length} matching students</p>
            {/if}
          </div>
        </form>
      </div>

      {#if data.groupMembers.length === 0}
        <p class="text-sm text-gray-500 italic">No students in this group yet.</p>
      {:else}
        <div class="{data.groupMembers.length > SCROLL_THRESHOLD ? 'max-h-60' : 'max-h-fit'} overflow-y-auto" style="{data.groupMembers.length > SCROLL_THRESHOLD ? 'height: 240px;' : ''}">
          <ul class="divide-y divide-gray-200">
            {#each data.groupMembers as member}
              <li class="py-3 pr-4 flex justify-between items-center">
                <div>
                  <p class="text-sm font-medium text-gray-900">{member.student.name || member.student.username}</p>
                  <p class="text-xs text-gray-500">{member.student.email}</p>
                  <p class="text-xs text-gray-400">Added: {formatDate(member.addedAt)}</p>
                </div>
                <form method="POST" action="?/removeStudent" use:enhance={handleSubmit}>
                  <input type="hidden" name="membershipId" value={member.id} />
                  <Button
                    type="submit"
                    variant="danger"
                    size="sm"
                    class="text-xs"
                  >
                    Remove
                  </Button>
                </form>
              </li>
            {/each}
          </ul>
        </div>
      {/if}
    </Card>

    <Card title="Co-managers" subtitle={`${data.groupManagers.length} manager${data.groupManagers.length !== 1 ? 's' : ''}`}>
      <div class="mb-4">
        <form method="POST" action="?/addManagers" use:enhance={handleSubmit} class="space-y-4">
          <div>
            <label for="lecturers" class="block text-sm font-medium text-gray-700">Add Co-managers</label>

            <!-- Search input for co-managers -->
            <div class="mt-1 mb-2">
              <input
                type="text"
                placeholder="Search lecturers by name, username or email..."
                bind:value={managerSearchTerm}
                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
            </div>

            <div class="mt-1 flex rounded-md shadow-sm">
              <select
                id="lecturers"
                name="lecturers[]"
                multiple
                bind:value={selectedManagers}
                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                size={filteredLecturers.length > 0 ? Math.min(6, filteredLecturers.length) : 3}
              >
                {#each filteredLecturers as lecturer}
                  <option value={lecturer.id}>{lecturer.name || lecturer.username} ({lecturer.email})</option>
                {/each}

                {#if filteredLecturers.length === 0}
                  <option disabled>No matching lecturers found</option>
                {/if}
              </select>
              <Button
                type="submit"
                variant="primary"
                size="sm"
                class="ml-3"
              >
                Add
              </Button>
            </div>

            {#if filteredLecturers.length > 0 && managerSearchTerm.trim() !== ''}
              <p class="mt-1 text-xs text-gray-500">Showing {filteredLecturers.length} matching lecturers</p>
            {/if}
          </div>
        </form>
      </div>

      {#if data.groupManagers.length === 0}
        <p class="text-sm text-gray-500 italic">No co-managers for this group yet.</p>
      {:else}
        <div class="{data.groupManagers.length > SCROLL_THRESHOLD ? 'max-h-60' : 'max-h-fit'} overflow-y-auto" style="{data.groupManagers.length > SCROLL_THRESHOLD ? 'height: 240px;' : ''}">
          <ul class="divide-y divide-gray-200">
            {#each data.groupManagers as manager}
              <li class="py-3 pr-4 flex justify-between items-center">
                <div>
                  <p class="text-sm font-medium text-gray-900">{manager.lecturer.name || manager.lecturer.username}</p>
                  <p class="text-xs text-gray-500">{manager.lecturer.email}</p>
                  <p class="text-xs text-gray-400">Added: {formatDate(manager.addedAt)}</p>
                </div>
                <form method="POST" action="?/removeManager" use:enhance={handleSubmit}>
                  <input type="hidden" name="managershipId" value={manager.id} />
                  <Button
                    type="submit"
                    variant="danger"
                    size="sm"
                    class="text-xs"
                  >
                    Remove
                  </Button>
                </form>
              </li>
            {/each}
          </ul>
        </div>
      {/if}
    </Card>
  </div>

  <Card title="Lecture Schedule" class="mb-6">
    <div class="mb-4">
      <form method="POST" action="?/addLectureTime" use:enhance={handleSubmit} class="space-y-4">
        <table class="w-full border-collapse">
          <thead>
            <tr>
              <th class="p-2 text-left text-sm font-medium text-gray-700">Day</th>
              <th class="p-2 text-left text-sm font-medium text-gray-700">Start Time</th>
              <th class="p-2 text-left text-sm font-medium text-gray-700">End Time</th>
              <th class="p-2 text-left text-sm font-medium text-gray-700">Location</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="p-2">
                <FormSelect
                  id="weekday"
                  name="weekday"
                  value={newLectureTime.weekday}
                  onChange={(e: Event) => newLectureTime.weekday = (e.target as HTMLSelectElement).value}
                  options={[
                    { value: 'monday', label: 'Monday' },
                    { value: 'tuesday', label: 'Tuesday' },
                    { value: 'wednesday', label: 'Wednesday' },
                    { value: 'thursday', label: 'Thursday' },
                    { value: 'friday', label: 'Friday' },
                    { value: 'saturday', label: 'Saturday' },
                    { value: 'sunday', label: 'Sunday' }
                  ]}
                />
              </td>

              <td class="p-2">
                <input
                  type="time"
                  id="startTime"
                  name="startTime"
                  bind:value={newLectureTime.startTime}
                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  required
                />
              </td>

              <td class="p-2">
                <input
                  type="time"
                  id="endTime"
                  name="endTime"
                  bind:value={newLectureTime.endTime}
                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  required
                />
              </td>

              <td class="p-2">
                <input
                  type="text"
                  id="location"
                  name="location"
                  bind:value={newLectureTime.location}
                  placeholder="Room number, building, etc."
                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                />
              </td>

              <td class="p-2">
                <Button
                  type="submit"
                  variant="primary"
                  size="md"
                >
                  Add
                </Button>
              </td>
            </tr>
          </tbody>
        </table>
      </form>
    </div>

    <div class="mt-6">
      {#if data.lectureTimes.length === 0}
        <p class="text-sm text-gray-500 italic">No lecture times scheduled yet.</p>
      {:else}
        <div class="overflow-x-auto">
          <div class="min-w-full">
            <table class="w-full border-collapse">
              <thead>
                <tr>
                  {#each ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] as day}
                    <th class="p-2 text-center font-medium bg-gray-100 border border-gray-200">{day}</th>
                  {/each}
                </tr>
              </thead>
              <tbody>
                <tr>
                  {#each ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] as day}
                    <td class="p-2 border border-gray-200 align-top min-h-[120px] h-[120px] min-w-[120px] w-[120px] relative">
                      {#each data.lectureTimes.filter((lt: SimpleLectureTime) => lt.weekday === day) as lecture}
                        <div class="mb-2 p-2 bg-blue-50 border border-blue-100 rounded text-xs">
                          <div class="flex justify-between items-start">
                            <div>
                              <p class="font-medium">{formatTime(lecture.startTime)} - {formatTime(lecture.endTime)}</p>
                              {#if lecture.location}
                                <p class="text-gray-600">{lecture.location}</p>
                              {/if}
                            </div>
                            <form method="POST" action="?/removeLectureTime" use:enhance={handleSubmit}>
                              <input type="hidden" name="lectureTimeId" value={lecture.id} />
                              <Button
                                type="submit"
                                variant="danger"
                                size="sm"
                                class="p-0 w-5 h-5 min-w-0 min-h-0"
                              >
                                ×
                              </Button>
                            </form>
                          </div>
                        </div>
                      {/each}
                    </td>
                  {/each}
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="mt-6">
          <h3 class="text-sm font-medium text-gray-700 mb-2">All Scheduled Times</h3>
          <ul class="divide-y divide-gray-200">
            {#each data.lectureTimes as lecture}
              <li class="py-3 flex justify-between items-center">
                <div>
                  <p class="text-sm font-medium text-gray-900">{getWeekdayName(lecture.weekday)}</p>
                  <p class="text-xs text-gray-500">{formatTime(lecture.startTime)} - {formatTime(lecture.endTime)}</p>
                  {#if lecture.location}
                    <p class="text-xs text-gray-500">Location: {lecture.location}</p>
                  {/if}
                </div>
                <form method="POST" action="?/removeLectureTime" use:enhance={handleSubmit}>
                  <input type="hidden" name="lectureTimeId" value={lecture.id} />
                  <Button
                    type="submit"
                    variant="danger"
                    size="sm"
                    class="text-xs"
                  >
                    Remove
                  </Button>
                </form>
              </li>
            {/each}
          </ul>
        </div>
      {/if}
    </div>
  </Card>

  <Card title="Associated Projects">
    {#if data.projectGroups.length === 0}
      <p class="text-sm text-gray-500 italic">No projects associated with this group yet.</p>
    {:else}
      <ul class="divide-y divide-gray-200">
        {#each data.projectGroups as projectGroup}
          <li class="py-3 flex justify-between items-center">
            <div>
              <p class="text-sm font-medium text-gray-900">{projectGroup.project.name}</p>
              {#if projectGroup.project.description}
                <p class="text-xs text-gray-500">{projectGroup.project.description}</p>
              {/if}
            </div>
            <div class="flex space-x-2">
              <Button
                variant="light"
                size="sm"
                onClick={() => window.location.href = `/dashboard/lecturer/projects/${projectGroup.projectId}`}
              >
                View Project
              </Button>
            </div>
          </li>
        {/each}
      </ul>
    {/if}
  </Card>

  {#if form?.message}
    <AlertMessage
      type={form.success ? "success" : "error"}
      message={form.message}
      class="mt-4"
    />
  {/if}
</div>
