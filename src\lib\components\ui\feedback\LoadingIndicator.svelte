<script lang="ts">
  import { fade } from 'svelte/transition';
  import { loading } from '$lib/stores/loading';

  let {
    size = 'md',
    fullScreen = false,
    message = 'Loading...',
    showSpinner = true,
    overlay = true,
    zIndex = 50
  } = $props<{
    size?: 'sm' | 'md' | 'lg',
    fullScreen?: boolean,
    message?: string,
    showSpinner?: boolean,
    overlay?: boolean,
    zIndex?: number
  }>();

  const sizeClasses = {
    sm: 'w-6 h-6 border-2',
    md: 'w-10 h-10 border-3',
    lg: 'w-16 h-16 border-4'
  };
  const spinnerClass = $derived(sizeClasses[size as keyof typeof sizeClasses] || sizeClasses.md);
  const containerClass = $derived(`
    flex items-center justify-center
    ${fullScreen ? 'fixed inset-0' : 'absolute inset-0'}
  `);
</script>

{#if $loading}
  <div
    transition:fade={{ duration: 150 }}
    class={containerClass}
    style="z-index: {zIndex};"
  >
    {#if overlay}
      <div class="absolute inset-0 bg-white dark:bg-gray-900 opacity-75"></div>
    {/if}

    <div class="relative flex flex-col items-center justify-center h-full">
      {#if showSpinner}
        <div class={`${spinnerClass} mb-3 rounded-full border-gray-200 dark:border-gray-700 border-solid animate-spin`}
             style="border-top-color: #3b82f6;"></div>
      {/if}

      {#if message}
        <p class="text-gray-700 dark:text-gray-300 font-medium">{message}</p>
      {/if}
    </div>
  </div>
{/if}
