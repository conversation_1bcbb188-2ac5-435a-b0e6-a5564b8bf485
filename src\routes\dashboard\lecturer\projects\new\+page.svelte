<script lang="ts">
  import { enhance } from '$app/forms';
  import {
    Button,
    FormInput,
    FormLayout,
    AlertMessage,
    PageHeader
  } from '$lib/components/ui';

  import type { GroupStudent, Student } from '$lib/types';

  let { data, form } = $props();

  let projectName = $state('');
  let projectDescription = $state('');
  let maxAttempts = $state(0); // unlimited attempts
  let deadline = $state(''); // no deadline
  let selectedGroups = $state<string[]>([]);
  let selectedStudents = $state<string[]>([]);
  let isSubmitting = $state(false);
  let showGroupMembers = $state<Record<string, boolean>>({});

  function toggleGroupMembers(groupId: string) {
    showGroupMembers[groupId] = !showGroupMembers[groupId];
    showGroupMembers = {...showGroupMembers};
  }

  function toggleStudentSelection(studentId: string) {
    if (selectedStudents.includes(studentId)) {
      selectedStudents = selectedStudents.filter(id => id !== studentId);
    } else {
      selectedStudents = [...selectedStudents, studentId];
    }
  }

  function handleGroupSelection(e: Event, groupId: string) {
    const checkbox = e.target as HTMLInputElement;

    if (checkbox.checked) {
      if (!selectedGroups.includes(groupId)) {
        selectedGroups = [...selectedGroups, groupId];
      }

      const groupStudents = data.groupStudents.find((gs: GroupStudent) => gs.groupId === groupId)?.students || [];
      const studentIds = groupStudents.map((s: Student) => s.id);
      const newStudentIds = studentIds.filter((id: string) => !selectedStudents.includes(id));

      if (newStudentIds.length > 0) {
        selectedStudents = [...selectedStudents, ...newStudentIds];
      }
    } else {
      selectedGroups = selectedGroups.filter(id => id !== groupId);

      const groupStudents = data.groupStudents.find((gs: GroupStudent) => gs.groupId === groupId)?.students || [];
      const studentIds = new Set(groupStudents.map((s: Student) => s.id));
      selectedStudents = selectedStudents.filter((id: string) => !studentIds.has(id));
    }
  }

  function areAllStudentsSelected(groupId: string): boolean {
    const groupStudents = data.groupStudents.find((gs: GroupStudent) => gs.groupId === groupId)?.students || [];
    if (groupStudents.length === 0) return false;

    return groupStudents.every((student: Student) => selectedStudents.includes(student.id));
  }

  function areAnyStudentsSelected(groupId: string): boolean {
    const groupStudents = data.groupStudents.find((gs: GroupStudent) => gs.groupId === groupId)?.students || [];
    if (groupStudents.length === 0) return false;

    return groupStudents.some((student: Student) => selectedStudents.includes(student.id));
  }

  $effect(() => {
    if (form?.message && 'data' in form) {
      const formData = form.data as any;
      projectName = formData.name || '';
      projectDescription = formData.description || '';
      maxAttempts = formData.maxAttempts || 0;
      deadline = formData.deadline || '';
      selectedGroups = formData.selectedGroups || [];
      selectedStudents = formData.selectedStudents || [];
    }
  });
</script>

<div>
  <PageHeader title="Create New Project" />

  {#if form?.success}
    <AlertMessage
      type="success"
      message="Project created successfully!"
      actionText="View project details"
      actionHref={`/dashboard/lecturer/projects/${form.projectId}`}
    />
  {/if}

  <FormLayout title="Project Details">
    <form
      method="POST"
      action="?/createProject"
      use:enhance={() => {
        isSubmitting = true;
        return ({ update }) => {
          isSubmitting = false;
          update();
        };
      }}
      class="space-y-4"
    >
      <FormInput
        id="name"
        name="name"
        label="Project Name"
        value={projectName}
        onChange={(e: Event) => projectName = (e.target as HTMLInputElement).value}
        required
      />

      <div>
        <label for="description" class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-2">
          Project Description <span class="text-gray-500">(optional)</span>
        </label>
        <textarea
          id="description"
          name="description"
          value={projectDescription}
          oninput={(e) => projectDescription = e.currentTarget.value}
          rows="4"
          class="appearance-none relative block w-full px-4 py-3 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white dark:bg-gray-700 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 text-base"
        ></textarea>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormInput
          id="maxAttempts"
          name="maxAttempts"
          type="number"
          label="Max Submission Attempts"
          helpText="0 = unlimited"
          value={maxAttempts.toString()}
          onChange={(e: Event) => maxAttempts = parseInt((e.target as HTMLInputElement).value) || 0}
          min={0}
        />

        <FormInput
          id="deadline"
          name="deadline"
          type="datetime-local"
          label="Submission Deadline"
          helpText="Optional"
          value={deadline}
          onChange={(e: Event) => deadline = (e.target as HTMLInputElement).value}
        />
      </div>

      <div class="mt-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Assign Groups and Students</h3>

        {#if data.groups.length === 0}
          <p class="text-sm text-gray-500 italic">No groups available. <a href="/dashboard/lecturer/groups/new" class="text-blue-600 hover:text-blue-800">Create a group</a> first.</p>
        {:else}
          <div class="space-y-4 max-h-80 overflow-y-auto border border-gray-200 rounded-md p-4">
            {#each data.groups as group}
              <div class="border border-gray-200 rounded-md p-3">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <input
                      type="checkbox"
                      id={`group-${group.id}`}
                      name="groups[]"
                      value={group.id}
                      checked={areAllStudentsSelected(group.id)}
                      indeterminate={!areAllStudentsSelected(group.id) && areAnyStudentsSelected(group.id)}
                      onclick={(e) => handleGroupSelection(e, group.id)}
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label for={`group-${group.id}`} class="ml-3 block text-sm font-medium text-gray-700">
                      {group.name}
                    </label>
                  </div>
                  <button
                    type="button"
                    class="text-sm text-blue-600 hover:text-blue-800"
                    onclick={() => toggleGroupMembers(group.id)}
                  >
                    {showGroupMembers[group.id] ? 'Hide Students' : 'Show Students'}
                  </button>
                </div>

                {#if group.description}
                  <p class="text-sm text-gray-500 ml-7 mt-1">{group.description}</p>
                {/if}

                {#if showGroupMembers[group.id]}
                  <div class="mt-3 ml-7 pl-3 border-l-2 border-gray-200">
                    {#if true}
                      {@const groupStudentsList = data.groupStudents.find((gs: GroupStudent) => gs.groupId === group.id)?.students || []}
                      {@const selectedCount = selectedStudents.filter((id: string) =>
                        groupStudentsList.some((s: Student) => s.id === id)
                      ).length}

                      <p class="text-sm font-medium text-gray-700 mb-2">
                        Students in this group:
                        <span class="text-xs text-gray-500">
                          ({selectedCount} of {groupStudentsList.length} selected)
                        </span>
                        {#if selectedCount === groupStudentsList.length && groupStudentsList.length > 0}
                          <span class="ml-1 px-1.5 py-0.5 text-xs font-medium bg-green-100 text-green-800 rounded-full">All selected</span>
                        {:else if selectedCount > 0}
                          <span class="ml-1 px-1.5 py-0.5 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">Partial</span>
                        {/if}
                      </p>

                      {#if groupStudentsList.length === 0}
                        <p class="text-sm text-gray-500 italic">No students in this group.</p>
                      {:else}
                        <div class="space-y-2 max-h-60 overflow-y-auto">
                          <div class="mb-2">
                            <Button
                              variant="light"
                              size="sm"
                              onClick={() => {
                                groupStudentsList.forEach((student: Student) => {
                                  if (!selectedStudents.includes(student.id)) {
                                    selectedStudents = [...selectedStudents, student.id];
                                  }
                                });
                              }}
                              class="mr-2"
                            >
                              Select All
                            </Button>
                            <Button
                              variant="light"
                              size="sm"
                              onClick={() => {
                                selectedStudents = selectedStudents.filter((id: string) =>
                                  !groupStudentsList.some((s: Student) => s.id === id)
                                );
                              }}
                            >
                              Deselect All
                            </Button>
                          </div>

                          {#each groupStudentsList as student}
                            <div class="flex items-center">
                              <input
                                type="checkbox"
                                id={`student-${student.id}`}
                                name="students[]"
                                value={student.id}
                                checked={selectedStudents.includes(student.id)}
                                onclick={() => toggleStudentSelection(student.id)}
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <label for={`student-${student.id}`} class="ml-3 block text-sm text-gray-700">
                                {student.name || student.username}
                                <span class="text-xs text-gray-500 ml-1">({student.email})</span>
                              </label>
                            </div>
                          {/each}
                        </div>
                      {/if}
                    {/if}
                  </div>
                {/if}
              </div>
            {/each}
          </div>
        {/if}
      </div>

      {#if form?.message && !form?.success}
        <AlertMessage
          type="error"
          message={form.message}
        />
      {/if}

      <div class="mt-6 flex items-center justify-end">
        <Button
          variant="light"
          onClick={() => window.location.href = '/dashboard/lecturer/projects'}
          class="mr-4"
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="primary"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Creating...' : 'Create Project'}
        </Button>
      </div>
    </form>
  </FormLayout>
</div>