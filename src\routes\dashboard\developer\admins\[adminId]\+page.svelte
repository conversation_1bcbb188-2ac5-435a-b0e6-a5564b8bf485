<script lang="ts">
  import { enhance } from '$app/forms';
  import type { PageData } from './$types';
  import {
    PageHeader,
    Card,
    Button,
    FormInput,
    FormSelect,
    AlertMessage,
    BackButton
  } from '$lib/components/ui';

  let { data, form } = $props<{ data: PageData, form?: any }>();

  function formatDate(dateString: string | Date | null | undefined): string {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString();
  }
</script>

<div>
  <div class="mb-6 flex items-center space-x-3">
    <BackButton href="/dashboard/developer/admins" label="Back to Admins" />
    <PageHeader title="Edit Admin: {data.admin.username}" class="mb-0" />
  </div>

  {#if form?.success}
    <AlertMessage
      type="success"
      message={form.message}
      class="mb-6"
    />
  {:else if form?.message}
    <AlertMessage
      type="error"
      message={form.message}
      class="mb-6"
    />
  {/if}

  <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
    <div class="bg-white rounded-lg shadow-sm p-6">
      <h2 class="text-xl font-semibold mb-4">Admin Information</h2>

      <form method="POST" action="?/updateAdmin" use:enhance class="space-y-6">
        <div class="space-y-4">
          <div>
            <label for="username" class="block text-sm font-medium text-gray-700 mb-1">Username</label>
            <input
              type="text"
              id="username"
              value={data.admin.username}
              disabled
              class="block w-full rounded-md border-gray-300 bg-gray-100 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
            <p class="mt-1 text-xs text-gray-500">Username cannot be changed</p>
          </div>

          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              value={data.admin.email}
              required
              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>

          <div>
            <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Role</label>
            <input
              type="text"
              id="role"
              value="Admin"
              disabled
              class="block w-full rounded-md border-gray-300 bg-gray-100 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>

          <div>
            <label for="createdAt" class="block text-sm font-medium text-gray-700 mb-1">Created</label>
            <input
              type="text"
              id="createdAt"
              value={formatDate(data.admin.createdAt)}
              disabled
              class="block w-full rounded-md border-gray-300 bg-gray-100 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>

          <div>
            <label for="organization" class="block text-sm font-medium text-gray-700 mb-1">Current Organization</label>
            <input
              type="text"
              id="currentOrganization"
              value={data.adminOrganization ? data.adminOrganization.name : 'None'}
              disabled
              class="block w-full rounded-md border-gray-300 bg-gray-100 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>

          <div class="flex items-center">
            <input
              type="checkbox"
              id="isActive"
              name="isActive"
              checked={data.admin.isActive}
              class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label for="isActive" class="ml-2 block text-sm text-gray-700">
              Active
            </label>
          </div>
        </div>

        <div>
          <button
            type="submit"
            class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Update Admin
          </button>
        </div>
      </form>
    </div>

    <div class="bg-white rounded-lg shadow-sm p-6">
      <h2 class="text-xl font-semibold mb-4">Organization Assignment</h2>

      <form method="POST" action="?/updateOrganization" use:enhance class="space-y-6">
        <div class="space-y-4">
          <div>
            <label for="organization" class="block text-sm font-medium text-gray-700 mb-1">Organization</label>
            <select
              id="organization"
              name="organization"
              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            >
              <option value="">No Organization</option>
              {#each data.organizations as org}
                <option
                  value={org.id}
                  selected={data.admin.organization === org.id}
                  class={'text-gray-500'}
                >
                  {org.name}
                </option>
              {/each}
            </select>
            <p class="mt-1 text-xs text-gray-500">Changing the organization will affect which users the admin can manage</p>
          </div>

          {#if data.adminOrganization}
            <div>
              <p class="block text-sm font-medium text-gray-700 mb-1">Current Organization Status</p>
              <div class="flex space-x-2 mt-1">
                {#if data.adminOrganization.isActive !== undefined}
                  <span class={`px-2 py-1 inline-flex text-xs leading-4 font-semibold rounded-full ${data.adminOrganization.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {data.adminOrganization.isActive ? 'Active' : 'Inactive'}
                  </span>
                {/if}

                {#if data.adminOrganization.isFrozen !== undefined}
                  <span class={`px-2 py-1 inline-flex text-xs leading-4 font-semibold rounded-full ${data.adminOrganization.isFrozen ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`}>
                    {data.adminOrganization.isFrozen ? 'Frozen' : 'Not Frozen'}
                  </span>
                {/if}

                <span class="px-2 py-1 inline-flex text-xs leading-4 font-semibold rounded-full bg-blue-100 text-blue-800">
                  {data.adminOrganization.userCount || 0} users
                </span>
              </div>
            </div>
          {/if}
        </div>

        <div>
          <button
            type="submit"
            class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Update Organization
          </button>
        </div>
      </form>
    </div>

    {#if data.admin.organization && data.adminOrganization}
      <div class="bg-white rounded-lg shadow-sm p-6">
        <h2 class="text-xl font-semibold mb-4">Organization Information</h2>

        <div class="space-y-4">
          <div>
            <p class="block text-sm font-medium text-gray-700 mb-1">Organization</p>
            <p class="text-base">{data.adminOrganization.name}</p>
          </div>

          <div>
            <p class="block text-sm font-medium text-gray-700 mb-1">Users</p>
            <p class="text-base">{data.adminOrganization.userCount || 0}</p>
          </div>
        </div>

        <div class="mt-4">
          <a
            href="/dashboard/developer/organizations/{data.admin.organization}"
            class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Manage Organization
          </a>
        </div>
      </div>
    {/if}
  </div>

</div>