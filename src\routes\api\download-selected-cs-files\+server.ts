import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { eq, and, inArray } from 'drizzle-orm';
import JSZip from 'jszip';
import { fileDownloadResponse, unauthorizedResponse, forbiddenResponse, notFoundResponse, errorResponse } from '$lib/server/api/responseUtils';

export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    const { user } = locals;
    if (!user || user.role !== 'lecturer')
      return unauthorizedResponse('Only lecturers can download selected CS files');

    const { submissionIds } = await request.json();
    if (!submissionIds?.length) return errorResponse('Missing or invalid submission IDs');

    const submissions = await db.select({
      id: table.submission.id,
      projectId: table.submission.projectId,
      studentName: table.user.username
    })
    .from(table.submission)
    .innerJoin(table.user, eq(table.submission.studentId, table.user.id))
    .where(inArray(table.submission.id, submissionIds));

    if (!submissions.length) return notFoundResponse('No valid submissions found');
    const projectIds = [...new Set(submissions.map(s => s.projectId))];
    const projects = await db.select({
      id: table.project.id,
      name: table.project.name
    })
    .from(table.project)
    .where(and(
      inArray(table.project.id, projectIds),
      eq(table.project.createdBy, user.id)
    ));

    if (projects.length !== projectIds.length)
      return forbiddenResponse('You are not authorized to access one or more of these submissions');

    const csFiles = await db.select()
      .from(table.csFile)
      .where(inArray(table.csFile.submissionId, submissionIds));

    if (!csFiles.length) return notFoundResponse('No CS files found for the selected submissions');

    const zip = new JSZip();
    const submissionMap = new Map(submissions.map(s => [s.id, s]));
    csFiles.forEach(csFile => {
      const submission = submissionMap.get(csFile.submissionId);
      if (!submission) return;

      const folderName = `${submission.studentName}_${csFile.submissionId}`;
      let fileName = csFile.filePath ?
        csFile.filePath.replace(/\\/g, '/').replace(/^\/+|\/+$/g, '').replace(/\//g, '_').replace(/\.[^/.]+$/, "") :
        csFile.fileName.replace(/\.[^/.]+$/, "");

      const timestamp = new Date(csFile.createdAt).toISOString()
        .replace(/[-:]/g, '').replace('T', '_').split('.')[0];

      zip.file(`${folderName}/${fileName}_${timestamp}.txt`, csFile.fileContent);
    });
    const zipContent = await zip.generateAsync({ type: 'nodebuffer' });
    const date = new Date().toISOString().split('T')[0];
    const projectName = projects.length ?
      projects[0].name.replace(/[^a-zA-Z0-9]/g, "_").substring(0, 30) : "project";

    return fileDownloadResponse(zipContent, `project_csfiles_${projectName}_${date}.zip`, 'application/zip');
  } catch (error) {
    console.error('Error downloading selected CS files:', error);
    return errorResponse('Failed to download selected CS files', 500);
  }
};
