<script lang="ts">
  const {
    title = '',
    value = '',
    href = '',
    linkText = 'View all',
    class: className = '',
    children
  } = $props<{
    title: string,
    value: string | number,
    href?: string,
    linkText?: string,
    class?: string,
    children?: any
  }>();
  const cardClass = $derived(`
    bg-white dark:bg-gray-800
    rounded-lg shadow-sm p-6
    border border-gray-200 dark:border-gray-700
    ${className}
  `);
</script>

<div class={cardClass}>
  <h2 class="text-xl font-semibold mb-4">{title}</h2>
  <div class="flex items-center justify-between">
    <div>
      <span class="text-3xl font-bold">{value}</span>
      {#if children}
        <div class="mt-1">
          {@render children()}
        </div>
      {/if}
    </div>
    {#if href}
      <a {href} class="text-blue-600 hover:text-blue-800 text-sm font-medium">
        {linkText}
      </a>
    {/if}
  </div>
</div>
