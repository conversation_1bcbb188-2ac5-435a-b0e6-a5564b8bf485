import type { Request<PERSON>and<PERSON> } from './$types';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import path from 'path';
import fs from 'fs';
import crypto from 'crypto';
import { extractCsFilesFromArchive, createCsFilesZip } from '$lib/server/storage/archiveHandler';
import { successResponse, errorResponse, forbiddenResponse } from '$lib/server/api/responseUtils';

export const POST: RequestHandler = async ({ request, locals }) => {
  const { user } = locals;
  if (!user || user.role !== 'lecturer')
    return forbiddenResponse('Only lecturers can process batch files');

  try {
    // Get and validate file
    const formData = await request.formData();
    const file = formData.get('file') as File;
    if (!file) return errorResponse('No file provided');

    const fileExt = path.extname(file.name).toLowerCase();
    if (!['.zip', '.rar', '.7z'].includes(fileExt))
      return errorResponse('Only ZIP, RAR, or 7Z archives are supported');

    // temp directory
    const tempDir = path.join(process.cwd(), 'temp');
    if (!fs.existsSync(tempDir)) fs.mkdirSync(tempDir, { recursive: true });

    // Extract CS files
    const buffer = Buffer.from(await file.arrayBuffer());
    let csFiles = [];
    try {
      csFiles = await extractCsFilesFromArchive(buffer, fileExt, user.id);
    } catch (error) {
      console.error('Error extracting archive:', error);
      return errorResponse('Failed to extract archive. File may be corrupted or unsupported.');
    }

    if (!csFiles.length)
      return errorResponse('No CS files found in the archive. Please check the file.');

    const batchId = crypto.randomUUID();
    const batchValues = csFiles.map(csFile => ({
      id: crypto.randomUUID(),
      fileName: csFile.name,
      filePath: csFile.path,
      fileContent: csFile.content,
      batchId,
      createdAt: new Date(),
      createdBy: user.id
    }));

    try {
      await db.insert(table.batchFile).values(batchValues);
    } catch (error) {
      console.error('Error inserting batch files:', error);
      return errorResponse('Failed to store files in the database.', 500);
    }

    const zipContent = await createCsFilesZip(csFiles);
    const zipFileName = `batch_cs_files_${new Date().toISOString().split('T')[0]}.zip`;
    const zipFilePath = path.join(tempDir, zipFileName);

    try {
      fs.writeFileSync(zipFilePath, zipContent);

      if (!fs.existsSync(zipFilePath)) {
        console.error('Failed to create ZIP file at path:', zipFilePath);
        return errorResponse('Failed to create downloadable ZIP file', 500);
      }

      const fileStats = fs.statSync(zipFilePath);
      if (fileStats.size === 0) {
        console.error('Created ZIP file is empty:', zipFilePath);
        return errorResponse('Generated ZIP file is empty', 500);
      }

      console.log(`Successfully created ZIP file: ${zipFilePath} (${fileStats.size} bytes)`);

      return successResponse({
        message: `Successfully processed ${csFiles.length} CS files.`,
        downloadUrl: `/api/batch-process/download/${zipFileName}`,
        fileCount: csFiles.length,
        fileSize: zipContent.length
      });
    } catch (error) {
      console.error('Error creating ZIP file:', error);
      return errorResponse('Failed to create downloadable ZIP file', 500);
    }
  } catch (error) {
    console.error('Error processing batch:', error);
    return errorResponse(error instanceof Error ? error.message : 'Unknown error', 500);
  }
};
