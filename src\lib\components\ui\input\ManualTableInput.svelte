<script lang="ts">
  import type { ExtractedTable } from '$lib/types';
  import { Button } from '$lib/components/ui';

  const { onSave } = $props<{
    onSave: (tables: ExtractedTable[]) => void
  }>();

  let tables = $state<ExtractedTable[]>([
    { name: "Table 1", time: [0], depth: [0] }
  ]);

  const MAX_TABLES = 3;
  const MAX_ROWS = 30;

  function addTable() {
    if (tables.length < MAX_TABLES) {
      tables = [...tables, {
        name: `Table ${tables.length + 1}`,
        time: [0],
        depth: [0]
      }];
    }
  }
  function removeTable(index: number) {
    tables = tables.filter((_, i) => i !== index);
  }

  function addRow(tableIndex: number) {
    if (tables[tableIndex].time.length < MAX_ROWS) {
      const updatedTables = [...tables];
      updatedTables[tableIndex] = {
        ...updatedTables[tableIndex],
        time: [...updatedTables[tableIndex].time, 0],
        depth: [...updatedTables[tableIndex].depth, 0]
      };
      tables = updatedTables;
    }
  }

  function removeRow(tableIndex: number, rowIndex: number) {
    const updatedTables = [...tables];
    updatedTables[tableIndex] = {
      ...updatedTables[tableIndex],
      time: updatedTables[tableIndex].time.filter((_, i) => i !== rowIndex),
      depth: updatedTables[tableIndex].depth.filter((_, i) => i !== rowIndex)
    };
    tables = updatedTables;
  }

  function updateValue(tableIndex: number, rowIndex: number, field: 'time' | 'depth', value: string) {
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      const updatedTables = [...tables];
      updatedTables[tableIndex] = {
        ...updatedTables[tableIndex]
      };
      updatedTables[tableIndex][field][rowIndex] = numValue;
      tables = updatedTables;
    }
  }

  function updateTableName(tableIndex: number, name: string) {
    const updatedTables = [...tables];
    updatedTables[tableIndex] = {
      ...updatedTables[tableIndex],
      name
    };
    tables = updatedTables;
  }

  // Save tables
  function saveTables() {
    // Filter out empty
    const validTables = tables.filter(table =>
      table.time.length > 0 &&
      table.depth.length > 0 &&
      table.time.length === table.depth.length
    );

    if (onSave) {
      onSave(validTables);
    }
  }
</script>

<div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
  <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Manual Table Input</h2>
  <p class="mb-4 text-gray-600 dark:text-gray-400">
    No tables were automatically detected. Please enter your time-depth data manually below.
    You can add up to {MAX_TABLES} tables with up to {MAX_ROWS} rows each.
  </p>

  <div class="space-y-8">
    {#each tables as table, tableIndex}
      <div class="border dark:border-gray-700 rounded-lg overflow-hidden">
        <div class="bg-gray-50 dark:bg-gray-700 px-4 py-2 border-b dark:border-gray-600 flex justify-between items-center">
          <input
            type="text"
            value={table.name}
            oninput={(e) => updateTableName(tableIndex, e.currentTarget.value)}
            class="font-medium bg-transparent border-0 focus:ring-0 p-0"
            placeholder="Table name"
          />

          {#if tables.length > 1}
            <button
              type="button"
              onclick={() => removeTable(tableIndex)}
              class="text-red-500 hover:text-red-700 dark:hover:text-red-400"
              aria-label="Remove table"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </button>
          {/if}
        </div>

        <div class="p-4 bg-white dark:bg-gray-800">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Time</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Depth</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-10"></th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {#each table.time as _, rowIndex}
                  <tr class={rowIndex % 2 === 0 ? 'bg-white dark:bg-gray-800' : 'bg-gray-50 dark:bg-gray-700'}>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <input
                        type="number"
                        step="any"
                        value={table.time[rowIndex]}
                        oninput={(e) => updateValue(tableIndex, rowIndex, 'time', e.currentTarget.value)}
                        class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <input
                        type="number"
                        step="any"
                        value={table.depth[rowIndex]}
                        oninput={(e) => updateValue(tableIndex, rowIndex, 'depth', e.currentTarget.value)}
                        class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <button
                        type="button"
                        onclick={() => removeRow(tableIndex, rowIndex)}
                        class="text-red-500 hover:text-red-700 dark:hover:text-red-400"
                        aria-label="Remove row"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                      </button>
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>

          {#if table.time.length < MAX_ROWS}
            <div class="mt-4">
              <button
                type="button"
                onclick={() => addRow(tableIndex)}
                class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                </svg>
                Add Row
              </button>
            </div>
          {/if}
        </div>
      </div>
    {/each}
  </div>

  <div class="mt-6 flex justify-between">
    {#if tables.length < MAX_TABLES}
      <button
        type="button"
        onclick={addTable}
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
        </svg>
        Add Table
      </button>
    {:else}
      <div></div>
    {/if}

    <Button type="button" variant="primary" onClick={saveTables}>
      Save Tables
    </Button>
  </div>
</div>
