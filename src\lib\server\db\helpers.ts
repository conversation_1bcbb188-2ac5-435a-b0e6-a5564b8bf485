import { count, eq, sql, desc, asc } from 'drizzle-orm';
import { db } from './index';
import * as table from './schema';
import type { SQL } from 'drizzle-orm';

export async function getById<T extends { id: string }>(
  tableName: keyof typeof table,
  id: string
): Promise<T | null> {
  if (!id) return null;

  const result = await db
    .select()
    .from(table[tableName] as any)
    .where(eq((table[tableName] as any).id, id))
    .limit(1);

  return result[0] as T || null;
}

export async function existsById(
  tableName: keyof typeof table,
  id: string
): Promise<boolean> {
  if (!id) return false;

  const [result] = await db
    .select({ exists: sql`1` })
    .from(table[tableName] as any)
    .where(eq((table[tableName] as any).id, id))
    .limit(1);

  return !!result?.exists;
}

export async function getCount(
  tableName: keyof typeof table,
  condition?: SQL<unknown>
): Promise<number> {
  const query = db
    .select({ count: count() })
    .from(table[tableName] as any);

  if (condition) {
    query.where(condition);
  }

  const [result] = await query;
  return result?.count || 0;
}

export async function batchInsert<T extends Record<string, any>>(
  tableName: keyof typeof table,
  records: T[]
): Promise<void> {
  if (!records?.length) return;
  await db.insert(table[tableName] as any).values(records);
}

export async function getPaginated<T>(
  tableName: keyof typeof table,
  options: {
    page?: number;
    limit?: number;
    condition?: SQL<unknown>;
    orderBy?: { column: string; direction: 'asc' | 'desc' };
    columns?: Record<string, any>;
  } = {}
): Promise<{ data: T[]; total: number; pages: number }> {
  const page = options.page || 1;
  const limit = options.limit || 20;
  const offset = (page - 1) * limit;

  const total = await getCount(tableName, options.condition);
  const pages = Math.ceil(total / limit);

  const query = db
    .select(options.columns || {})
    .from(table[tableName] as any)
    .limit(limit)
    .offset(offset);

  if (options.condition) {
    query.where(options.condition);
  }

  if (options.orderBy) {
    const direction = options.orderBy.direction === 'desc' ? desc : asc;
    query.orderBy(direction((table[tableName] as any)[options.orderBy.column]));
  }

  return {
    data: await query as T[],
    total,
    pages
  };
}
