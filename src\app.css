*, ::before, ::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
    "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  scroll-behavior: smooth;
  overflow-y: scroll;
}

body {
  margin: 0;
  line-height: inherit;
  background-color: #f9fafb;
  color: #1f2937;
}

/* Dark mode */
.dark body {
  background-color: #111827;
  color: #f3f4f6;
}

/* Dark mode borders */
.dark .border-gray-100 { border-color: #374151; }
.dark .border-gray-200 { border-color: #1f2937; }
.dark .border-gray-300 { border-color: #374151; }
.dark .border-gray-400 { border-color: #4b5563; }
.dark .border-gray-500 { border-color: #6b7280; }
.dark .border-gray-600 { border-color: #4b5563; }
.dark .border-gray-700 { border-color: #374151; }
.dark .border-gray-800 { border-color: #1f2937; }
.dark .border-indigo-200 { border-color: #6366f1; opacity: 0.3; }
.dark .border-indigo-300 { border-color: #6366f1; opacity: 0.4; }
.dark .border-indigo-700 { border-color: #4338ca; }

/* Dark mode text */
.dark .text-gray-50 { color: #f9fafb; }
.dark .text-gray-100 { color: #f3f4f6; }
.dark .text-gray-200 { color: #e5e7eb; }
.dark .text-gray-300 { color: #d1d5db; }
.dark .text-gray-400 { color: #9ca3af; }
.dark .text-gray-500 { color: #6b7280; }
.dark .text-gray-600 { color: #d1d5db; }
.dark .text-gray-700 { color: #e5e7eb; }
.dark .text-gray-800 { color: #f3f4f6; }
.dark .text-gray-900 { color: #f9fafb; }
.dark .text-indigo-300 { color: #a5b4fc; }
.dark .text-indigo-400 { color: #818cf8; }
.dark .text-indigo-700 { color: #6366f1; }
.dark .text-blue-300 { color: #93c5fd; }
.dark .text-blue-500 { color: #3b82f6; }
.dark .text-green-300 { color: #6ee7b7; }
.dark .text-green-500 { color: #10b981; }
.dark .text-red-400 { color: #f87171; }

/* Dark mode background */
.dark .bg-white { background-color: #1f2937; }
.dark .bg-gray-50 { background-color: #111827; }
.dark .bg-gray-100 { background-color: #1f2937; }
.dark .bg-gray-200 { background-color: #374151; }
.dark .bg-gray-600 { background-color: #4b5563; }
.dark .bg-gray-700 { background-color: #374151; }
.dark .bg-gray-800 { background-color: #1f2937; }
.dark .bg-gray-900 { background-color: #111827; }
.dark .bg-indigo-50 { background-color: #312e81; }
.dark .bg-indigo-100 { background-color: #3730a3; }
.dark .bg-indigo-900 { background-color: #312e81; }
.dark .bg-blue-50 { background-color: #1e3a8a; }
.dark .bg-blue-900 { background-color: #1e3a8a; }
.dark .bg-green-50 { background-color: #064e3b; }
.dark .bg-green-900 { background-color: #064e3b; }

/* Dark mode hover */
.dark .hover\:bg-gray-50:hover { background-color: #374151; }
.dark .hover\:bg-gray-100:hover { background-color: #4b5563; }
.dark .hover\:bg-gray-600:hover { background-color: #6b7280; }
.dark .hover\:bg-gray-700:hover { background-color: #4b5563; }
.dark .hover\:bg-gray-800:hover { background-color: #374151; }
.dark .hover\:bg-blue-100:hover { background-color: #1e40af; }
.dark .hover\:bg-green-100:hover { background-color: #065f46; }
.dark .hover\:text-gray-100:hover { color: #f3f4f6; }
.dark .hover\:text-gray-300:hover { color: #d1d5db; }
.dark .hover\:text-gray-500:hover { color: #9ca3af; }
.dark .hover\:text-white:hover { color: #ffffff; }
.dark .hover\:text-indigo-300:hover { color: #a5b4fc; }
.dark .hover\:text-indigo-500:hover { color: #6366f1; }

/* Dark mode ring focus */
.dark .focus\:ring-offset-gray-800:focus { --tw-ring-offset-color: #1f2937; }
.dark .focus\:ring-indigo-500:focus { --tw-ring-color: rgba(99, 102, 241, 0.5); }
.dark .focus\:ring-blue-500:focus { --tw-ring-color: rgba(59, 130, 246, 0.5); }
.dark .focus\:ring-green-500:focus { --tw-ring-color: rgba(16, 185, 129, 0.5); }

/* Dark mode input styles */
.dark input, .dark textarea, .dark select {
  background-color: #374151;
  border-color: #4b5563;
  color: #f3f4f6;
}

.dark input:focus, .dark textarea:focus, .dark select:focus {
  border-color: #818cf8;
  box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.2);
}

/* Dark mode button styles */
.dark button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Dark mode dropdown styles */
.dark .shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.15);
}

/* Dark mode transitions */
.dark .transition-colors, .transition-colors {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Link-style button */
.btn-link {
  background: transparent;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  text-decoration: none;
  display: inline;
}

/* Font family class */
.font-inter {
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

/* Remove text decoration for all links */
a {
  text-decoration: none;
}

/* Common utility classes - expanded for modern SaaS appearance */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.hidden { display: none; }
.block { display: block; }
.grid { display: grid; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }
.justify-start { justify-content: flex-start; }

.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }
.flex-grow { flex-grow: 1; }
.flex-grow-0 { flex-grow: 0; }
.flex-shrink { flex-shrink: 1; }
.flex-shrink-0 { flex-shrink: 0; }

.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }
.space-y-10 > * + * { margin-top: 2.5rem; }

.space-x-1 > * + * { margin-left: 0.25rem; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-x-8 > * + * { margin-left: 2rem; }

.divide-y > * + * { border-top-width: 1px; }
.divide-x > * + * { border-left-width: 1px; }
.divide-gray-200 > * + * { border-color: #e5e7eb; }
.first\:pt-0:first-child { padding-top: 0; }

/* Typography */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.text-6xl { font-size: 3.75rem; line-height: 1; }

.font-thin { font-weight: 100; }
.font-extralight { font-weight: 200; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

.tracking-tight { letter-spacing: -0.025em; }
.tracking-wide { letter-spacing: 0.025em; }
.leading-4 { line-height: 1rem; }
.leading-6 { line-height: 1.5rem; }
.leading-8 { line-height: 2rem; }

.uppercase { text-transform: uppercase; }
.capitalize { text-transform: capitalize; }
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Colors - expanded with modern SaaS palette */
.text-white { color: #ffffff; }
.text-black { color: #000000; }
.text-transparent { color: transparent; }

.text-gray-50 { color: #f9fafb; }
.text-gray-100 { color: #f3f4f6; }
.text-gray-200 { color: #e5e7eb; }
.text-gray-300 { color: #d1d5db; }
.text-gray-400 { color: #9ca3af; }
.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-gray-900 { color: #111827; }

.text-indigo-50 { color: #eef2ff; }
.text-indigo-100 { color: #e0e7ff; }
.text-indigo-200 { color: #c7d2fe; }
.text-indigo-300 { color: #a5b4fc; }
.text-indigo-400 { color: #818cf8; }
.text-indigo-500 { color: #6366f1; }
.text-indigo-600 { color: #4f46e5; }
.text-indigo-700 { color: #4338ca; }
.text-indigo-800 { color: #3730a3; }
.text-indigo-900 { color: #312e81; }

.text-blue-50 { color: #eff6ff; }
.text-blue-100 { color: #dbeafe; }
.text-blue-200 { color: #bfdbfe; }
.text-blue-300 { color: #93c5fd; }
.text-blue-400 { color: #60a5fa; }
.text-blue-500 { color: #3b82f6; }
.text-blue-600 { color: #2563eb; }
.text-blue-700 { color: #1d4ed8; }
.text-blue-800 { color: #1e40af; }
.text-blue-900 { color: #1e3a8a; }

.text-green-50 { color: #ecfdf5; }
.text-green-100 { color: #d1fae5; }
.text-green-200 { color: #a7f3d0; }
.text-green-300 { color: #6ee7b7; }
.text-green-400 { color: #34d399; }
.text-green-500 { color: #10b981; }
.text-green-600 { color: #059669; }
.text-green-700 { color: #047857; }
.text-green-800 { color: #065f46; }
.text-green-900 { color: #064e3b; }

.text-red-50 { color: #fef2f2; }
.text-red-100 { color: #fee2e2; }
.text-red-200 { color: #fecaca; }
.text-red-300 { color: #fca5a5; }
.text-red-400 { color: #f87171; }
.text-red-500 { color: #ef4444; }
.text-red-600 { color: #dc2626; }
.text-red-700 { color: #b91c1c; }
.text-red-800 { color: #991b1b; }
.text-red-900 { color: #7f1d1d; }

/* Background colors */
.bg-white { background-color: #ffffff; }
.bg-black { background-color: #000000; }
.bg-transparent { background-color: transparent; }

.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-300 { background-color: #d1d5db; }
.bg-gray-400 { background-color: #9ca3af; }
.bg-gray-500 { background-color: #6b7280; }
.bg-gray-600 { background-color: #4b5563; }
.bg-gray-700 { background-color: #374151; }
.bg-gray-800 { background-color: #1f2937; }
.bg-gray-900 { background-color: #111827; }

.bg-indigo-50 { background-color: #eef2ff; }
.bg-indigo-100 { background-color: #e0e7ff; }
.bg-indigo-200 { background-color: #c7d2fe; }
.bg-indigo-300 { background-color: #a5b4fc; }
.bg-indigo-400 { background-color: #818cf8; }
.bg-indigo-500 { background-color: #6366f1; }
.bg-indigo-600 { background-color: #4f46e5; }
.bg-indigo-700 { background-color: #4338ca; }
.bg-indigo-800 { background-color: #3730a3; }
.bg-indigo-900 { background-color: #312e81; }

.bg-blue-50 { background-color: #eff6ff; }
.bg-blue-100 { background-color: #dbeafe; }
.bg-blue-200 { background-color: #bfdbfe; }
.bg-blue-300 { background-color: #93c5fd; }
.bg-blue-400 { background-color: #60a5fa; }
.bg-blue-500 { background-color: #3b82f6; }
.bg-blue-600 { background-color: #2563eb; }
.bg-blue-700 { background-color: #1d4ed8; }
.bg-blue-800 { background-color: #1e40af; }
.bg-blue-900 { background-color: #1e3a8a; }

.bg-green-50 { background-color: #ecfdf5; }
.bg-green-100 { background-color: #d1fae5; }
.bg-green-200 { background-color: #a7f3d0; }
.bg-green-300 { background-color: #6ee7b7; }
.bg-green-400 { background-color: #34d399; }
.bg-green-500 { background-color: #10b981; }
.bg-green-600 { background-color: #059669; }
.bg-green-700 { background-color: #047857; }
.bg-green-800 { background-color: #065f46; }
.bg-green-900 { background-color: #064e3b; }

.bg-red-50 { background-color: #fef2f2; }
.bg-red-100 { background-color: #fee2e2; }
.bg-red-200 { background-color: #fecaca; }
.bg-red-300 { background-color: #fca5a5; }
.bg-red-400 { background-color: #f87171; }
.bg-red-500 { background-color: #ef4444; }
.bg-red-600 { background-color: #dc2626; }
.bg-red-700 { background-color: #b91c1c; }
.bg-red-800 { background-color: #991b1b; }
.bg-red-900 { background-color: #7f1d1d; }

.bg-purple-50 { background-color: #f5f3ff; }
.bg-purple-100 { background-color: #ede9fe; }
.bg-purple-200 { background-color: #ddd6fe; }
.bg-purple-300 { background-color: #c4b5fd; }
.bg-purple-400 { background-color: #a78bfa; }
.bg-purple-500 { background-color: #8b5cf6; }
.bg-purple-600 { background-color: #7c3aed; }
.bg-purple-700 { background-color: #6d28d9; }
.bg-purple-800 { background-color: #5b21b6; }
.bg-purple-900 { background-color: #4c1d95; }

/* Border styles */
.border { border-width: 1px; }
.border-0 { border-width: 0px; }
.border-2 { border-width: 2px; }
.border-4 { border-width: 4px; }
.border-t { border-top-width: 1px; }
.border-b { border-bottom-width: 1px; }
.border-l { border-left-width: 1px; }
.border-r { border-right-width: 1px; }
.border-l-4 { border-left-width: 4px; }

.border-transparent { border-color: transparent; }
.border-gray-100 { border-color: #f3f4f6; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-gray-400 { border-color: #9ca3af; }
.border-indigo-300 { border-color: #a5b4fc; }
.border-indigo-500 { border-color: #6366f1; }
.border-blue-500 { border-color: #3b82f6; }
.border-green-200 { border-color: #a7f3d0; }
.border-green-400 { border-color: #34d399; }
.border-green-500 { border-color: #10b981; }
.border-red-500 { border-color: #ef4444; }

.border-solid { border-style: solid; }
.border-dashed { border-style: dashed; }

/* Padding and margins */
.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.p-10 { padding: 2.5rem; }
.p-12 { padding: 3rem; }

.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: 0.25rem; padding-right: 0.25rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-2\.5 { padding-left: 0.625rem; padding-right: 0.625rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-5 { padding-left: 1.25rem; padding-right: 1.25rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.px-10 { padding-left: 2.5rem; padding-right: 2.5rem; }

.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-0\.5 { padding-top: 0.125rem; padding-bottom: 0.125rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-5 { padding-top: 1.25rem; padding-bottom: 1.25rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-10 { padding-top: 2.5rem; padding-bottom: 2.5rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }
.py-20 { padding-top: 5rem; padding-bottom: 5rem; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 0.25rem; }
.pt-2 { padding-top: 0.5rem; }
.pt-3 { padding-top: 0.75rem; }
.pt-4 { padding-top: 1rem; }
.pt-5 { padding-top: 1.25rem; }
.pt-6 { padding-top: 1.5rem; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 0.25rem; }
.pb-2 { padding-bottom: 0.5rem; }
.pb-3 { padding-bottom: 0.75rem; }
.pb-4 { padding-bottom: 1rem; }
.pb-5 { padding-bottom: 1.25rem; }
.pb-6 { padding-bottom: 1.5rem; }
.pb-8 { padding-bottom: 2rem; }
.pb-16 { padding-bottom: 4rem; }
.pb-20 { padding-bottom: 5rem; }
.pb-28 { padding-bottom: 7rem; }
.pb-32 { padding-bottom: 8rem; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 0.25rem; }
.pl-2 { padding-left: 0.5rem; }
.pl-3 { padding-left: 0.75rem; }
.pl-4 { padding-left: 1rem; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 0.25rem; }
.pr-2 { padding-right: 0.5rem; }
.pr-3 { padding-right: 0.75rem; }
.pr-4 { padding-right: 1rem; }

.m-0 { margin: 0; }
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 0.75rem; }
.m-4 { margin: 1rem; }
.m-5 { margin: 1.25rem; }
.m-6 { margin: 1.5rem; }
.m-8 { margin: 2rem; }
.m-auto { margin: auto; }

.mx-auto { margin-left: auto; margin-right: auto; }
.mx-0 { margin-left: 0; margin-right: 0; }
.mx-1 { margin-left: 0.25rem; margin-right: 0.25rem; }
.mx-2 { margin-left: 0.5rem; margin-right: 0.5rem; }
.mx-3 { margin-left: 0.75rem; margin-right: 0.75rem; }
.mx-4 { margin-left: 1rem; margin-right: 1rem; }

.my-0 { margin-top: 0; margin-bottom: 0; }
.my-1 { margin-top: 0.25rem; margin-bottom: 0.25rem; }
.my-2 { margin-top: 0.5rem; margin-bottom: 0.5rem; }
.my-3 { margin-top: 0.75rem; margin-bottom: 0.75rem; }
.my-4 { margin-top: 1rem; margin-bottom: 1rem; }
.my-6 { margin-top: 1.5rem; margin-bottom: 1.5rem; }
.my-8 { margin-top: 2rem; margin-bottom: 2rem; }
.my-10 { margin-top: 2.5rem; margin-bottom: 2.5rem; }
.my-12 { margin-top: 3rem; margin-bottom: 3rem; }
.my-16 { margin-top: 4rem; margin-bottom: 4rem; }
.my-24 { margin-top: 6rem; margin-bottom: 6rem; }
.my-32 { margin-top: 8rem; margin-bottom: 8rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-5 { margin-top: 1.25rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.mt-10 { margin-top: 2.5rem; }
.mt-12 { margin-top: 3rem; }
.mt-16 { margin-top: 4rem; }
.mt-20 { margin-top: 5rem; }
.mt-24 { margin-top: 6rem; }
.mt-28 { margin-top: 7rem; }
.mt-32 { margin-top: 8rem; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-5 { margin-bottom: 1.25rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-10 { margin-bottom: 2.5rem; }
.mb-12 { margin-bottom: 3rem; }
.mb-16 { margin-bottom: 4rem; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.ml-4 { margin-left: 1rem; }
.ml-5 { margin-left: 1.25rem; }
.ml-6 { margin-left: 1.5rem; }
.ml-8 { margin-left: 2rem; }
.ml-10 { margin-left: 2.5rem; }
.ml-12 { margin-left: 3rem; }
.ml-16 { margin-left: 4rem; }
.ml-auto { margin-left: auto; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.mr-4 { margin-right: 1rem; }
.mr-5 { margin-right: 1.25rem; }
.mr-6 { margin-right: 1.5rem; }
.mr-8 { margin-right: 2rem; }
.mr-auto { margin-right: auto; }

.-ml-1 { margin-left: -0.25rem; }
.-mr-1 { margin-right: -0.25rem; }
.-mt-1 { margin-top: -0.25rem; }
.-mb-1 { margin-bottom: -0.25rem; }

/* Width and height */
.w-0 { width: 0; }
.w-1 { width: 0.25rem; }
.w-2 { width: 0.5rem; }
.w-3 { width: 0.75rem; }
.w-4 { width: 1rem; }
.w-5 { width: 1.25rem; }
.w-6 { width: 1.5rem; }
.w-8 { width: 2rem; }
.w-10 { width: 2.5rem; }
.w-12 { width: 3rem; }
.w-16 { width: 4rem; }
.w-24 { width: 6rem; }
.w-32 { width: 8rem; }
.w-48 { width: 12rem; }
.w-64 { width: 16rem; }
.w-auto { width: auto; }
.w-full { width: 100%; }
.w-screen { width: 100vw; }
.w-1\/2 { width: 50%; }
.w-1\/3 { width: 33.333333%; }
.w-2\/3 { width: 66.666667%; }
.w-1\/4 { width: 25%; }
.w-3\/4 { width: 75%; }
.w-1\/5 { width: 20%; }
.w-2\/5 { width: 40%; }
.w-3\/5 { width: 60%; }
.w-4\/5 { width: 80%; }

.h-0 { height: 0; }
.h-1 { height: 0.25rem; }
.h-2 { height: 0.5rem; }
.h-3 { height: 0.75rem; }
.h-4 { height: 1rem; }
.h-5 { height: 1.25rem; }
.h-6 { height: 1.5rem; }
.h-8 { height: 2rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-16 { height: 4rem; }
.h-24 { height: 6rem; }
.h-32 { height: 8rem; }
.h-48 { height: 12rem; }
.h-56 { height: 14rem; }
.h-64 { height: 16rem; }
.h-auto { height: auto; }
.h-full { height: 100%; }
.h-screen { height: 100vh; }

.max-w-xs { max-width: 20rem; }
.max-w-sm { max-width: 24rem; }
.max-w-md { max-width: 28rem; }
.max-w-lg { max-width: 32rem; }
.max-w-xl { max-width: 36rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-3xl { max-width: 48rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-5xl { max-width: 64rem; }
.max-w-6xl { max-width: 72rem; }
.max-w-7xl { max-width: 80rem; }
.max-w-full { max-width: 100%; }

.min-h-screen { min-height: 100vh; }
.min-w-full { min-width: 100%; }

/* Position */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }
.static { position: static; }

.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.inset-y-0 { top: 0; bottom: 0; }

.top-0 { top: 0; }
.top-24 { top: 6rem; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }

.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* Shadows */
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
.shadow-inner { box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06); }
.shadow-none { box-shadow: none; }

/* Rounded corners */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: 0.125rem; }
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-3xl { border-radius: 1.5rem; }
.rounded-full { border-radius: 9999px; }

.rounded-t-md { border-top-left-radius: 0.375rem; border-top-right-radius: 0.375rem; }
.rounded-b-md { border-bottom-left-radius: 0.375rem; border-bottom-right-radius: 0.375rem; }

/* Grid */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-5 { gap: 1.25rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }
.gap-10 { gap: 2.5rem; }
.gap-12 { gap: 3rem; }

.gap-x-1 { column-gap: 0.25rem; }
.gap-x-2 { column-gap: 0.5rem; }
.gap-x-3 { column-gap: 0.75rem; }
.gap-x-4 { column-gap: 1rem; }
.gap-x-5 { column-gap: 1.25rem; }
.gap-x-6 { column-gap: 1.5rem; }
.gap-x-8 { column-gap: 2rem; }
.gap-x-10 { column-gap: 2.5rem; }
.gap-x-12 { column-gap: 3rem; }

.gap-y-1 { row-gap: 0.25rem; }
.gap-y-2 { row-gap: 0.5rem; }
.gap-y-3 { row-gap: 0.75rem; }
.gap-y-4 { row-gap: 1rem; }
.gap-y-5 { row-gap: 1.25rem; }
.gap-y-6 { row-gap: 1.5rem; }
.gap-y-8 { row-gap: 2rem; }
.gap-y-10 { row-gap: 2.5rem; }
.gap-y-12 { row-gap: 3rem; }

/* Transforms */
.transform { transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.translate-x-1\/2 { --tw-translate-x: 50%; }
.translate-y-1\/2 { --tw-translate-y: 50%; }
.translate-x-full { --tw-translate-x: 100%; }
.translate-y-full { --tw-translate-y: 100%; }
.-translate-x-1\/2 { --tw-translate-x: -50%; }
.-translate-y-1\/2 { --tw-translate-y: -50%; }

/* Overflow */
.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }

/* Interactivity */
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }
.cursor-default { cursor: default; }

.focus\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px var(--tw-ring-color, rgba(59, 130, 246, 0.5)); }
.focus\:ring-offset-2:focus { box-shadow: 0 0 0 2px white, 0 0 0 4px var(--tw-ring-color, rgba(59, 130, 246, 0.5)); }
.focus\:ring-blue-500:focus { --tw-ring-color: rgba(59, 130, 246, 0.5); }
.focus\:ring-indigo-500:focus { --tw-ring-color: rgba(99, 102, 241, 0.5); }
.focus\:ring-green-500:focus { --tw-ring-color: rgba(16, 185, 129, 0.5); }
.focus\:border-blue-500:focus { border-color: #3b82f6; }
.focus\:border-indigo-500:focus { border-color: #6366f1; }

.ring-1 { box-shadow: 0 0 0 1px var(--tw-ring-color); }
.ring-2 { box-shadow: 0 0 0 2px var(--tw-ring-color); }
.ring-inset { box-shadow: inset 0 0 0 1px var(--tw-ring-color); }
.ring-gray-300 { --tw-ring-color: rgba(209, 213, 219, 0.5); }

/* Hover states */
.hover\:bg-gray-50:hover { background-color: #f9fafb; }
.hover\:bg-gray-100:hover { background-color: #f3f4f6; }
.hover\:bg-blue-700:hover { background-color: #1d4ed8; }
.hover\:bg-indigo-50:hover { background-color: #eef2ff; }
.hover\:bg-indigo-700:hover { background-color: #4338ca; }
.hover\:bg-green-700:hover { background-color: #047857; }
.hover\:bg-red-700:hover { background-color: #b91c1c; }

.hover\:text-gray-500:hover { color: #6b7280; }
.hover\:text-gray-700:hover { color: #374151; }
.hover\:text-gray-800:hover { color: #1f2937; }
.hover\:text-blue-500:hover { color: #3b82f6; }
.hover\:text-indigo-500:hover { color: #6366f1; }
.hover\:text-green-500:hover { color: #10b981; }
.hover\:text-red-900:hover { color: #7f1d1d; }

.hover\:border-gray-300:hover { border-color: #d1d5db; }
.hover\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

/* Group hover */
.group:hover .group-hover\:text-gray-500 { color: #6b7280; }

/* Transitions */
.transition { transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-colors { transition-property: background-color, border-color, color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-100 { transition-duration: 100ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }

/* Animations */
.animate-spin { animation: spin 1s linear infinite; }
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Opacity */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* Form specifics */
input, textarea, select {
  border-width: 1px;
  border-color: #d1d5db;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Utilities for SaaS styling */
.aspect-video { aspect-ratio: 16 / 9; }
.object-cover { object-fit: cover; }
.object-contain { object-fit: contain; }

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Placeholder */
.placeholder-gray-400::placeholder { color: #9ca3af; }
.placeholder-gray-500::placeholder { color: #6b7280; }

/* Appearance */
.appearance-none { appearance: none; }

/* Container queries - md: 768px, lg: 1024px, sm: 640px */
@media (min-width: 640px) {
  .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .sm\:py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
  .sm\:p-6 { padding: 1.5rem; }
  .sm\:mt-5 { margin-top: 1.25rem; }
  .sm\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .sm\:text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
  .sm\:text-center { text-align: center; }
  .sm\:max-w-xl { max-width: 36rem; }
  .sm\:mx-auto { margin-left: auto; margin-right: auto; }
  .sm\:w-auto { width: auto; }
  .sm\:flex { display: flex; }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .sm\:mt-0 { margin-top: 0; }
  .sm\:ml-3 { margin-left: 0.75rem; }
}

@media (min-width: 768px) {
  .md\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .md\:py-8 { padding-top: 2rem; padding-bottom: 2rem; }
  .md\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .md\:text-6xl { font-size: 3.75rem; line-height: 1; }
  .md\:flex { display: flex; }
  .md\:hidden { display: none; }
  .md\:w-64 { width: 16rem; }
  .md\:mr-8 { margin-right: 2rem; }
  .md\:ml-4 { margin-left: 1rem; }
  .md\:flex-row { flex-direction: row; }
  .md\:flex-col { flex-direction: column; }
  .md\:items-center { align-items: center; }
  .md\:space-y-0 { --space-y-reverse: 0; margin-top: calc(0px * calc(1 - var(--space-y-reverse))); margin-bottom: calc(0px * var(--space-y-reverse)); }
  .md\:space-x-4 > * + * { --space-x-reverse: 0; margin-right: calc(1rem * var(--space-x-reverse)); margin-left: calc(1rem * calc(1 - var(--space-x-reverse))); }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:py-4 { padding-top: 1rem; padding-bottom: 1rem; }
  .md\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .md\:mt-0 { margin-top: 0; }
}

@media (min-width: 1024px) {
  .lg\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .lg\:py-8 { padding-top: 2rem; padding-bottom: 2rem; }
  .lg\:mx-auto { margin-left: auto; margin-right: auto; }
  .lg\:max-w-2xl { max-width: 42rem; }
  .lg\:w-full { width: 100%; }
  .lg\:text-left { text-align: left; }
  .lg\:justify-start { justify-content: flex-start; }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:col-span-2 { grid-column: span 2 / span 2; }
  .lg\:block { display: block; }
  .lg\:hidden { display: none; }
  .lg\:py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
  .lg\:pb-28 { padding-bottom: 7rem; }
  .lg\:absolute { position: absolute; }
  .lg\:inset-y-0 { top: 0; bottom: 0; }
  .lg\:right-0 { right: 0; }
  .lg\:w-1\/2 { width: 50%; }
  .lg\:h-full { height: 100%; }
  .lg\:mx-0 { margin-left: 0; margin-right: 0; }
}

@media (min-width: 1280px) {
  .xl\:pb-32 { padding-bottom: 8rem; }
  .xl\:mt-28 { margin-top: 7rem; }
  .xl\:inline { display: inline; }
}