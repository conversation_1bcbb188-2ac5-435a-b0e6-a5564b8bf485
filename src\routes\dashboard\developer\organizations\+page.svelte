<script lang="ts">
  import {
    <PERSON><PERSON><PERSON><PERSON>,
    Card,
    <PERSON><PERSON>,
    DashboardSection
  } from '$lib/components/ui';

  type Organization = {
    id: string;
    name: string;
    isActive: boolean;
    isFrozen: boolean;
    adminCount: number;
    userCount: number;
  };

  interface PageData {
    user: {
      id: string;
      username: string;
      role: "student" | "lecturer" | "admin" | "developer";
    };
    organizations: Organization[];
    stats: {
      totalOrganizations: number;
      activeOrganizations: number;
      frozenOrganizations: number;
    };
  }

  let { data } = $props<{ data: PageData }>();

  let showInactive = $state(false);
  let showFrozen = $state(false);

  let filteredOrganizations = $derived(data.organizations.filter((org: Organization) => {
    if (!showInactive && !org.isActive) return false;
    if (!showFrozen && org.isFrozen) return false;
    return true;
  }));
</script>

<div>
  <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
    <PageHeader title="Organizations" />
    <Button
      variant="primary"
      onClick={() => window.location.href = '/dashboard/developer/organizations/new'}
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
      </svg>
      Create New Organization
    </Button>
  </div>

  <DashboardSection cols="3" gap="4" marginBottom={true}>
    <Card>
      <h3 class="text-sm font-medium text-gray-500">Total Organizations</h3>
      <p class="text-2xl font-bold">{data.stats.totalOrganizations}</p>
    </Card>

    <Card>
      <h3 class="text-sm font-medium text-gray-500">Active Organizations</h3>
      <p class="text-2xl font-bold text-green-600">{data.stats.activeOrganizations}</p>
    </Card>

    <Card>
      <h3 class="text-sm font-medium text-gray-500">Frozen Organizations</h3>
      <p class="text-2xl font-bold text-red-600">{data.stats.frozenOrganizations}</p>
    </Card>
  </DashboardSection>

  <Card class="mb-6">
    <div class="flex flex-wrap items-center gap-4">
      <div class="font-medium">Filters:</div>

      <label class="inline-flex items-center">
        <input
          type="checkbox"
          bind:checked={showInactive}
          class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
        <span class="ml-2 text-sm text-gray-700">Show Inactive</span>
      </label>

      <label class="inline-flex items-center">
        <input
          type="checkbox"
          bind:checked={showFrozen}
          class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
        <span class="ml-2 text-sm text-gray-700">Show Frozen</span>
      </label>
    </div>
  </Card>

  <Card>
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Organization
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Admins
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Users
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          {#if filteredOrganizations.length === 0}
            <tr>
              <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                {data.organizations.length === 0 ? 'No organizations found' : 'No organizations match the selected filters'}
              </td>
            </tr>
          {:else}
            {#each filteredOrganizations as org}
              <tr class={org.isFrozen ? 'bg-red-50' : !org.isActive ? 'bg-gray-50' : ''}>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="font-medium text-gray-900">{org.name}</div>
                  <div class="text-xs text-gray-500">{org.id}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  {#if org.isFrozen}
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                      Frozen
                    </span>
                  {:else if !org.isActive}
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                      Inactive
                    </span>
                  {:else}
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      Active
                    </span>
                  {/if}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {org.adminCount}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {org.userCount}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <Button
                    variant="light"
                    size="sm"
                    onClick={() => window.location.href = `/dashboard/developer/organizations/${org.id}`}
                  >
                    Manage
                  </Button>
                </td>
              </tr>
            {/each}
          {/if}
        </tbody>
      </table>
    </div>
  </Card>
</div>
