import { fail, redirect } from '@sveltejs/kit';
import { eq, and, desc } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;

  if (!user || user.role !== 'admin') {
    return redirect(303, '/auth/login');
  }

  const pendingApprovals = await db
    .select({
      id: table.user.id,
      username: table.user.username,
      email: table.user.email,
      createdAt: table.user.createdAt
    })
    .from(table.user)
    .where(
      and(
        eq(table.user.role, 'lecturer'),
        eq(table.user.isApproved, false),
        eq(table.user.isActive, true)
      )
    )
    .orderBy(desc(table.user.createdAt));

  const recentApprovals = await db
    .select({
      id: table.user.id,
      username: table.user.username,
      email: table.user.email,
      createdAt: table.user.createdAt
    })
    .from(table.user)
    .where(
      and(
        eq(table.user.role, 'lecturer'),
        eq(table.user.isApproved, true),
        eq(table.user.isActive, true)
      )
    )
    .orderBy(desc(table.user.createdAt))
    .limit(5);

  return {
    user,
    pendingApprovals,
    recentApprovals
  };
};

export const actions: Actions = {
  approveUser: async (event) => {
    const { user } = event.locals;

    if (!user || user.role !== 'admin') {
      return fail(403, { message: 'Unauthorized' });
    }

    const formData = await event.request.formData();
    const userId = formData.get('userId')?.toString();

    if (!userId) {
      return fail(400, { message: 'User ID is required' });
    }

    try {
      const [targetUser] = await db
        .select()
        .from(table.user)
        .where(
          and(
            eq(table.user.id, userId),
            eq(table.user.role, 'lecturer'),
            eq(table.user.isApproved, false),
            eq(table.user.isActive, true)
          )
        );

      if (!targetUser) {
        return fail(404, { message: 'User not found or already approved' });
      }

      // Approve the user
      await db
        .update(table.user)
        .set({ isApproved: true })
        .where(eq(table.user.id, userId));

      return {
        success: true,
        message: `User ${targetUser.username} has been approved`,
        approvedUser: {
          id: targetUser.id,
          username: targetUser.username
        }
      };
    } catch (error) {
      console.error('User approval error:', error);
      return fail(500, { message: 'An error occurred during the approval process' });
    }
  },

  rejectUser: async (event) => {
    const { user } = event.locals;

    if (!user || user.role !== 'admin') {
      return fail(403, { message: 'Unauthorized' });
    }

    const formData = await event.request.formData();
    const userId = formData.get('userId')?.toString();

    if (!userId) {
      return fail(400, { message: 'User ID is required' });
    }

    try {
      const [targetUser] = await db
        .select()
        .from(table.user)
        .where(
          and(
            eq(table.user.id, userId),
            eq(table.user.role, 'lecturer'),
            eq(table.user.isApproved, false),
            eq(table.user.isActive, true)
          )
        );

      if (!targetUser) {
        return fail(404, { message: 'User not found or already approved/rejected' });
      }

      // Reject the user
      await db
        .update(table.user)
        .set({ isActive: false })
        .where(eq(table.user.id, userId));

      return {
        success: true,
        message: `User ${targetUser.username} has been rejected`,
        rejectedUser: {
          id: targetUser.id,
          username: targetUser.username
        }
      };
    } catch (error) {
      console.error('User rejection error:', error);
      return fail(500, { message: 'An error occurred during the rejection process' });
    }
  }
};