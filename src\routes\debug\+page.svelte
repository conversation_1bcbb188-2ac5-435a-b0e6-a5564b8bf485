<script lang="ts">
  import { page } from '$app/state';
  import { locale, t } from '$lib/stores/locale';
  import { browser } from '$app/environment';

  // Use reactive store access instead of manual state management
  const currentLocale = $derived($locale);
  let isHeaderVisible = $state(false);

  // Modern effect pattern for DOM queries
  $effect(() => {
    if (!browser) return;

    const header = document.querySelector('header');
    isHeaderVisible = !!header && window.getComputedStyle(header).display !== 'none';
  });
</script>

<div class="p-8">
  <h1 class="text-2xl font-bold mb-4">{t('debug.title', currentLocale)}</h1>

  <div class="bg-white shadow rounded p-4 mb-4 dark:bg-gray-800 dark:text-white">
    <h2 class="text-lg font-semibold mb-2">{t('debug.headerStatus', currentLocale)}</h2>
    <p>{t('debug.headerVisible', currentLocale)} {isHeaderVisible ? 'Yes' : 'No'}</p>
  </div>

  <div class="bg-white shadow rounded p-4 mb-4 dark:bg-gray-800 dark:text-white">
    <h2 class="text-lg font-semibold mb-2">{t('debug.pageData', currentLocale)}</h2>
    <pre class="bg-gray-100 p-4 rounded overflow-auto max-h-96 dark:bg-gray-700 dark:text-gray-300">{JSON.stringify(page.data, null, 2)}</pre>
  </div>

  <div class="bg-white shadow rounded p-4 mb-4 dark:bg-gray-800 dark:text-white">
    <h2 class="text-lg font-semibold mb-2">{t('debug.userData', currentLocale)}</h2>
    <pre class="bg-gray-100 p-4 rounded overflow-auto max-h-96 dark:bg-gray-700 dark:text-gray-300">{JSON.stringify(page.data.user, null, 2)}</pre>
  </div>

  <div class="mt-4">
    <a href="/dashboard" class="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700">{t('debug.goToDashboard', currentLocale)}</a>
  </div>
</div>