<script lang="ts">
  const {
    title = '',
    subtitle = '',
    class: className = '',
    headerAction = null,
    children = undefined
  } = $props<{
    title?: string,
    subtitle?: string,
    class?: string,
    headerAction?: any,
    children?: any
  }>();

  const cardClass = $derived(`
    bg-white dark:bg-gray-800
    rounded-lg shadow-sm p-6
    border border-gray-200 dark:border-gray-700
    ${className}
  `);
  const showHeader = $derived(!!title);
</script>

<div class={cardClass}>
  {#if showHeader}
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
      <div>
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">{title}</h2>
        {#if subtitle}
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{subtitle}</p>
        {/if}
      </div>

      {#if headerAction}
        <div class="mt-2 md:mt-0">
          {@render headerAction()}
        </div>
      {/if}
    </div>
  {/if}

  {#if children !== undefined}
    {@render children()}
  {/if}
</div>
