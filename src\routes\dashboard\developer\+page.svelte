<script lang="ts">
  import { locale, t } from '$lib/stores/locale';
  import {
    PageHeader,
    DashboardSection,
    StatLink,
    Card,
    ActionButton,
    KeyValueList,
    DataTable
  } from '$lib/components/ui';

  type Organization = {
    id: string;
    name: string;
    isActive: boolean;
  };

  interface PageData {
    user: {
      id: string;
      username: string;
      role: "student" | "lecturer" | "admin" | "developer";
      isActive: boolean;
      isApproved: boolean;
      email: string;
      organization: string | null;
    };
    stats: {
      activeAdmins: number;
      totalUsers: number;
      totalStudents: number;
      totalLecturers: number;
      totalAdmins: number;
      totalProjects: number;
      totalSubmissions: number;
    };
    organizations: Organization[];
  }

  let { data } = $props<{ data: PageData }>();

  // Use reactive store access instead of manual state management
  const currentLocale = $derived($locale);

  const totalUsersLabel = $derived(t('dashboard.developer.totalUsers', currentLocale));
  const activeAdminsLabel = $derived(t('dashboard.developer.activeAdmins', currentLocale));
  const manageAdminsLabel = $derived(t('dashboard.developer.manageAdmins', currentLocale));
  const allRolesCombinedLabel = $derived(t('dashboard.developer.allRolesCombined', currentLocale));
  const quickActionsLabel = $derived(t('dashboard.developer.quickActions', currentLocale));
  const systemStatsLabel = $derived(t('dashboard.developer.systemStats', currentLocale));
  const organizationsLabel = $derived(t('header.organizations', currentLocale));
  const createNewOrgLabel = $derived(t('dashboard.developer.createNewOrg', currentLocale));

  const statsItems = $derived([
    { key: t('dashboard.developer.totalStudents', currentLocale), value: data.stats.totalStudents },
    { key: t('dashboard.developer.totalLecturers', currentLocale), value: data.stats.totalLecturers },
    { key: t('dashboard.developer.totalAdmins', currentLocale), value: data.stats.totalAdmins },
    { key: t('dashboard.developer.totalProjects', currentLocale), value: data.stats.totalProjects },
    { key: t('dashboard.developer.totalSubmissions', currentLocale), value: data.stats.totalSubmissions }
  ]);

  const orgColumns = $derived([
    { key: 'name', label: t('common.organization', currentLocale) },
    { key: 'status', label: t('common.status', currentLocale) },
    { key: 'actions', label: t('common.actions', currentLocale) }
  ]);

  const orgData = $derived(data.organizations.map((org: Organization) => {
    const statusClass = org.isActive
      ? 'px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800'
      : 'px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800';
    const statusText = org.isActive
      ? t('common.active', currentLocale)
      : t('common.inactive', currentLocale);

    return {
      name: `<div class="font-medium text-gray-900 dark:text-white">${org.name}</div>
             <div class="text-sm text-gray-500">${org.id}</div>`,
      status: `<span class="${statusClass}">${statusText}</span>`,
      actions: `<a href="/dashboard/developer/organizations/${org.id}" class="text-blue-600 hover:text-blue-900 mr-3">${t('common.edit', currentLocale)}</a>`
    };
  }));
</script>

<div>
  <PageHeader title={t('dashboard.developer.title', currentLocale)} />

  <DashboardSection cols="3" gap="6">
    <StatLink
      title={activeAdminsLabel}
      value={data.stats.activeAdmins}
      href="/dashboard/developer/admins"
      linkText={manageAdminsLabel}
    />

    <StatLink
      title={totalUsersLabel}
      value={data.stats.totalUsers}
    >
      <span class="text-sm text-gray-500">{allRolesCombinedLabel}</span>
    </StatLink>
  </DashboardSection>

  <DashboardSection cols="2" gap="6">
    <Card title={quickActionsLabel}>
      <div class="space-y-3">
        <ActionButton
          href="/dashboard/developer/organizations/new"
          color="green"
          title={t('dashboard.developer.createNewOrg', currentLocale)}
          description={t('dashboard.developer.createNewOrgDesc', currentLocale)}
        />
        <ActionButton
          href="/dashboard/developer/admins/new"
          color="blue"
          title={t('dashboard.developer.createNewAdmin', currentLocale)}
          description={t('dashboard.developer.createNewAdminDesc', currentLocale)}
        />
        <ActionButton
          href="/dashboard/developer/organizations"
          color="purple"
          title={t('header.organizations', currentLocale)}
          description="View and manage all organizations"
        />
        <ActionButton
          href="/dashboard/developer/pdf-analysis"
          color="blue"
          title="PDF Analysis"
          description="Test PDF text extraction with Claude API"
        />
        <ActionButton
          href="/dashboard/developer/system/settings"
          color="gray"
          title={t('header.system', currentLocale)}
          description="Manage global system configuration"
        />
      </div>
    </Card>

    <Card title={systemStatsLabel}>
      <KeyValueList items={statsItems} />
    </Card>
  </DashboardSection>

  <DashboardSection>
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">{organizationsLabel}</h2>
        <a href="/dashboard/developer/organizations/new" class="text-blue-600 hover:text-blue-800 text-sm font-medium mt-2 md:mt-0">
          {createNewOrgLabel}
        </a>
      </div>
      <DataTable columns={orgColumns} data={orgData} />
    </div>
  </DashboardSection>
</div>