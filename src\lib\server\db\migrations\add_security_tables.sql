-- Migration: Add security tables for login attempt tracking and account lockout
-- Created: December 2024
-- Purpose: Enhance authentication security with login monitoring and account lockout

-- Create login_attempt table for tracking all login attempts
CREATE TABLE IF NOT EXISTS login_attempt (
    id TEXT PRIMARY KEY,
    username TEXT NOT NULL,
    ip_address TEXT NOT NULL,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    failure_reason TEXT,
    attempted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create account_lockout table for managing account lockouts
CREATE TABLE IF NOT EXISTS account_lockout (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES "user"(id),
    locked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    locked_until TIMESTAMP WITH TIME ZONE NOT NULL,
    failed_attempts INTEGER NOT NULL,
    lock_reason TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true
);

-- Create comprehensive indexes for optimal performance
-- Single column indexes for basic queries
CREATE INDEX IF NOT EXISTS idx_login_attempt_username ON login_attempt(username);
CREATE INDEX IF NOT EXISTS idx_login_attempt_ip_address ON login_attempt(ip_address);
CREATE INDEX IF NOT EXISTS idx_login_attempt_attempted_at ON login_attempt(attempted_at DESC);
CREATE INDEX IF NOT EXISTS idx_login_attempt_success ON login_attempt(success);

CREATE INDEX IF NOT EXISTS idx_account_lockout_user_id ON account_lockout(user_id);
CREATE INDEX IF NOT EXISTS idx_account_lockout_locked_until ON account_lockout(locked_until DESC);
CREATE INDEX IF NOT EXISTS idx_account_lockout_is_active ON account_lockout(is_active);

-- Composite indexes for common query patterns (order matters for performance)
-- For recent failed attempts by username
CREATE INDEX IF NOT EXISTS idx_login_attempt_username_time_success ON login_attempt(username, attempted_at DESC, success);

-- For IP-based rate limiting and monitoring
CREATE INDEX IF NOT EXISTS idx_login_attempt_ip_time_success ON login_attempt(ip_address, attempted_at DESC, success);

-- For active lockout checks (most common query)
CREATE INDEX IF NOT EXISTS idx_account_lockout_user_active_until ON account_lockout(user_id, is_active, locked_until DESC);

-- For lockout expiration cleanup
CREATE INDEX IF NOT EXISTS idx_account_lockout_active_until ON account_lockout(is_active, locked_until);

-- For security monitoring and statistics
CREATE INDEX IF NOT EXISTS idx_login_attempt_time_success_ip ON login_attempt(attempted_at DESC, success, ip_address);

-- Partial indexes for better performance on filtered queries
-- Only index failed login attempts (most security-relevant)
CREATE INDEX IF NOT EXISTS idx_login_attempt_failed_username_time ON login_attempt(username, attempted_at DESC)
WHERE success = false;

-- Only index active lockouts
CREATE INDEX IF NOT EXISTS idx_account_lockout_active_user_until ON account_lockout(user_id, locked_until DESC)
WHERE is_active = true;

-- Index for recent failed attempts (last 24 hours) - can be recreated daily
CREATE INDEX IF NOT EXISTS idx_login_attempt_recent_failed ON login_attempt(username, attempted_at DESC)
WHERE success = false AND attempted_at > NOW() - INTERVAL '24 hours';

-- Add comments for documentation
COMMENT ON TABLE login_attempt IS 'Tracks all login attempts for security monitoring and rate limiting';
COMMENT ON TABLE account_lockout IS 'Manages account lockouts after failed login attempts';

COMMENT ON COLUMN login_attempt.username IS 'Username attempted (may not exist in user table)';
COMMENT ON COLUMN login_attempt.ip_address IS 'IP address of the login attempt';
COMMENT ON COLUMN login_attempt.user_agent IS 'User agent string from the browser';
COMMENT ON COLUMN login_attempt.success IS 'Whether the login attempt was successful';
COMMENT ON COLUMN login_attempt.failure_reason IS 'Reason for login failure (for security analysis)';

COMMENT ON COLUMN account_lockout.user_id IS 'Reference to the locked user account';
COMMENT ON COLUMN account_lockout.locked_at IS 'When the account was locked';
COMMENT ON COLUMN account_lockout.locked_until IS 'When the lockout expires';
COMMENT ON COLUMN account_lockout.failed_attempts IS 'Number of failed attempts that triggered the lockout';
COMMENT ON COLUMN account_lockout.lock_reason IS 'Reason for the account lockout';
COMMENT ON COLUMN account_lockout.is_active IS 'Whether this lockout is currently active';

-- Create a function to clean up old login attempts (optional, for maintenance)
CREATE OR REPLACE FUNCTION cleanup_old_login_attempts()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete login attempts older than 90 days
    DELETE FROM login_attempt 
    WHERE attempted_at < NOW() - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Delete inactive lockouts older than 30 days
    DELETE FROM account_lockout 
    WHERE is_active = false 
    AND locked_at < NOW() - INTERVAL '30 days';
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get security statistics
CREATE OR REPLACE FUNCTION get_security_stats()
RETURNS TABLE (
    total_attempts_24h INTEGER,
    failed_attempts_24h INTEGER,
    unique_ips_24h INTEGER,
    active_lockouts INTEGER,
    total_lockouts_7d INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*)::INTEGER FROM login_attempt WHERE attempted_at > NOW() - INTERVAL '24 hours'),
        (SELECT COUNT(*)::INTEGER FROM login_attempt WHERE attempted_at > NOW() - INTERVAL '24 hours' AND success = false),
        (SELECT COUNT(DISTINCT ip_address)::INTEGER FROM login_attempt WHERE attempted_at > NOW() - INTERVAL '24 hours'),
        (SELECT COUNT(*)::INTEGER FROM account_lockout WHERE is_active = true AND locked_until > NOW()),
        (SELECT COUNT(*)::INTEGER FROM account_lockout WHERE locked_at > NOW() - INTERVAL '7 days');
END;
$$ LANGUAGE plpgsql;

-- Grant appropriate permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT ON login_attempt TO app_user;
-- GRANT SELECT, INSERT, UPDATE ON account_lockout TO app_user;
-- GRANT EXECUTE ON FUNCTION cleanup_old_login_attempts() TO app_user;
-- GRANT EXECUTE ON FUNCTION get_security_stats() TO app_user;
