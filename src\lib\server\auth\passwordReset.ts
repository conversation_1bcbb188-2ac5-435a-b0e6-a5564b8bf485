import { encodeBase64url } from '@oslojs/encoding';
import { db } from '../db';
import * as table from '../db/schema';
import { eq, and, lt } from 'drizzle-orm';
import { getPasswordResetExpiry } from '../email/email';
import type { User } from '../db/schema';

export function generatePasswordResetToken(): string {
  const bytes = crypto.getRandomValues(new Uint8Array(32));
  return encodeBase64url(bytes);
}

export async function createPasswordResetToken(userId: string): Promise<string> {
  await db
    .update(table.passwordReset)
    .set({ isUsed: true })
    .where(eq(table.passwordReset.userId, userId));

  const token = generatePasswordResetToken();
  const expiresAt = getPasswordResetExpiry();

  await db.insert(table.passwordReset).values({
    id: crypto.randomUUID(),
    userId,
    token,
    expiresAt: expiresAt.toISOString(),
    isUsed: false
  });

  return token;
}

export async function validatePasswordResetToken(token: string): Promise<User | null> {
  if (!token || typeof token !== 'string' || token.trim() === '') {
    console.error('Invalid token format provided to validatePasswordResetToken');
    return null;
  }

  try {
    // Find token in database
    const results = await db
      .select({
        reset: table.passwordReset,
        user: table.user
      })
      .from(table.passwordReset)
      .innerJoin(table.user, eq(table.passwordReset.userId, table.user.id))
      .where(
        and(
          eq(table.passwordReset.token, token),
          eq(table.passwordReset.isUsed, false)
        )
      );

    const result = results[0];

    if (!result) {
      console.log('No matching token found in database or token already used');
      return null;
    }

    const now = new Date();
    const expiresAt = new Date(result.reset.expiresAt);

    if (isNaN(expiresAt.getTime())) {
      console.error('Invalid expiry date format in database:', result.reset.expiresAt);
      return null;
    }

    if (now >= expiresAt) {
      console.log('Token expired. Current time:', now, 'Expiry time:', expiresAt);
      return null;
    }

    return result.user;
  } catch (error) {
    console.error('Error validating password reset token:', error);
    return null;
  }
}

export async function markTokenAsUsed(token: string): Promise<boolean> {
  if (!token || typeof token !== 'string' || token.trim() === '') {
    console.error('Invalid token format provided to markTokenAsUsed');
    return false;
  }

  try {
    const result = await db
      .update(table.passwordReset)
      .set({ isUsed: true })
      .where(eq(table.passwordReset.token, token));

    return true;
  } catch (error) {
    console.error('Error marking token as used:', error);
    return false;
  }
}

export async function cleanupExpiredTokens(): Promise<void> {
  const now = new Date();
  const nowISOString = now.toISOString();

  await db
    .delete(table.passwordReset)
    .where(lt(table.passwordReset.expiresAt, nowISOString));
}
