<script lang="ts">
    import { enhance } from '$app/forms';
    import {
      <PERSON><PERSON><PERSON><PERSON>,
      Card,
      Button,
      AlertMessage,
      EmptyState
    } from '$lib/components/ui';

    let { data, form } = $props();

    function formatDate(dateString: string | Date): string {
      return new Date(dateString).toLocaleDateString();
    }
  </script>

  <div>
    <PageHeader title="Pending Approvals" class="mb-6" />

    {#if form?.success}
      <AlertMessage
        type="success"
        message={form.message}
        class="mb-6"
      />
    {/if}

    <Card class="mb-6">
      <h2 class="text-xl font-semibold mb-4">Lecturer Account Requests</h2>

      {#if data.pendingApprovals.length === 0}
        <EmptyState
          message="No pending approval requests at this time."
        />
      {:else}
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Username
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Registration Date
                </th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {#each data.pendingApprovals as user}
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{user.username}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-500">{user.email}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-500">{formatDate(user.createdAt)}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex justify-end space-x-3">
                      <form method="POST" action="?/approveUser" use:enhance>
                        <input type="hidden" name="userId" value={user.id} />
                        <Button
                          type="submit"
                          variant="success"
                          size="sm"
                        >
                          Approve
                        </Button>
                      </form>
                      <form method="POST" action="?/rejectUser" use:enhance>
                        <input type="hidden" name="userId" value={user.id} />
                        <Button
                          type="submit"
                          variant="danger"
                          size="sm"
                        >
                          Reject
                        </Button>
                      </form>
                    </div>
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      {/if}
    </Card>

    <Card>
      <h2 class="text-xl font-semibold mb-4">Recently Approved Lecturers</h2>

      {#if data.recentApprovals.length === 0}
        <EmptyState
          message="No recently approved lecturers."
        />
      {:else}
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Username
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Registration Date
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {#each data.recentApprovals as user}
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{user.username}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-500">{user.email}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-500">{formatDate(user.createdAt)}</div>
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      {/if}
    </Card>
  </div>