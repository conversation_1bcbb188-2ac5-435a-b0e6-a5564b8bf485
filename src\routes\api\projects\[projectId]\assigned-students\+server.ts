import { json } from '@sveltejs/kit';
import { and, eq } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async (event) => {
  const { user } = event.locals;
  const { projectId } = event.params;

  if (!user || user.role !== 'lecturer') {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 403,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    // First verify the project belongs to the requesting lecturer
    const [project] = await db
      .select({ id: table.project.id })
      .from(table.project)
      .where(
        and(
          eq(table.project.id, projectId),
          eq(table.project.createdBy, user.id)
        )
      );

    if (!project) {
      return new Response(JSON.stringify({ error: 'Project not found or unauthorized' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get assigned students (with institution filtering for extra security)
    const assignedStudents = await db
      .select({
        id: table.projectStudent.id,
        studentId: table.projectStudent.studentId,
        student: {
          id: table.user.id,
          username: table.user.username,
          name: table.user.name,
          email: table.user.email
        },
        createdAt: table.projectStudent.createdAt
      })
      .from(table.projectStudent)
      .innerJoin(
        table.user,
        eq(table.projectStudent.studentId, table.user.id)
      )
      .where(
        and(
          eq(table.projectStudent.projectId, projectId),
          user.organization ? eq(table.user.organization, user.organization) : undefined
        )
      );

    return json(assignedStudents);
  } catch (error) {
    console.error('Error fetching assigned students:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
