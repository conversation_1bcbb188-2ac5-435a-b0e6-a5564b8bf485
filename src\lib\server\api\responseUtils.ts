import { json } from '@sveltejs/kit';
import type { User } from '$lib/types';

export interface ErrorResponse {
  success: false;
  message: string;
  status: number;
}

export interface SuccessResponse<T = any> {
  success: true;
  message?: string;
  data?: T;
}

export function createErrorResponse(message: string, status: number = 400): ErrorResponse {
  return {
    success: false,
    message,
    status
  };
}

export function createSuccessResponse<T>(data?: T, message?: string): SuccessResponse<T> {
  return {
    success: true,
    message,
    data
  };
}

export function errorResponse(message: string, status: number = 400) {
  return json(createErrorResponse(message, status), { status });
}

export function successResponse<T>(data?: T, message?: string) {
  return json(createSuccessResponse(data, message));
}

export function unauthorizedResponse(message: string = 'Unauthorized') {
  return errorResponse(message, 401);
}

export function forbiddenResponse(message: string = 'Forbidden') {
  return errorResponse(message, 403);
}

export function notFoundResponse(message: string = 'Not found') {
  return errorResponse(message, 404);
}

export function serverErrorResponse(message: string = 'Internal server error') {
  return errorResponse(message, 500);
}

export function checkUserRole(user: User | null, requiredRole?: string | string[]): boolean {
  if (!user) return false;

  if (!requiredRole) return true;

  if (Array.isArray(requiredRole)) {
    return requiredRole.includes(user.role);
  }

  return user.role === requiredRole;
}

export function fileDownloadResponse(
  content: string | Buffer,
  filename: string,
  contentType: string = 'application/octet-stream',
  disposition: 'attachment' | 'inline' = 'attachment',
  cacheControl?: string
) {
  const safeFilename = filename.replace(/[\/:*?"<>|]/g, '_');

  const headers = new Headers();
  headers.set('Content-Type', contentType);
  headers.set('Content-Disposition', `${disposition}; filename="${safeFilename}"`);

  if (cacheControl) {
    headers.set('Cache-Control', cacheControl);
  }

  return new Response(content, {
    status: 200,
    headers
  });
}

export function textFileDownloadResponse(content: string, filename: string) {
  return fileDownloadResponse(
    content,
    filename.endsWith('.txt') ? filename : `${filename}.txt`,
    'text/plain; charset=utf-8'
  );
}

export function binaryFileDownloadResponse(content: Buffer, filename: string, contentType: string) {
  return fileDownloadResponse(content, filename, contentType);
}
