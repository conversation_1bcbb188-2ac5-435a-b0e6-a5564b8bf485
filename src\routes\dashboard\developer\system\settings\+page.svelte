<script lang="ts">
  import { enhance } from '$app/forms';
  import {
    <PERSON><PERSON><PERSON>er,
    Card,
    Button,
    AlertMessage,
    FormInput
  } from '$lib/components/ui';

  let { data, form } = $props();

  type Organization = {
    id: string;
    name: string;
    description?: string | null;
    isActive: boolean;
    isFrozen: boolean;
    createdAt?: Date;
  };

  let editingOrg = $state<Organization | null>(null);
  let showEditForm = $state(false);

  function editOrganization(org: Organization) {
    editingOrg = { ...org };
    showEditForm = true;
  }

  function cancelEdit() {
    editingOrg = null;
    showEditForm = false;
  }

  function confirmDelete(organizationId: string) {
    if (confirm('Are you sure you want to delete this organization? This action cannot be undone.')) {
      const formData = new FormData();
      formData.append('organizationId', organizationId);

      fetch('?/deleteOrganization', {
        method: 'POST',
        body: formData
      }).then(() => {
        window.location.reload();
      });
    }
  }
</script>

<div>
  <div class="mb-6">
    <PageHeader title="System Settings" />
  </div>

  {#if form?.success && form?.message}
    <AlertMessage
      type="success"
      message={form.message}
      class="mb-6"
    />
  {/if}

  {#if !form?.success && form?.message}
    <AlertMessage
      type="error"
      message={form.message}
      class="mb-6"
    />
  {/if}

  <Card class="mb-6">
    <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Configuration</h2>

    <form method="POST" action="?/updateSettings" use:enhance class="space-y-6">
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div>
          <label for="maxProjectsPerLecturer" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Max Projects Per Lecturer
          </label>
          <input
            type="number"
            id="maxProjectsPerLecturer"
            name="maxProjectsPerLecturer"
            value={data.settings.maxProjectsPerLecturer}
            min="1"
            required
            class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          />
        </div>

      </div>

      <div>
        <Button
          type="submit"
          variant="primary"
        >
          Update Settings
        </Button>
      </div>
    </form>
  </Card>

  <Card class="mb-6">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Organizations</h2>
      <Button
        variant="primary"
        onClick={() => {
          const newOrgSection = document.getElementById('new-org-section');
          if (newOrgSection) {
            newOrgSection.classList.toggle('hidden');
          }
        }}
      >
        Add Organization
      </Button>
    </div>

    <div id="new-org-section" class="mb-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 hidden">
      <h3 class="text-lg font-medium mb-3 text-gray-900 dark:text-white">Create New Organization</h3>
      <form method="POST" action="?/createOrganization" use:enhance class="space-y-4">
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Organization Name
          </label>
          <input
            type="text"
            id="name"
            name="name"
            required
            class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          />
        </div>

        <div>
          <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            rows="2"
            class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          ></textarea>
        </div>


        <div>
          <Button
            type="submit"
            variant="success"
          >
            Create Organization
          </Button>
        </div>
      </form>
    </div>

    {#if showEditForm && editingOrg}
      <div class="mb-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
        <div class="flex justify-between items-center mb-3">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Edit Organization</h3>
          <button
            type="button"
            class="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100"
            onclick={cancelEdit}
            aria-label="Close edit form"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>

        <form method="POST" action="?/updateOrganization" use:enhance class="space-y-4">
          <input type="hidden" name="organizationId" value={editingOrg.id} />

          <div>
            <label for="edit-name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Organization Name
            </label>
            <input
              type="text"
              id="edit-name"
              name="name"
              value={editingOrg.name}
              required
              class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>

          <div>
            <label for="edit-description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description
            </label>
            <textarea
              id="edit-description"
              name="description"
              rows="2"
              class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            >{editingOrg.description || ''}</textarea>
          </div>

          <div>
            <Button
              type="submit"
              variant="primary"
            >
              Update Organization
            </Button>
          </div>
        </form>
      </div>
    {/if}

    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-800">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Name
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Description
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Status
            </th>
            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          {#if data.organizations?.length === 0}
            <tr>
              <td colspan="4" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-center">
                No organizations found
              </td>
            </tr>
          {:else}
            {#each data.organizations || [] as organization (organization.id)}
              <tr class={organization.isActive ? '' : 'bg-red-50 dark:bg-red-900/20'}>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="font-medium text-gray-900 dark:text-white">{organization.name}</div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-500 dark:text-gray-400">{organization.description || 'No description'}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                  <div class="flex flex-col space-y-1">
                    <span class={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${organization.isActive ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'}`}>
                      {organization.isActive ? 'Active' : 'Inactive'}
                    </span>

                    {#if organization.isFrozen}
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                        Frozen
                      </span>
                    {/if}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex justify-end space-x-3">
                    <!-- Edit button -->
                    <button
                      type="button"
                      class="btn-link text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                      onclick={() => editOrganization(organization)}
                    >
                      Edit
                    </button>

                    <!-- Toggle status buttons -->
                    <form method="POST" action="?/toggleOrganizationStatus" use:enhance class="inline">
                      <input type="hidden" name="organizationId" value={organization.id} />
                      {#if organization.isActive}
                        <input type="hidden" name="action" value="deactivate" />
                        <button
                          type="submit"
                          class="btn-link text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        >
                          Deactivate
                        </button>
                      {:else}
                        <input type="hidden" name="action" value="activate" />
                        <button
                          type="submit"
                          class="btn-link text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                        >
                          Activate
                        </button>
                      {/if}
                    </form>

                    <!-- Freeze / unfreeze buttons -->
                    <form method="POST" action="?/toggleOrganizationStatus" use:enhance class="inline">
                      <input type="hidden" name="organizationId" value={organization.id} />
                      {#if organization.isFrozen}
                        <input type="hidden" name="action" value="unfreeze" />
                        <button
                          type="submit"
                          class="btn-link text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          Unfreeze
                        </button>
                      {:else}
                        <input type="hidden" name="action" value="freeze" />
                        <button
                          type="submit"
                          class="btn-link text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        >
                          Freeze
                        </button>
                      {/if}
                    </form>

                    <!-- Delete button -->
                    <button
                      type="button"
                      class="btn-link text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                      onclick={() => confirmDelete(organization.id)}
                    >
                      Delete
                    </button>
                  </div>
                </td>
              </tr>
            {/each}
          {/if}
        </tbody>
      </table>
    </div>
  </Card>

  <Card>
    <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">System Information</h2>

    <div class="space-y-4">
      <div>
        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">System Version</p>
        <p class="text-gray-900 dark:text-white">{data.settings.systemVersion}</p>
      </div>

      <div>
        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Backup</p>
        <p class="text-gray-900 dark:text-white">{new Date(data.settings.lastBackup).toLocaleString()}</p>
      </div>

      <div class="pt-4">
        <Button
          type="button"
          variant="success"
        >
          Backup System
        </Button>
      </div>
    </div>
  </Card>
</div>