import { redirect } from '@sveltejs/kit';
import { eq, inArray, count, sql } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;

  if (!user || user.role !== 'lecturer') {
    return redirect(303, '/auth/login');
  }

  if (!user.isApproved) {
    return redirect(303, '/pending');
  }

  // Get groups created by the user
  const createdGroups = await db
    .select({
      id: table.group.id,
      name: table.group.name,
      description: table.group.description,
      createdAt: table.group.createdAt,
      updatedAt: table.group.updatedAt,
      isCreator: sql`true`
    })
    .from(table.group)
    .where(eq(table.group.createdBy, user.id));

  // Get groups where the user is a manager
  const managedGroups = await db
    .select({
      id: table.group.id,
      name: table.group.name,
      description: table.group.description,
      createdAt: table.group.createdAt,
      updatedAt: table.group.updatedAt,
      isCreator: sql`false`
    })
    .from(table.group)
    .innerJoin(
      table.groupManager,
      eq(table.group.id, table.groupManager.groupId)
    )
    .where(eq(table.groupManager.lecturerId, user.id));

  const groupMap = new Map();

  // Add created groups first
  createdGroups.forEach(group => {
    groupMap.set(group.id, group);
  });

  // Add managed groups if not added
  managedGroups.forEach(group => {
    if (!groupMap.has(group.id)) {
      groupMap.set(group.id, group);
    }
  });

  const groups = Array.from(groupMap.values());

  // Get all group IDs
  const groupIds = groups.map(group => group.id);

  // Get student counts for all groups
  const studentCounts = await db
    .select({
      groupId: table.groupMember.groupId,
      count: count()
    })
    .from(table.groupMember)
    .where(inArray(table.groupMember.groupId, groupIds))
    .groupBy(table.groupMember.groupId);

  // Get manager counts for all groups
  const managerCounts = await db
    .select({
      groupId: table.groupManager.groupId,
      count: count()
    })
    .from(table.groupManager)
    .where(inArray(table.groupManager.groupId, groupIds))
    .groupBy(table.groupManager.groupId);

  // Get lecture times for all groups
  const allLectureTimes = await db
    .select({
      groupId: table.lectureTime.groupId,
      id: table.lectureTime.id,
      weekday: table.lectureTime.weekday,
      startTime: table.lectureTime.startTime,
      endTime: table.lectureTime.endTime,
      location: table.lectureTime.location
    })
    .from(table.lectureTime)
    .where(inArray(table.lectureTime.groupId, groupIds));

  // Create lookup maps
  const studentCountMap = new Map(
    studentCounts.map(item => [item.groupId, item.count])
  );

  const managerCountMap = new Map(
    managerCounts.map(item => [item.groupId, item.count])
  );

  const lectureTimesMap = new Map();
  allLectureTimes.forEach(lectureTime => {
    if (!lectureTimesMap.has(lectureTime.groupId)) {
      lectureTimesMap.set(lectureTime.groupId, []);
    }
    lectureTimesMap.get(lectureTime.groupId).push({
      id: lectureTime.id,
      weekday: lectureTime.weekday,
      startTime: lectureTime.startTime,
      endTime: lectureTime.endTime,
      location: lectureTime.location
    });
  });

  // Combine all into result
  const groupsWithDetails = groups.map(group => ({
    ...group,
    studentCount: studentCountMap.get(group.id) || 0,
    managerCount: managerCountMap.get(group.id) || 0,
    lectureTimes: lectureTimesMap.get(group.id) || []
  }));

  return {
    user,
    groups: groupsWithDetails
  };
};
