<script lang="ts">
  import { slide } from 'svelte/transition';

  const {
    type = 'info',
    message = '',
    showIcon = true,
    dismissible = false,
    onDismiss = undefined,
    actionText = '',
    actionHref = '',
    actionOnClick = undefined,
    class: className = ''
  } = $props<{
    type?: 'success' | 'error' | 'warning' | 'info',
    message: string,
    showIcon?: boolean,
    dismissible?: boolean,
    onDismiss?: () => void,
    actionText?: string,
    actionHref?: string,
    actionOnClick?: () => void,
    class?: string
  }>();

  //  classes and icons
  const typeClasses: Record<'success' | 'error' | 'warning' | 'info', string> = {
    success: 'bg-green-50 dark:bg-green-900/30 border-green-200 dark:border-green-800/30 text-green-700 dark:text-green-300',
    error: 'bg-red-50 dark:bg-red-900/30 border-red-200 dark:border-red-800/30 text-red-700 dark:text-red-300',
    warning: 'bg-yellow-50 dark:bg-yellow-900/30 border-yellow-200 dark:border-yellow-800/30 text-yellow-700 dark:text-yellow-300',
    info: 'bg-blue-50 dark:bg-blue-900/30 border-blue-200 dark:border-blue-800/30 text-blue-700 dark:text-blue-300'
  };

  const iconPaths: Record<'success' | 'error' | 'warning' | 'info', string> = {
    success: 'M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z',
    error: 'M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z',
    warning: 'M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z',
    info: 'M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z'
  };

  const validType = (type in typeClasses) ? type as keyof typeof typeClasses : 'info';

  const alertClass = $derived(`
    p-4 rounded-md border ${typeClasses[validType]} ${className}
  `);

  function handleActionClick() {
    if (actionOnClick) {
      actionOnClick();
    }
  }
</script>

<div transition:slide={{ duration: 200 }} class={alertClass} role="alert">
  <div class="flex">
    {#if showIcon}
      <div class="flex-shrink-0">
        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d={iconPaths[validType]} clip-rule="evenodd" />
        </svg>
      </div>
    {/if}

    <div class="ml-3 flex-1">
      <p class="text-sm">{message}</p>

      {#if actionText}
        <div class="mt-4">
          {#if actionHref}
            <a href={actionHref} class="text-sm font-medium underline hover:opacity-80">
              {actionText}
            </a>
          {:else}
            <button
              type="button"
              class="text-sm font-medium underline hover:opacity-80"
              onclick={handleActionClick}
            >
              {actionText}
            </button>
          {/if}
        </div>
      {/if}
    </div>

    {#if dismissible && onDismiss}
      <div class="ml-auto pl-3">
        <button
          type="button"
          class="inline-flex rounded-md p-1.5 hover:bg-white/20 focus:outline-none"
          onclick={onDismiss}
          aria-label="Dismiss"
        >
          <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>
    {/if}
  </div>
</div>
