import { fail, redirect } from '@sveltejs/kit';
import { eq } from 'drizzle-orm';
import { hashPassword } from '$lib/server/auth/password';
import { encodeBase32LowerCase } from '@oslojs/encoding';
import * as auth from '$lib/server/auth';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;

  if (!user || user.role !== 'developer') {
    return redirect(303, '/auth/login');
  }

  let organizations = [];
  try {
    organizations = await db
      .select({
        id: table.organization.id,
        name: table.organization.name,
        isActive: table.organization.isActive,
        isFrozen: table.organization.isFrozen
      })
      .from(table.organization)
      .where(eq(table.organization.isActive, true))
      .orderBy(table.organization.name);
  } catch (error) {
    console.error('Error fetching organizations with active filter:', error);
    organizations = await db
      .select({
        id: table.organization.id,
        name: table.organization.name
      })
      .from(table.organization)
      .orderBy(table.organization.name);
  }

  return {
    user,
    organizations
  };
};

export const actions: Actions = {
  createAdmin: async (event) => {
    const { user } = event.locals;

    if (!user || user.role !== 'developer') {
      return fail(403, { message: 'Unauthorized' });
    }

    const formData = await event.request.formData();
    const username = formData.get('username')?.toString();
    const email = formData.get('email')?.toString();
    const password = formData.get('password')?.toString();
    const organization = formData.get('organization')?.toString() || null;

    if (!validateUsername(username)) {
      return fail(400, { message: 'Invalid username (min 3, max 31 characters, alphanumeric only)' });
    }
    if (!validateEmail(email)) {
      return fail(400, { message: 'Invalid email address' });
    }
    if (!validatePassword(password)) {
      return fail(400, { message: 'Invalid password (min 6, max 255 characters)' });
    }

    const existingUsers = await db
      .select({ id: table.user.id })
      .from(table.user)
      .where(eq(table.user.username, username));

    if (existingUsers.length > 0) {
      return fail(400, { message: 'Username already taken' });
    }

    const existingEmails = await db
      .select({ id: table.user.id })
      .from(table.user)
      .where(eq(table.user.email, email));

    if (existingEmails.length > 0) {
      return fail(400, { message: 'Email already registered' });
    }

    const userId = generateUserId();

    const passwordHash = await hashPassword(password);

    try {
      await db.insert(table.user).values({
        id: userId,
        username,
        email,
        passwordHash,
        role: 'admin',
        isActive: true,
        isApproved: true,
        organization
      });

      return {
        success: true,
        message: 'Admin account created successfully',
        adminId: userId
      };
    } catch (error) {
      console.error('Admin creation error:', error);
      return fail(500, { message: 'An error occurred while creating the admin account' });
    }
  }
};

function generateUserId() {
  const bytes = crypto.getRandomValues(new Uint8Array(15));
  const id = encodeBase32LowerCase(bytes);
  return id;
}

function validateUsername(username: unknown): username is string {
  return (
    typeof username === 'string' &&
    username.length >= 3 &&
    username.length <= 31 &&
    /^[a-z0-9_-]+$/.test(username)
  );
}

function validateEmail(email: unknown): email is string {
  return (
    typeof email === 'string' &&
    email.length >= 3 &&
    email.length <= 255 &&
    /^[^@]+@[^@]+\.[^@]+$/.test(email)
  );
}

function validatePassword(password: unknown): password is string {
  return (
    typeof password === 'string' &&
    password.length >= 6 &&
    password.length <= 255
  );
}