import { json, error } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import { getAbsoluteFilePath } from '$lib/server/storage/fileStorage';
import fs from 'fs';

export const DELETE: RequestHandler = async ({ params, locals }) => {
  const { user } = locals;
  const { submissionId } = params;

  if (!user) {
    throw error(401, 'Unauthorized');
  }

  try {
    const [submission] = await db
      .select()
      .from(table.submission)
      .where(eq(table.submission.id, submissionId));

    if (!submission) {
      throw error(404, 'Submission not found');
    }

    if (submission.studentId !== user.id && user.role !== 'admin' && user.role !== 'developer') {
      throw error(403, 'You are not authorized to delete this submission');
    }

    await db
      .delete(table.csFile)
      .where(eq(table.csFile.submissionId, submissionId));

    await db
      .delete(table.complexityAnalysis)
      .where(eq(table.complexityAnalysis.submissionId, submissionId));

    await db
      .delete(table.submissionAttempt)
      .where(eq(table.submissionAttempt.submissionId, submissionId));
    try {
      const absolutePath = getAbsoluteFilePath(submission.filePath);
      if (fs.existsSync(absolutePath)) {
        fs.unlinkSync(absolutePath);
        console.log(`Deleted PDF file: ${absolutePath}`);
      }
    } catch (error) {
      console.error(`Error deleting PDF file: ${error}`);
    }

    await db
      .delete(table.submission)
      .where(eq(table.submission.id, submissionId));

    return json({
      success: true,
      message: 'Submission deleted successfully'
    });
  } catch (err: any) {
    console.error('Error deleting submission:', err);
    throw error(500, err.message || 'An error occurred while deleting the submission');
  }
};
