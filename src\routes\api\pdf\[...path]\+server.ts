import { getAbsoluteFilePath, fileExists } from '$lib/server/storage/fileStorage';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import fs from 'fs';
import path from 'path';
import type { RequestHandler } from './$types';
import { requireAuth } from '$lib/utils/auth';
import { fileDownloadResponse, forbiddenResponse, notFoundResponse, errorResponse } from '$lib/server/api/responseUtils';

export const GET: RequestHandler = async ({ params, locals }) => {
  const user = requireAuth(locals.user);

  // Get file path and check if it exists
  const filePath = `/uploads/${params.path}`;
  if (!fileExists(filePath)) return notFoundResponse('File not found');

  const studentId = params.path.split('/')[0];
  let isAuthorized = user.id === studentId;

  // If not student file, check if lecturer has access
  if (!isAuthorized && user.role === 'lecturer') {
    const submission = await db.select({ projectCreator: table.project.createdBy })
      .from(table.submission)
      .innerJoin(table.project, eq(table.submission.projectId, table.project.id))
      .where(eq(table.submission.filePath, filePath))
      .limit(1);

    isAuthorized = submission.length > 0 && submission[0].projectCreator === user.id;
  }

  if (!isAuthorized) return forbiddenResponse('You do not have permission to access this file');

  try {
    // Serve PDF file
    const fileBuffer = fs.readFileSync(getAbsoluteFilePath(filePath));
    return fileDownloadResponse(
      fileBuffer,
      path.basename(filePath),
      'application/pdf',
      'inline',
      'no-cache'
    );
  } catch (err) {
    console.error('Error serving PDF:', err);
    return errorResponse('Error serving file', 500);
  }
};
