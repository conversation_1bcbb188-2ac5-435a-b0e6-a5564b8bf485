<script lang="ts">
    import {
      Card,
      ActionButton,
      ListItem,
      Button,
      PageHeader,
      DashboardSection,
      EmptyState,
      ViewAllLink,
      BatchProcessModal
    } from '$lib/components/ui';

    let { data } = $props();

    let showBatchProcessModal = $state(false);

    async function handleBatchProcess(formData: FormData) {
      const response = await fetch('/api/batch-process', {
        method: 'POST',
        body: formData
      });

      return await response.json();
    }
  </script>

  <div>
    <div class="flex justify-between items-center mb-6">
      <div>
        <PageHeader
          title="Lecturer Dashboard"
          subtitle={data.organization ? `Organization: ${data.organization.name}` : undefined}
          class="mb-0"
        />
      </div>
      <Button onClick={() => showBatchProcessModal = true}>
        Batch Process from ZIP
      </Button>
    </div>

    <DashboardSection cols="2" gap="6">
      <Card title="Quick Actions">
        <div class="space-y-3">
          <ActionButton
            href="/dashboard/lecturer/groups/new"
            color="purple"
            title="Create New Group"
            description="Create a new student group with lecture schedule"
          />
          <ActionButton
            href="/dashboard/lecturer/groups"
            color="purple"
            title="Manage Groups"
            description="View and manage your student groups"
          />
          <ActionButton
            href="/dashboard/lecturer/projects/new"
            color="green"
            title="Create New Project"
            description="Set up a new testing project"
          />
          <ActionButton
            href="/dashboard/lecturer/projects"
            color="blue"
            title="Manage Projects"
            description="View and edit your existing projects"
          />
        </div>
      </Card>

      <Card title="Recent Projects">
        {#if data.recentProjects && data.recentProjects.length > 0}
          <ul class="space-y-2">
            {#each data.recentProjects as project}
              <ListItem href={`/dashboard/lecturer/projects/${project.id}`}>
                <div class="flex justify-between">
                  <span class="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors">
                    {project.name}
                  </span>
                  <span class="text-sm text-gray-500 dark:text-gray-400">{new Date(project.updatedAt).toLocaleDateString()}</span>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 truncate">{project.description || 'No description'}</p>
              </ListItem>
            {/each}
          </ul>
        {:else}
          <EmptyState
            message="No projects created yet. Create your first project to get started."
            icon="folder"
          />
        {/if}
      </Card>
    </DashboardSection>

    <DashboardSection cols="2" gap="6">
      <Card title="Recent Submissions">
        {#if data.recentSubmissions && data.recentSubmissions.length > 0}
          <ul class="space-y-2">
            {#each data.recentSubmissions as submission}
              <ListItem href={`/dashboard/lecturer/projects/${submission.project.id}`}>
                <div class="flex justify-between">
                  <span class="font-medium text-gray-900 dark:text-white">{submission.originalFilename}</span>
                  <span class="text-sm text-gray-500 dark:text-gray-400">{new Date(submission.submittedAt).toLocaleDateString()}</span>
                </div>
                <div class="flex justify-between mt-1">
                  <span class="text-sm text-gray-600 dark:text-gray-400">Project: {submission.project.name}</span>
                  <span class="text-sm text-gray-600 dark:text-gray-400">Student: {submission.student.username}</span>
                </div>
              </ListItem>
            {/each}
          </ul>
        {:else}
          <EmptyState
            message="No submissions received yet."
            icon="document"
          />
        {/if}
      </Card>

      <Card title="Recent Groups">
        {#if data.recentGroups && data.recentGroups.length > 0}
          <ul class="space-y-2">
            {#each data.recentGroups as group}
              <ListItem href={`/dashboard/lecturer/groups/${group.id}`}>
                <div class="flex justify-between">
                  <span class="font-medium text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 transition-colors">
                    {group.name}
                  </span>
                  <span class="text-sm text-gray-500 dark:text-gray-400">{new Date(group.createdAt).toLocaleDateString()}</span>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 truncate">{group.description || 'No description'}</p>
              </ListItem>
            {/each}
            <ViewAllLink
              href="/dashboard/lecturer/groups"
              label="View all groups"
            />
          </ul>
        {:else}
          <EmptyState
            message="No groups created yet. Create your first group to get started."
            icon="users"
          />
        {/if}
      </Card>
    </DashboardSection>
  </div>

  <BatchProcessModal
    show={showBatchProcessModal}
    onclose={() => showBatchProcessModal = false}
    onprocess={handleBatchProcess}
  />