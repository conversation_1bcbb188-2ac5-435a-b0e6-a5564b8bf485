import { writable } from 'svelte/store';

export const loading = (() => {
  const { subscribe, set, update } = writable(false);

  return {
    subscribe,
    start: () => set(true),
    stop: () => set(false),
    toggle: () => update(n => !n)
  };
})();
export function createLoadingContext(initialState = false) {
  const { subscribe, set, update } = writable(initialState);

  return {
    subscribe,
    start: () => set(true),
    stop: () => set(false),
    toggle: () => update(n => !n)
  };
}
