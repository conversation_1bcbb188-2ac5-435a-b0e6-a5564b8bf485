<script lang="ts">
  const {
    items = [],
    class: className = ''
  } = $props<{
    items: Array<{key: string, value: string | number}>,
    class?: string
  }>();
  const listClass = $derived(`
    space-y-2 ${className}
  `);
</script>

<ul class={listClass}>
  {#each items as item, i}
    <li class="flex justify-between p-2 {i < items.length - 1 ? 'border-b border-gray-200 dark:border-gray-700' : ''}">
      <span class="text-gray-600 dark:text-gray-400">{item.key}</span>
      <span class="font-semibold text-gray-900 dark:text-white">{item.value}</span>
    </li>
  {/each}
</ul>
