import { fail, redirect } from '@sveltejs/kit';
import { eq } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;

  if (!user || user.role !== 'developer') {
    return redirect(303, '/auth/login');
  }

  return {
    user
  };
};

export const actions: Actions = {
  createOrganization: async (event) => {
    const { user } = event.locals;

    if (!user || user.role !== 'developer') {
      return fail(403, { message: 'Unauthorized' });
    }

    const formData = await event.request.formData();
    const name = formData.get('name')?.toString();
    const description = formData.get('description')?.toString() || null;
    const isActive = formData.has('isActive');

    if (!name) {
      return fail(400, { message: 'Organization name is required' });
    }

    // Check if same name already exists
    const existingOrgs = await db
      .select({ id: table.organization.id })
      .from(table.organization)
      .where(eq(table.organization.name, name));

    if (existingOrgs.length > 0) {
      return fail(400, { message: 'An organization with this name already exists' });
    }

    try {
      await db.insert(table.organization).values({
        id: crypto.randomUUID(),
        name,
        description,
        isActive,
        createdBy: user.id,
        createdAt: new Date()
      });

      return {
        success: true,
        message: 'Organization created successfully'
      };
    } catch (error) {
      console.error('Organization creation error:', error);
      return fail(500, { message: 'An error occurred while creating the organization' });
    }
  }
};
