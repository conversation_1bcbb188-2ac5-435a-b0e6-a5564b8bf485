import { verifyPassword } from '$lib/server/auth/password';
import { fail, redirect } from '@sveltejs/kit';
import { eq } from 'drizzle-orm';
import * as auth from '$lib/server/auth';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { Actions, PageServerLoad } from './$types';
import { validateUsername, validatePassword } from '$lib/utils/validation';
import { requireUnauthenticated, redirectToRoleDashboard } from '$lib/utils/auth';
import { validateLoginAttempt, isAccountLocked } from '$lib/server/auth/accountLockout';
import { validateEmailSecure, validateTextInput } from '$lib/server/security/inputValidation';

export const load: PageServerLoad = async (event) => {
	requireUnauthenticated(event.locals.user);
	return {};
};

export const actions: Actions = {
	login: async (event) => {
		const formData = await event.request.formData();
		const username = formData.get('username');
		const password = formData.get('password');

		// Get client information for security logging
		const ipAddress = event.getClientAddress();
		const userAgent = event.request.headers.get('user-agent');

		// Enhanced input validation
		const usernameValidation = validateTextInput(username, 3, 31);
		if (!usernameValidation.isValid) {
			await validateLoginAttempt(
				String(username || ''),
				null,
				ipAddress,
				userAgent,
				false,
				'Invalid username format'
			);
			return fail(400, { message: 'Invalid username format' });
		}

		const passwordValidation = validateTextInput(password, 1, 255);
		if (!passwordValidation.isValid) {
			await validateLoginAttempt(
				usernameValidation.sanitized!,
				null,
				ipAddress,
				userAgent,
				false,
				'Invalid password format'
			);
			return fail(400, { message: 'Invalid password format' });
		}

		const sanitizedUsername = usernameValidation.sanitized!;
		const sanitizedPassword = passwordValidation.sanitized!;

		// Look up user
		const results = await db
			.select()
			.from(table.user)
			.where(eq(table.user.username, sanitizedUsername));

		const existingUser = results.at(0);

		// Check account lockout status first (if user exists)
		if (existingUser) {
			const lockStatus = await isAccountLocked(existingUser.id);
			if (lockStatus.isLocked) {
				await validateLoginAttempt(
					sanitizedUsername,
					existingUser.id,
					ipAddress,
					userAgent,
					false,
					'Account locked'
				);
				return fail(423, {
					message: `Account is locked until ${lockStatus.lockoutUntil?.toLocaleString()}. Please try again later.`
				});
			}
		}

		// Verify password (always do this to prevent user enumeration timing attacks)
		let validPassword = false;
		let failureReason = 'Invalid credentials';

		if (existingUser) {
			if (!existingUser.isActive) {
				failureReason = 'Account deactivated';
			} else {
				validPassword = await verifyPassword(existingUser.passwordHash, sanitizedPassword);
				if (!validPassword) {
					failureReason = 'Invalid password';
				}
			}
		} else {
			// Perform dummy password verification to prevent timing attacks
			await verifyPassword('$argon2id$v=19$m=65536,t=3,p=1$dummy$dummy', sanitizedPassword);
			failureReason = 'User not found';
		}

		// Handle login attempt validation and potential lockout
		const attemptResult = await validateLoginAttempt(
			sanitizedUsername,
			existingUser?.id || null,
			ipAddress,
			userAgent,
			validPassword,
			validPassword ? undefined : failureReason
		);

		if (!attemptResult.success) {
			if (attemptResult.isLocked) {
				return fail(423, { message: attemptResult.message });
			}

			// Generic error message to prevent user enumeration
			let errorMessage = 'Incorrect username or password';
			if (attemptResult.remainingAttempts !== undefined && attemptResult.remainingAttempts <= 2) {
				errorMessage += `. ${attemptResult.remainingAttempts} attempts remaining before account lockout.`;
			}

			return fail(400, { message: errorMessage });
		}

		// Successful login - create session
		if (existingUser && validPassword) {
			const sessionToken = auth.generateSessionToken();
			const session = await auth.createSession(sessionToken, existingUser.id);
			auth.setSessionTokenCookie(event, sessionToken, session.expiresAt);

			return redirectToRoleDashboard(existingUser, 302);
		}

		// This should never be reached, but just in case
		return fail(400, { message: 'Login failed' });
	}
};

