<script lang="ts">
  import {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
    AlertMessage,
    ManualTableInput
  } from '$lib/components/ui';
  import type { ExtractedTable } from '$lib/types';

  let fileInputRef = $state<HTMLInputElement | null>(null);
  let selectedFile = $state<File | null>(null);
  let dragActive = $state(false);
  let isAnalyzing = $state(false);
  let analysisResults = $state<any>(null);
  let error = $state<string | null>(null);

  function handleFileChange(e: Event) {
    const input = e.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      selectedFile = input.files[0];
    }
  }

  async function analyzeFile() {
    if (!selectedFile) {
      error = "Please select a PDF file first";
      return;
    }

    if (selectedFile.type !== 'application/pdf') {
      error = "Only PDF files are supported";
      return;
    }

    isAnalyzing = true;
    error = null;
    analysisResults = null;

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);

      const response = await fetch('/api/analyze-pdf-text', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to analyze PDF');
      }

      analysisResults = await response.json();
      console.log('Analysis results:', analysisResults);
    } catch (err) {
      console.error('Error analyzing PDF:', err);
      error = err instanceof Error ? err.message : 'An unknown error occurred';
    } finally {
      isAnalyzing = false;
    }
  }

  let activeTab = $state('included'); // 'included', 'excluded', or 'tables'

  function resetForm() {
    selectedFile = null;
    analysisResults = null;
    error = null;
    if (fileInputRef) {
      fileInputRef.value = '';
    }
  }


</script>

<div class="container mx-auto px-4 py-8">
  <div class="mb-6">
    <PageHeader
      title="PDF Text Analysis"
      subtitle="Upload a PDF to analyze its content for time-related terms in Lithuanian"
    />
  </div>

  {#if !analysisResults}
    <Card class="mb-6">
      <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Upload PDF</h2>

      <div>
        <label for="file-upload" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Upload PDF File</label>
        <div
          role="button"
          tabindex="0"
          aria-label="Drop zone for file upload"
          class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 {dragActive ? 'border-indigo-300 bg-indigo-50 dark:border-indigo-700 dark:bg-indigo-900/30' : 'border-gray-300 dark:border-gray-600'} border-dashed rounded-md transition-colors duration-200"
          ondragenter={(e) => {
            e.preventDefault();
            e.stopPropagation();
            dragActive = true;
          }}
          ondragover={(e) => {
            e.preventDefault();
            e.stopPropagation();
            dragActive = true;
          }}
          ondragleave={(e) => {
            e.preventDefault();
            e.stopPropagation();
            dragActive = false;
          }}
          ondrop={(e) => {
            e.preventDefault();
            e.stopPropagation();
            dragActive = false;

            if (e.dataTransfer?.files && e.dataTransfer.files.length > 0) {
              selectedFile = e.dataTransfer.files[0];
              if (fileInputRef) {
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(selectedFile);
                fileInputRef.files = dataTransfer.files;
              }
            }
          }}
          onclick={() => {
            if (fileInputRef) {
              fileInputRef.click();
            }
          }}
          onkeydown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              if (fileInputRef) {
                fileInputRef.click();
              }
            }
          }}
        >
          <div class="space-y-1 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            <div class="flex text-sm text-gray-600 dark:text-gray-400">
              <label for="file-upload" class="relative cursor-pointer bg-white dark:bg-gray-700 rounded-md font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 focus-within:outline-none">
                <span>Upload a PDF</span>
                <input
                  id="file-upload"
                  name="file"
                  type="file"
                  accept=".pdf,application/pdf"
                  class="sr-only"
                  bind:this={fileInputRef}
                  onchange={(e) => handleFileChange(e)}
                />
              </label>
              <p class="pl-1">or drag and drop</p>
            </div>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              PDF files only, up to 20MB
            </p>
            {#if dragActive}
              <p class="text-sm text-indigo-600 dark:text-indigo-400 font-medium">
                Drop file here
              </p>
            {/if}
          </div>
        </div>
        {#if selectedFile}
          <div class="mt-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
            <div class="flex items-center">
              <svg class="h-5 w-5 text-gray-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
              </svg>
              <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{selectedFile.name}</span>
              <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">
                {selectedFile.size < 1024
                  ? `${selectedFile.size} bytes`
                  : selectedFile.size < 1024 * 1024
                  ? `${(selectedFile.size / 1024).toFixed(1)} KB`
                  : `${(selectedFile.size / (1024 * 1024)).toFixed(1)} MB`}
              </span>
              <button
                type="button"
                aria-label="Remove selected file"
                title="Remove selected file"
                class="ml-auto text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                onclick={() => {
                  selectedFile = null;
                  if (fileInputRef) {
                    fileInputRef.value = '';
                  }
                }}
              >
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        {/if}
      </div>

      {#if error}
        <AlertMessage
          type="error"
          message={error}
          class="mt-4"
        />
      {/if}

      <div class="mt-6">
        <Button
          type="button"
          variant="primary"
          disabled={!selectedFile || isAnalyzing}
          onClick={analyzeFile}
        >
          {#if isAnalyzing}
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Analyzing...
          {:else}
            Analyze PDF
          {/if}
        </Button>
      </div>
    </Card>
  {:else}
    <Card class="mb-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-medium text-gray-900 dark:text-white">Analysis Results</h2>
        <Button
          type="button"
          variant="light"
          onClick={resetForm}
        >
          Analyze Another PDF
        </Button>
      </div>

      <div class="mb-4">
        <p class="text-sm text-gray-500 dark:text-gray-400">
          Analyzed {analysisResults.totalPages} pages in "{analysisResults.originalFilename}".
          Found {analysisResults.includedPages?.length || 0} pages with time-related terms.
          Excluded {analysisResults.excludedPages?.length || 0} pages.
        </p>
        <div class="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-md text-sm">
          <p class="font-medium mb-1">Filtering Rules Applied:</p>
          <ul class="list-disc list-inside space-y-1 ml-2">
            <li>Excluded all pages before and including the Table of Contents (Turinys)</li>
            <li>Excluded all pages after and including the TURNYS section</li>
            <li>Excluded all pages after and including the IŠVADOS (Conclusions) section</li>
            <li>Excluded pages with titles containing terms like "uždavinys" or "sąlyga"</li>
            <li>Excluded pages that contain only code</li>
            <li>Kept only pages containing time-related terms like "laikas", "gylis", etc.</li>
          </ul>
        </div>
      </div>

      <!-- Tabs -->
      <div class="border-b border-gray-200 dark:border-gray-700 mb-4">
        <nav class="flex -mb-px">
          <button
            class={`py-2 px-4 text-sm font-medium ${activeTab === 'included'
              ? 'border-b-2 border-indigo-500 text-indigo-600 dark:text-indigo-400'
              : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'}`}
            onclick={() => activeTab = 'included'}
            aria-label="Show included pages"
          >
            Included Pages ({analysisResults.includedPages?.length || 0})
          </button>
          <button
            class={`ml-8 py-2 px-4 text-sm font-medium ${activeTab === 'excluded'
              ? 'border-b-2 border-indigo-500 text-indigo-600 dark:text-indigo-400'
              : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'}`}
            onclick={() => activeTab = 'excluded'}
            aria-label="Show excluded pages"
          >
            Excluded Pages ({analysisResults.excludedPages?.length || 0})
          </button>
          <button
            class={`ml-8 py-2 px-4 text-sm font-medium ${activeTab === 'tables'
              ? 'border-b-2 border-indigo-500 text-indigo-600 dark:text-indigo-400'
              : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'}`}
            onclick={() => activeTab = 'tables'}
            aria-label="Show extracted tables"
          >
            Extracted Tables ({analysisResults.extractedTables?.length || 0})
          </button>
        </nav>
      </div>

      <!-- Included Pages Tab -->
      {#if activeTab === 'included'}
        {#if analysisResults.includedPages && analysisResults.includedPages.length > 0}
          <div class="space-y-6">
            {#each analysisResults.includedPages as page}
              <div class="border dark:border-gray-700 rounded-lg overflow-hidden">
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-2 border-b dark:border-gray-600 flex justify-between items-center">
                  <h3 class="font-medium">Page {page.pageNumber}</h3>
                  <span class="text-xs px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded-full">
                    Included
                  </span>
                </div>
                <div class="p-4 bg-white dark:bg-gray-800">
                  <p class="whitespace-pre-wrap font-mono text-sm">{page.text}</p>
                </div>
              </div>
            {/each}
          </div>
        {:else}
          <AlertMessage
            type="warning"
            message="No pages with time-related terms found in this PDF."
          />
        {/if}

      <!-- Excluded Pages Tab -->
      {:else if activeTab === 'excluded'}
        {#if analysisResults.excludedPages && analysisResults.excludedPages.length > 0}
          <div class="space-y-6">
            {#each analysisResults.excludedPages as page}
              <div class="border dark:border-gray-700 rounded-lg overflow-hidden">
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-2 border-b dark:border-gray-600 flex justify-between items-center">
                  <h3 class="font-medium">Page {page.pageNumber}</h3>
                  <span class="text-xs px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 rounded-full">
                    Excluded: {page.excludeReason}
                  </span>
                </div>
                <div class="p-4 bg-white dark:bg-gray-800">
                  <p class="whitespace-pre-wrap font-mono text-sm">{page.text}</p>
                </div>
              </div>
            {/each}
          </div>
        {:else}
          <AlertMessage
            type="warning"
            message="No excluded pages in this PDF."
          />
        {/if}
      <!-- Tables Tab -->
      {:else if activeTab === 'tables'}
        {#if analysisResults.extractedTables && analysisResults.extractedTables.length > 0}
          <div class="space-y-8">
            {#each analysisResults.extractedTables as table, index}
              <div class="border dark:border-gray-700 rounded-lg overflow-hidden">
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-2 border-b dark:border-gray-600">
                  <h3 class="font-medium">{table.name || `Table ${index + 1}`}</h3>
                </div>
                <div class="p-4 bg-white dark:bg-gray-800">
                  <!-- Table Description -->
                  <div class="mb-4">
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                      This table shows the relationship between time and depth values.
                    </p>
                  </div>

                  <!-- Data Table -->
                  <div class="overflow-x-auto mt-4">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Time</th>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Depth</th>
                        </tr>
                      </thead>
                      <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {#each table.time as _, i}
                          {#if i < table.time.length && i < table.depth.length}
                            <tr class={i % 2 === 0 ? 'bg-white dark:bg-gray-800' : 'bg-gray-50 dark:bg-gray-700'}>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{table.time[i]}</td>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{table.depth[i]}</td>
                            </tr>
                          {/if}
                        {/each}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            {/each}
          </div>
        {:else}
          <ManualTableInput onSave={(tables: ExtractedTable[]) => {
            analysisResults.extractedTables = tables;
          }} />
        {/if}
      {/if}
    </Card>
  {/if}
</div>
