<script lang="ts">
  import { Modal, AlertMessage, Button, FormInput } from '$lib/components/ui';

  let {
    show = false,
    submissionId = null,
    onclose = () => {},
    onsubmit = () => {}
  } = $props<{
    show: boolean;
    submissionId?: string | null;
    onclose?: (data?: { cancelled: boolean }) => void;
    onsubmit?: (data: { submissionId: string, tableData: { depth: number[], time: number[] } }) => void;
  }>();

  let depthValues = $state('');
  let timeValues = $state('');
  let isSubmitting = $state(false);
  let error = $state('');
  let processingMessage = $state('');

  function handleClose(cancelled = true) {
    onclose({ cancelled });
  }

  function resetForm() {
    depthValues = '';
    timeValues = '';
    error = '';
    isSubmitting = false;
    processingMessage = '';
  }

  async function handleSubmit() {
    error = '';

    // Validate inputs
    if (!depthValues.trim() || !timeValues.trim()) {
      error = 'Both depth and time values are required';
      return;
    }

    // Parse depth values
    const depthArray = depthValues.split(',').map(v => {
      const parsed = parseFloat(v.trim());
      return isNaN(parsed) ? null : parsed;
    }).filter(v => v !== null) as number[];

    // Parse time values
    const timeArray = timeValues.split(',').map(v => {
      const parsed = parseFloat(v.trim());
      return isNaN(parsed) ? null : parsed;
    }).filter(v => v !== null) as number[];

    if (depthArray.length === 0 || timeArray.length === 0) {
      error = 'Please enter valid numeric values separated by commas';
      return;
    }

    if (depthArray.length !== timeArray.length) {
      error = 'The number of depth values must match the number of time values';
      return;
    }

    // Check if there are 3 or more identical time values
    const timeValueCounts = new Map<number, number>();
    for (const time of timeArray) {
      timeValueCounts.set(time, (timeValueCounts.get(time) || 0) + 1);
    }

    for (const [_, count] of timeValueCounts.entries()) {
      if (count >= 3) {
        error = 'The table appears to have 3 or more identical time values, which is invalid. Please check your data.';
        return;
      }
    }

    isSubmitting = true;
    processingMessage = 'Submitting data...';

    try {
      if (submissionId) {
        onsubmit({
          submissionId,
          tableData: {
            depth: depthArray,
            time: timeArray
          }
        });
        handleClose(false);
      }
    } catch (err) {
      console.error('Error submitting manual data:', err);
      error = 'An error occurred while submitting the data';
      processingMessage = '';
      isSubmitting = false;
    }
  }

  $effect(() => {
    if (show) resetForm();
  });
</script>

<Modal
  {show}
  title="Manual Data Entry"
  onClose={handleClose}
  size="md"
>
  <div>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400 mb-4">
      No time/depth table was detected in your PDF. Please enter the data manually.
    </p>

    <form onsubmit={(e) => { e.preventDefault(); handleSubmit(); return false; }} class="space-y-4">
      <FormInput
        id="depth-values"
        name="depth-values"
        label="Depth Values (comma separated)"
        value={depthValues}
        onChange={(e: Event) => depthValues = (e.target as HTMLInputElement).value}
        placeholder="1, 2, 3, 4, 5"
        helpText="Enter recursion depth values separated by commas (e.g., 1, 2, 3, 4, 5)"
      />

      <FormInput
        id="time-values"
        name="time-values"
        label="Time Values (comma separated)"
        value={timeValues}
        onChange={(e: Event) => timeValues = (e.target as HTMLInputElement).value}
        placeholder="0.1, 0.2, 0.4, 0.8, 1.6"
        helpText="Enter time values in seconds separated by commas (e.g., 0.1, 0.2, 0.4, 0.8, 1.6)"
      />

      {#if error}
        <AlertMessage
          type="error"
          message={error}
        />
      {/if}

      {#if processingMessage && isSubmitting}
        <AlertMessage
          type="info"
          message={processingMessage}
          showIcon={true}
        />
      {/if}

      <div class="flex justify-end space-x-3 pt-4">
        <Button
          type="button"
          variant="secondary"
          onClick={handleClose}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="primary"
          disabled={isSubmitting}
        >
          {#if isSubmitting}
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Submitting...
          {:else}
            Submit Data
          {/if}
        </Button>
      </div>
    </form>
  </div>
</Modal>
