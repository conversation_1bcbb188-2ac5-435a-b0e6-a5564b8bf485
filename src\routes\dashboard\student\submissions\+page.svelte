<script lang="ts">
  import {
    <PERSON><PERSON><PERSON><PERSON>,
    Card,
    EmptyState,
    Button,
    PDFViewerModal
  } from '$lib/components/ui';

  let { data } = $props();

  let showPdfViewer = $state(false);
  let currentPdfUrl = $state('');
  let currentPdfTitle = $state('');
  let currentSubmissionId = $state('');

  function formatDate(dateString: string | Date): string {
    return new Date(dateString).toLocaleDateString();
  }

  function formatTime(dateString: string | Date): string {
    return new Date(dateString).toLocaleTimeString();
  }

  function formatFileSize(bytes: number): string {
    if (bytes < 1024) {
      return bytes + ' bytes';
    } else if (bytes < 1024 * 1024) {
      return (bytes / 1024).toFixed(1) + ' KB';
    } else {
      return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    }
  }

  function viewPdf(submission: any) {
    const pathParts = submission.filePath.split('/');
    const studentId = pathParts[2]; // /uploads/studentId/filename

    currentPdfUrl = `/api/pdf/${studentId}/${pathParts.slice(3).join('/')}`;
    currentPdfTitle = submission.originalFilename;
    currentSubmissionId = submission.id;
    showPdfViewer = true;
  }

  function closePdfViewer() {
    showPdfViewer = false;
  }
</script>

<div>
  <PageHeader title="My Submissions" class="mb-6" />

  {#if data.submissions.length === 0}
    <Card>
      <EmptyState
        icon="document"
        message="No submissions. You haven't submitted any files yet."
      />
      <div class="mt-6 text-center">
        <Button
          variant="primary"
          onClick={() => window.location.href = '/dashboard/student/submit'}
        >
          Submit Document
        </Button>
      </div>
    </Card>
  {:else}
    <Card>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                File
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Project
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Submitted
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Size
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            {#each data.submissions as submission}
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <svg class="flex-shrink-0 h-5 w-5 text-gray-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                    </svg>
                    <div class="text-sm font-medium text-gray-900 truncate max-w-xs">
                      {submission.originalFilename}
                    </div>
                    <Button
                      variant="light"
                      size="sm"
                      onClick={() => viewPdf(submission)}
                      class="ml-2 p-1"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </Button>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{submission.project?.name || 'Unknown Project'}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{formatDate(submission.submittedAt)}</div>
                  <div class="text-sm text-gray-500">{formatTime(submission.submittedAt)}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{formatFileSize(submission.fileSize)}</div>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    </Card>
  {/if}
</div>

<PDFViewerModal
  show={showPdfViewer}
  url={currentPdfUrl}
  title={currentPdfTitle}
  submissionId={currentSubmissionId}
  userRole="student"
  onclose={closePdfViewer}
/>