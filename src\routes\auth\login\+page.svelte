<script lang="ts">
	import { enhance } from '$app/forms';
	import type { ActionData } from './$types';
	import { locale, t } from '$lib/stores/locale';
	import { Button, AuthLayout, FormInput, AlertMessage } from '$lib/components/ui';

	let { form }: { form: ActionData } = $props();

	// Use reactive store access instead of manual subscription
	const currentLocale = $derived($locale);
</script>

<AuthLayout title={t('auth.loginToAccount', currentLocale)}>
	<form class="mt-10 space-y-8" method="POST" action="?/login" use:enhance>
		<div class="space-y-6">
			<FormInput
				id="username"
				name="username"
				type="text"
				label={t('auth.username', currentLocale)}
				placeholder={t('auth.username', currentLocale)}
				required
			/>

			<FormInput
				id="password"
				name="password"
				type="password"
				label={t('auth.password', currentLocale)}
				placeholder={t('auth.password', currentLocale)}
				required
			/>
		</div>

		{#if form?.message}
			<AlertMessage
				type="error"
				message={form.message}
			/>
		{/if}

		<div class="pt-2">
			<Button
				type="submit"
				variant="primary"
				size="lg"
				class="w-full"
			>
				{t('auth.login', currentLocale)}
			</Button>
		</div>

		<div class="mt-4 text-center text-base text-gray-600 dark:text-gray-400">
			<a href="/auth/register" class="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors">
				{t('auth.register', currentLocale)}
			</a>
			<span class="mx-2">•</span>
			<a href="/auth/forgot-password" class="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors">
				Forgot Password?
			</a>
		</div>
	</form>
</AuthLayout>