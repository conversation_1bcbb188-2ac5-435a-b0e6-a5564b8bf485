import { redirect } from '@sveltejs/kit';
import { eq, and, inArray, or } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;

  if (!user || user.role !== 'student') {
    return redirect(303, '/auth/login');
  }

  // Get all group memberships for the current user
  const groupMemberships = await db
    .select({
      id: table.groupMember.id,
      groupId: table.groupMember.groupId,
      addedAt: table.groupMember.addedAt,
      group: {
        name: table.group.name,
        description: table.group.description
      }
    })
    .from(table.groupMember)
    .innerJoin(
      table.group,
      eq(table.groupMember.groupId, table.group.id)
    )
    .where(eq(table.groupMember.studentId, user.id));

  // If no groups, return 
  if (groupMemberships.length === 0) {
    return {
      user,
      groups: []
    };
  }

  // Extract all group IDs
  const groupIds = groupMemberships.map(membership => membership.groupId);

  // Get all lecture times for all groups
  const allLectureTimes = await db
    .select({
      groupId: table.lectureTime.groupId,
      id: table.lectureTime.id,
      weekday: table.lectureTime.weekday,
      startTime: table.lectureTime.startTime,
      endTime: table.lectureTime.endTime,
      location: table.lectureTime.location
    })
    .from(table.lectureTime)
    .where(inArray(table.lectureTime.groupId, groupIds));

  // Get all project groups for all groups
  const allGroupProjects = await db
    .select({
      id: table.projectGroup.id,
      groupId: table.projectGroup.groupId,
      projectId: table.projectGroup.projectId,
      project: {
        id: table.project.id,
        name: table.project.name,
        description: table.project.description,
        isHidden: table.project.isHidden
      }
    })
    .from(table.projectGroup)
    .innerJoin(
      table.project,
      eq(table.projectGroup.projectId, table.project.id)
    )
    .where(
      and(
        inArray(table.projectGroup.groupId, groupIds),
        eq(table.project.isHidden, false)
      )
    );

  const projectIds = allGroupProjects.map(gp => gp.projectId);

  // Get all individual assignments for the current user
  const individualAssignments = await db
    .select({
      projectId: table.projectStudent.projectId
    })
    .from(table.projectStudent)
    .where(
      and(
        inArray(table.projectStudent.projectId, projectIds),
        eq(table.projectStudent.studentId, user.id)
      )
    );

  // Create set of individually assigned project IDs
  const individuallyAssignedProjectIds = new Set(
    individualAssignments.map(a => a.projectId)
  );

  // Get all group members for all groups
  const allGroupMembers = await db
    .select({
      groupId: table.groupMember.groupId,
      studentId: table.groupMember.studentId
    })
    .from(table.groupMember)
    .where(inArray(table.groupMember.groupId, groupIds));

  // Get all project students for all projects in a single query
  const allProjectStudents = await db
    .select({
      projectId: table.projectStudent.projectId,
      studentId: table.projectStudent.studentId
    })
    .from(table.projectStudent)
    .where(inArray(table.projectStudent.projectId, projectIds));

  // Create lookup maps
  const lectureTimesByGroupId = new Map();
  allLectureTimes.forEach(lt => {
    if (!lectureTimesByGroupId.has(lt.groupId)) {
      lectureTimesByGroupId.set(lt.groupId, []);
    }
    lectureTimesByGroupId.get(lt.groupId).push(lt);
  });

  const projectsByGroupId = new Map();
  allGroupProjects.forEach(gp => {
    if (!projectsByGroupId.has(gp.groupId)) {
      projectsByGroupId.set(gp.groupId, []);
    }
    projectsByGroupId.get(gp.groupId).push(gp);
  });

  // Group members by group ID
  const membersByGroupId = new Map();
  allGroupMembers.forEach(member => {
    if (!membersByGroupId.has(member.groupId)) {
      membersByGroupId.set(member.groupId, []);
    }
    membersByGroupId.get(member.groupId).push(member.studentId);
  });

  // Project students by project ID
  const studentsByProjectId = new Map();
  allProjectStudents.forEach(ps => {
    if (!studentsByProjectId.has(ps.projectId)) {
      studentsByProjectId.set(ps.projectId, []);
    }
    studentsByProjectId.get(ps.projectId).push(ps.studentId);
  });

  // Process all groups with their details
  const groupsWithDetails = groupMemberships.map(membership => {
    const lectureTimes = lectureTimesByGroupId.get(membership.groupId) || [];
    const groupProjects = projectsByGroupId.get(membership.groupId) || [];

    // Filter projects based on assignment criteria
    const filteredProjects = groupProjects.filter((project: { projectId: string }) => {
      if (individuallyAssignedProjectIds.has(project.projectId)) {
        return true;
      }
      const assignedStudentIds = studentsByProjectId.get(project.projectId) || [];
      const assignedStudentSet = new Set(assignedStudentIds);
      return assignedStudentSet.has(user.id);
    });

    return {
      ...membership,
      lectureTimes,
      projects: filteredProjects
    };
  });

  return {
    user,
    groups: groupsWithDetails
  };
};
