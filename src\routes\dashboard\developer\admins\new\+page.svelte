<script lang="ts">
  import { enhance } from '$app/forms';
  import {
    PageHeader,
    Card,
    Button,
    FormInput,
    FormSelect,
    AlertMessage,
    BackButton
  } from '$lib/components/ui';

  type Organization = {
    id: string;
    name: string;
    isActive?: boolean;
    isFrozen?: boolean;
  };

  interface PageData {
    user: {
      id: string;
      username: string;
      role: "student" | "lecturer" | "admin" | "developer";
      isActive: boolean;
      isApproved: boolean;
      email: string;
      organization: string | null;
    };
    organizations: Organization[];
  }

  let { data, form } = $props<{ data: PageData, form: any }>();
</script>

<div>
  <div class="mb-6 flex items-center space-x-3">
    <BackButton href="/dashboard/developer/admins" label="Back to Admins" />
    <PageHeader title="Create New Admin" class="mb-0" />
  </div>

  {#if form?.success}
    <AlertMessage
      type="success"
      message={form.message}
      actionText="Go back to admin management"
      actionHref="/dashboard/developer/admins"
      class="mb-6"
    />
  {:else if form?.message}
    <AlertMessage
      type="error"
      message={form.message}
      class="mb-6"
    />
  {/if}

  <Card>
    <form method="POST" action="?/createAdmin" use:enhance class="space-y-6">
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <FormInput
          id="username"
          name="username"
          label="Username"
          required
          placeholder="admin_username"

          helpText="3-31 characters, lowercase letters, numbers, underscores, and hyphens only"
        />

        <FormInput
          id="email"
          name="email"
          type="email"
          label="Email"
          required
          placeholder="<EMAIL>"
        />

        <FormInput
          id="password"
          name="password"
          type="password"
          label="Password"
          required
          minlength={6}
          helpText="Minimum 6 characters"
        />

        <FormSelect
          id="organization"
          name="organization"
          label="Organization"
          helpText="Organization this admin belongs to"
          options={[
            { value: '', label: 'No Organization' },
            ...data.organizations.map((org: Organization) => ({
              value: org.id,
              label: `${org.name} ${org.isFrozen ? '(Frozen)' : !org.isActive ? '(Inactive)' : ''}`,
              className: org.isFrozen ? 'text-red-500' : org.isActive ? 'text-green-500' : 'text-gray-500'
            }))
          ]}
        />
      </div>

      <div>
        <Button
          type="submit"
          variant="primary"
        >
          Create Admin
        </Button>
      </div>
    </form>
  </Card>
</div>