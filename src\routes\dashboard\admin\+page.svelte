<script lang="ts">
  import {
    PageHeader,
    DashboardSection,
    StatLink,
    Card,
    ActionButton,
    KeyValueList
  } from '$lib/components/ui';

  let { data } = $props();

  const statsItems = [
    {
      key: `Total Users${data.user?.organization ? ' (Organization)' : ''}`,
      value: data.stats.totalUsers
    },
    {
      key: `Active Users${data.user?.organization ? ' (Organization)' : ''}`,
      value: data.stats.activeUsers
    },
    {
      key: 'Total Projects',
      value: data.stats.totalProjects
    },
    {
      key: 'Total Submissions',
      value: data.stats.totalSubmissions
    }
  ];
</script>

<div>
  <PageHeader title="Admin Dashboard" />

  <DashboardSection cols="3" gap="6">
    <StatLink
      title="Pending Approvals"
      value={data.pendingApprovals}
      href="/dashboard/admin/approvals"
      linkText="View all"
    />

    <StatLink
      title="Active Users"
      value={data.stats.activeUsers}
      href="/dashboard/admin/users"
      linkText="Manage users"
    />
  </DashboardSection>

  <DashboardSection cols="2" gap="6">
    <Card title="Quick Actions">
      <div class="space-y-3">
        <ActionButton
          href="/dashboard/admin/users"
          color="green"
          title="Manage Organization Users"
          description="Manage users in your organization"
        />
      </div>
    </Card>

    <Card title="System Stats">
      <KeyValueList items={statsItems} />
    </Card>
  </DashboardSection>
</div>