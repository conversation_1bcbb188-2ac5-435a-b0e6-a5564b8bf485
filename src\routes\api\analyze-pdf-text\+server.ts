import type { Request<PERSON><PERSON><PERSON> } from './$types';
import { saveFile, getAbsoluteFilePath } from '$lib/server/storage/fileStorage';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import type { ExtractedTable } from '$lib/types';
import { analyzePdfContent } from '$lib/server/api/claude-api';
import { extractTextFromPDF, filterPages } from '$lib/server/pdf/pdfAnalysis';
import { successResponse, errorResponse, forbiddenResponse, unauthorizedResponse } from '$lib/server/api/responseUtils';

const TEMP_DIR = path.join(process.cwd(), 'temp');
if (!fs.existsSync(TEMP_DIR)) fs.mkdirSync(TEMP_DIR, { recursive: true });

export const POST: RequestHandler = async ({ request, locals }) => {
  const { user } = locals;
  if (!user) return unauthorizedResponse();
  if (user.role !== 'developer') return forbiddenResponse();

  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    if (!file) return errorResponse('No file provided');
    if (file.type !== 'application/pdf') return errorResponse('Only PDF files are supported');

    const buffer = Buffer.from(await file.arrayBuffer());
    const tempId = crypto.randomUUID();
    const tempFilePath = await saveFile(tempId, buffer, file.name);
    const absoluteFilePath = getAbsoluteFilePath(tempFilePath);
    const extractedPages = await extractTextFromPDF(absoluteFilePath);
    const processedPages = filterPages(extractedPages);
    const includedPages = processedPages.filter(page => !page.isExcluded);
    const excludedPages = processedPages.filter(page => page.isExcluded);

    let extractedTables: ExtractedTable[] = [];
    if (includedPages.length > 0) {
      try {
        const pdfBuffer = fs.readFileSync(absoluteFilePath);
        const includedPageNumbers = includedPages.map(page => page.pageNumber);
        console.log(`Sending ${includedPageNumbers.length} filtered pages to Claude API: ${includedPageNumbers.join(', ')}`);
        const analysisResult = await analyzePdfContent(pdfBuffer, extractedPages.length, includedPageNumbers);
        extractedTables = analysisResult.tables || [];
      } catch (error) {
        console.error('Error analyzing PDF content:', error);
      }
    }

    return successResponse({
      originalFilename: file.name,
      totalPages: extractedPages.length,
      includedPages, excludedPages, allPages: processedPages, extractedTables
    });
  } catch (err) {
    return errorResponse(err instanceof Error ? err.message : 'Failed to analyze PDF', 500);
  }
};
