import fs from 'fs';
import path from 'path';
import axios from 'axios';
import { exec } from 'child_process';
import { promisify } from 'util';
import os from 'os';
import type { ExtractedTable, ClaudeAnalysisResponse } from '$lib/types';
import { claudeApiLimiter } from '$lib/server/security/rateLimiter';

const execPromise = promisify(exec);

function convertTimeToSeconds(timeValue: string | number): number {
  try {
    if (typeof timeValue === 'number') return timeValue;
    if (!timeValue) return 0;

    const str = String(timeValue).toLowerCase().trim();

    if (str.includes(':')) {
      const p = str.split(':');
      try {
        if (p.length === 3) {
          const hours = parseInt(p[0]) || 0;
          const minutes = parseInt(p[1]) || 0;
          const seconds = parseFloat(p[2]) || 0;
          return hours * 3600 + minutes * 60 + seconds;
        } else {
          const minutes = parseInt(p[0]) || 0;
          const seconds = parseFloat(p[1]) || 0;
          return minutes * 60 + seconds;
        }
      } catch (e) {
        console.error(`Error parsing time format: ${str}`, e);
        return 0;
      }
    }

    if (str.endsWith('ms')) {
      const val = parseFloat(str.replace('ms', ''));
      return isNaN(val) ? 0 : val / 1000;
    }

    if (str.endsWith('µs') || str.endsWith('us')) {
      const val = parseFloat(str.replace(/[µu]s/, ''));
      return isNaN(val) ? 0 : val / 1000000;
    }

    const val = parseFloat(str);
    return isNaN(val) ? 0 : val;
  } catch (e) {
    console.error(`Failed to convert time value: ${timeValue}`, e);
    return 0;
  }
}

function containsMathSymbols(str: string): boolean {
  if (typeof str !== 'string') return false;

  // Mathematical symbols
  const mathSymbols = [
    '∑', '∫', '∏', '∈', '∉', '∋', '∌', '∩', '∪', '⊂', '⊃', '⊆', '⊇', '≠', '≈', '≡', '≤', '≥',
    '±', '∓', '÷', '×', '∝', '∞', '∇', '∂', '√', '∛', '∜', '∴', '∵', '∼', '∽', '≅', '≈', '≠',
    '≡', '≤', '≥', '⊕', '⊗', '⊥', '⋅', '⌈', '⌉', '⌊', '⌋', '⟨', '⟩', '∀', '∃', '∄', '∅', '∆'
  ];

  // Check for notations 
  const variablePatterns = [
    /\bx\d*\b/, // x, x1, x2
    /\by\d*\b/, // y, y1, y2
    /\bz\d*\b/, // z, z1, z2
    /\bn\d*\b/, // n, n1, n2
    /\bi\d*\b/, // i, i1, i2
    /\bj\d*\b/, // j, j1, j2
    /\bk\d*\b/, // k, k1, k2
    /\[.*\]/,
    /\(.*\)/,
  ];

  // Check mathematical
  const expressionPatterns = [/\+/, /\-(?!\d)/, /\//, /\*/, /\^/, /\=/, /\</, /\>/,];

  // Check for math symbols
  for (const symbol of mathSymbols) {
    if (str.includes(symbol)) {
      return true;
    }
  }

  // Check for variable patterns
  for (const pattern of variablePatterns) {
    if (pattern.test(str)) {
      // Check format like 1:30
      if (!/^\d+:\d+$/.test(str) && !/^\d+\.\d+$/.test(str) && !/^\d+$/.test(str)) {
        return true;
      }
    }
  }

  // Check for expression patterns
  for (const pattern of expressionPatterns) {
    if (pattern.test(str)) {
      // Check format like 1:30
      if (!/^\d+:\d+$/.test(str) && !/^\d+\.\d+$/.test(str) && !/^\d+$/.test(str)) {
        return true;
      }
    }
  }

  return false;
}

function processTimeValues(timeData: any): number[] {
  try {
    if (!timeData) return [];

    if (Array.isArray(timeData)) {
      return timeData.map(item => {
        try {
          const value = item && typeof item === 'object' && item.value !== undefined ? item.value : item;
          return convertTimeToSeconds(value);
        } catch (e) {
          console.error(`Error processing time array item:`, e);
          return 0;
        }
      });
    }

    if (timeData && typeof timeData === 'object') {
      const value = timeData.value !== undefined ? timeData.value : timeData;
      return [convertTimeToSeconds(value)];
    }

    return [convertTimeToSeconds(timeData)];
  } catch (e) {
    console.error(`Error processing time values:`, e);
    return [];
  }
}

async function createSlicedPdf(sourcePdfPath: string, pageNumbers: number[], outputPath: string): Promise<void> {
  const { PDFDocument } = await import('pdf-lib');
  const sourcePdf = await PDFDocument.load(fs.readFileSync(sourcePdfPath));
  const newPdf = await PDFDocument.create();

  const validIndices = pageNumbers
    .map(num => num - 1)
    .filter(idx => idx >= 0 && idx < sourcePdf.getPageCount());

  if (validIndices.length === 0) {
    fs.writeFileSync(outputPath, await newPdf.save());
    return;
  }

  try {
    (await newPdf.copyPages(sourcePdf, validIndices)).forEach(page => newPdf.addPage(page));
    fs.writeFileSync(outputPath, await newPdf.save());
  } catch (e) {
    for (const idx of validIndices) {
      try {
        const [page] = await newPdf.copyPages(sourcePdf, [idx]);
        newPdf.addPage(page);
      } catch (err) {}
    }
    fs.writeFileSync(outputPath, await newPdf.save());
  }
}

async function convertPdfToImages(pdfPath: string, outputDir: string): Promise<Array<{ pageNumber: number, base64Image: string }>> {
  const { PDFDocument } = await import('pdf-lib');
  const pdfDoc = await PDFDocument.load(fs.readFileSync(pdfPath));
  const pageCount = pdfDoc.getPageCount();

  const batchSize = Math.max(1, Math.min(4, os.cpus().length, pageCount));

  const convertPage = async (pageIndex: number) => {
    const pageNum = pageIndex + 1;
    const outputPath = path.join(outputDir, `output-${pageIndex}.png`);

    await execPromise(`magick -density 300 -colorspace RGB "${pdfPath}"[${pageIndex}] -resize 1092x1092 -quality 90 -flatten "${outputPath}"`);

    const imageBuffer = fs.readFileSync(outputPath);
    fs.unlinkSync(outputPath);

    return {
      pageNumber: pageNum,
      base64Image: imageBuffer.toString('base64')
    };
  };

  const results = [];
  for (let i = 0; i < pageCount; i += batchSize) {
    const batch = Array.from({length: Math.min(batchSize, pageCount - i)}, (_, j) => convertPage(i + j));
    results.push(...await Promise.all(batch));
  }

  return results.sort((a, b) => a.pageNumber - b.pageNumber);
}

export async function analyzePdfContent(pdfBuffer: Buffer, pageCount: number, includedPageNumbers?: number[], userId?: string): Promise<ClaudeAnalysisResponse> {
  if (!process.env.CLAUDE_API_KEY) {
    console.error('CLAUDE_API_KEY is not set');
    return { success: false, error: 'API key not configured', tables: [] };
  }

  // Apply rate limiting if userId is provided
  if (userId) {
    const rateLimitResult = claudeApiLimiter.checkLimit(userId);
    if (!rateLimitResult.allowed) {
      console.warn(`Rate limit exceeded for user ${userId}`);
      return {
        success: false,
        error: `Rate limit exceeded. Please try again in ${Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000)} seconds.`,
        tables: []
      };
    }
  }

  try {
    console.log(`Analyzing PDF with ${pageCount} total pages`);

    const tempDir = path.join(process.cwd(), 'temp', 'claude-images');
    if (!fs.existsSync(tempDir)) fs.mkdirSync(tempDir, { recursive: true });

    const tempPdfPath = path.join(tempDir, `temp-${Date.now()}.pdf`);
    fs.writeFileSync(tempPdfPath, pdfBuffer);

    let pdfToProcess = tempPdfPath;
    if (includedPageNumbers && includedPageNumbers.length > 0) {
      console.log(`Filtering PDF to include only ${includedPageNumbers.length} pages: ${includedPageNumbers.join(', ')}`);
      const slicedPdfPath = path.join(tempDir, `sliced-${Date.now()}.pdf`);
      await createSlicedPdf(tempPdfPath, includedPageNumbers, slicedPdfPath);
      pdfToProcess = slicedPdfPath;
    } else {
      console.log('No page filtering applied, processing entire PDF');
    }

    const pageImages = await convertPdfToImages(pdfToProcess, tempDir);
    if (pageImages.length === 0) {
      console.error('Failed to convert pages to images');
      return { success: false, error: 'Failed to convert PDF to images', tables: [] };
    }

    console.log(`Number of images converted: ${pageImages.length}`);

    const tables = await callClaudeApi(pageImages);
    console.log(`Number of tables extracted: ${tables.length}`);

    try {
      fs.unlinkSync(tempPdfPath);
      if (pdfToProcess !== tempPdfPath) {
        fs.unlinkSync(pdfToProcess);
      }
    } catch (e) {}

    return {
      success: true,
      tables,
      includedPages: pageImages.map(img => ({
        pageNumber: includedPageNumbers ? includedPageNumbers[img.pageNumber - 1] : img.pageNumber,
        text: '',
        hasTimeTerms: true,
        isExcluded: false
      })),
      excludedPages: []
    };
  } catch (error) {
    console.error('Error in analyzePdfContent:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      tables: []
    };
  }
}

async function callClaudeApi(pageImages: Array<{ pageNumber: number, base64Image: string }>): Promise<ExtractedTable[]> {
  console.log(`Total images received: ${pageImages.length}`);
  const validImages = pageImages.filter(page => page.base64Image.length >= 1000);
  console.log(`Valid images after filtering: ${validImages.length}`);

  if (validImages.length === 0) {
    console.log('No valid images to process, returning empty result');
    return [];
  }

  const MAX_IMAGES_PER_BATCH = 20;
  console.log(`Using ${validImages.length > MAX_IMAGES_PER_BATCH ? 'batched' : 'single batch'} processing`);

  return validImages.length > MAX_IMAGES_PER_BATCH
    ? await processBatchedImages(validImages, MAX_IMAGES_PER_BATCH)
    : await processSingleBatch(validImages);
}

async function processBatchedImages(images: Array<{ pageNumber: number, base64Image: string }>, batchSize: number): Promise<ExtractedTable[]> {
  const allTables: ExtractedTable[] = [];
  const batches = Array.from({ length: Math.ceil(images.length / batchSize) },
                             (_, i) => images.slice(i * batchSize, (i + 1) * batchSize));

  for (let i = 0; i < batches.length; i++) {
    try {
      const batchTables = await processSingleBatch(batches[i]);
      for (const table of batchTables) {
        if (!allTables.some(existing =>
          existing.name === table.name &&
          JSON.stringify(existing.time) === JSON.stringify(table.time) &&
          JSON.stringify(existing.depth) === JSON.stringify(table.depth)
        )) {
          allTables.push(table);
        }
      }
    } catch (error) {}
  }

  return allTables;
}

async function processSingleBatch(images: Array<{ pageNumber: number, base64Image: string }>): Promise<ExtractedTable[]> {
  console.log(`Processing batch with ${images.length} images`);

  const messages = [{
    role: "user",
    content: [
      {
        type: "text",
        text: "Extract tables from Lithuanian academic documents that have BOTH 'laikas' (time) AND 'gylis' or 'rekursijos gylis' (recursion depth) specifically labeled in their headers. IGNORE GRAPHS, CHARTS and values next to code blocks. DO NOT work with or extract tables where: One of the columns is 'kartai','Paveikslelio dydis' (image size) or dimensions like '1000x1000, or 'Rezoliucija', 'Operacijų skaičius' or 'veiksmų skaičius' (operations count). Tables with measurements that don't have a true recursion depth\nKeep all time values in their original format. Do not convert time values to seconds. Return a JSON object with this exact structure: { \"tables\": [ { \"table_id\": number, \"description\": \"brief description of table location\", \"data\": [ { \"gylis\": number, \"laikas\": string }, ... ] } ] }. Note that 'laikas' should be a string containing the original time value. Time is not formulas (like '(1-x) ∑ ∑ [i]'), and if the table contains duplicate time values, the table is incorrect. Sort data by gylis (ascending). If no tables are found, return an empty array. Return only the JSON object with no explanations. State where the data was found table, chart, graph, etc. in the description."      },        ...images.map(page => ({
        type: "image",
        source: { type: "base64", media_type: "image/png", data: page.base64Image }
      }))
    ]
  }];

  console.log(`Sending request to Claude API...`);
  const response = await axios.post(
    'https://api.anthropic.com/v1/messages',
    {
      model: "claude-3-5-haiku-20241022",
      max_tokens: 4096,
      messages: messages
    },
    {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.CLAUDE_API_KEY,
        'anthropic-version': '2023-06-01'
      }
    }
  );

  const responseText = response.data.content[0].text;
  console.log(`Claude API Response:\n${responseText}`);

  const tables: ExtractedTable[] = [];

  try {
    const extractJson = (text: string) => {
      const jsonStart = text.indexOf('{');
      const jsonEnd = text.lastIndexOf('}') + 1;
      return (jsonStart >= 0 && jsonEnd > jsonStart) ? text.substring(jsonStart, jsonEnd) : null;
    };

    const jsonStr = extractJson(responseText);
    if (jsonStr) {
      const data = JSON.parse(jsonStr);

      if (data.tables && Array.isArray(data.tables)) {
        for (const table of data.tables) {
          if (table.table_id !== undefined && Array.isArray(table.data) && table.data.length > 0) {
            if (table.description && typeof table.description === 'string' &&
                (table.description.toLowerCase().includes('graph') ||
                 table.description.toLowerCase().includes('chart') ||
                 table.description.toLowerCase().includes('kiekio') ||
                 table.description.toLowerCase().includes('grafikas') ||
                 table.description.toLowerCase().includes('pixel') ||
                 table.description.toLowerCase().includes('piksel') ||
                 table.description.toLowerCase().includes('count') ||
                 /\brezoliuc\w*/i.test(table.description.toLowerCase()))) {
              console.log(`Skipping table ${table.table_id} because its description mentions graph, chart, quantity, pixel, or words starting with "rezoliuc"`);
              continue;
            }

            const hasMathSymbols = table.data.some((point: any) =>
              point && point.laikas !== undefined &&
              typeof point.laikas === 'string' &&
              containsMathSymbols(point.laikas)
            );

            const hasRezoliuc = table.data.some((point: any) => {
              return Object.entries(point).some(([key, value]) => {
                const keyStr = String(key).toLowerCase();
                const valueStr = value !== undefined ? String(value).toLowerCase() : '';
                return keyStr.includes('rezoliuc') || /\brezoliuc\w*/i.test(valueStr);
              });
            });

            // Skip tables with mathematical symbols or rezoliuc
            if (hasMathSymbols) {
              console.log(`Skipping table ${table.table_id} because it contains mathematical symbols`);
              continue;
            }

            if (hasRezoliuc) {
              console.log(`Skipping table ${table.table_id} because it contains words starting with "rezoliuc"`);
              continue;
            }

            const gylis: number[] = [];
            const laikas: number[] = [];

            for (const point of table.data) {
              try {
                if (point && point.gylis !== undefined && point.laikas !== undefined) {
                  const depthValue = Number(point.gylis);
                  const timeValue = convertTimeToSeconds(point.laikas);

                  if (!isNaN(depthValue)) {
                    gylis.push(depthValue);
                    laikas.push(timeValue);
                  } else {
                    console.warn(`Invalid depth value: ${point.gylis}`);
                  }
                }
              } catch (e) {
                console.error(`Error processing data point:`, e);
              }
            }

            if (gylis.length > 0 && laikas.length > 0) {
              tables.push({ name: `Table ${table.table_id}`, time: laikas, depth: gylis });
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('Error processing Claude API response:', error);
  }

  return tables;
}