<script lang="ts">
	import { enhance } from '$app/forms';
	import type { ActionData } from './$types';
	import { locale } from '$lib/stores/locale';
	import { Button, AuthLayout, FormInput, AlertMessage } from '$lib/components/ui';

	let { form }: { form: ActionData } = $props();

	// Use reactive store access instead of manual subscription
	const currentLocale = $derived($locale);
</script>

<AuthLayout
	title="Forgot Password"
	subtitle="Enter your email address and we'll send you a link to reset your password."
	backHref="/auth/login"
>
	{#if form?.success}
		<AlertMessage
			type="success"
			message={form.message}
		/>
	{:else}
		<form class="mt-10 space-y-8" method="POST" action="?/forgotPassword" use:enhance>
			<FormInput
				id="email"
				name="email"
				type="email"
				label="Email Address"
				placeholder="Enter your email address"
				required
			/>

			{#if form?.error}
				<AlertMessage
					type="error"
					message={form.message}
				/>
			{/if}

			<div class="pt-2">
				<Button
					type="submit"
					variant="primary"
					size="lg"
					class="w-full"
				>
					Send Reset Link
				</Button>
			</div>

			<p class="mt-4 text-center text-base text-gray-600 dark:text-gray-400">
				Remember your password? <a href="/auth/login" class="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors">
					Sign in
				</a>
			</p>
		</form>
	{/if}
</AuthLayout>
