<script lang="ts">
  import { enhance } from '$app/forms';
  import {
    <PERSON><PERSON><PERSON>er,
    BackButton,
    FormInput,
    Button,
    Card
  } from '$lib/components/ui';

  type Organization = {
    id: string;
    name: string;
    description: string | null;
    isActive: boolean;
    createdAt: string;
    createdBy: string | null;
  };

  type Admin = {
    id: string;
    username: string;
    email: string;
    isActive: boolean;
  };

  interface PageData {
    user: {
      id: string;
      username: string;
      role: "student" | "lecturer" | "admin" | "developer";
    };
    organization: Organization;
    admins: Admin[];
    createdByUser: {
      username: string;
      email: string;
    } | null;
  }

  let { data } = $props<{ data: PageData, form?: any }>();

  function formatDate(dateString: string | Date | null | undefined): string {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString();
  }
</script>

<div>
  <div class="mb-6 flex items-center space-x-3">
    <BackButton href="/dashboard/developer/organizations" label="Back to Organizations" />
    <PageHeader title="Organization: {data.organization.name}" />
  </div>

  <Card class="mb-6">
    <h2 class="text-xl font-semibold mb-4">Organization Details</h2>

    <form method="POST" action="?/updateOrganization" use:enhance class="space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormInput
          id="name"
          name="name"
          label="Name"
          value={data.organization.name}
          required
        />

        <FormInput
          id="description"
          name="description"
          label="Description"
          value={data.organization.description || ''}
        />

        <div>
          <div class="block text-sm font-medium text-gray-700 mb-1">Status</div>
          <div class="flex items-center space-x-4">
            <label class="inline-flex items-center" for="isActive">
              <input
                type="checkbox"
                id="isActive"
                name="isActive"
                checked={data.organization.isActive}
                class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700">Active</span>
            </label>

            <label class="inline-flex items-center" for="isFrozen">
              <input
                type="checkbox"
                id="isFrozen"
                name="isFrozen"
                checked={data.organization.isFrozen}
                class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700">Frozen</span>
            </label>
          </div>
        </div>

        <div>
          <div class="block text-sm font-medium text-gray-700 mb-1">Created</div>
          <div class="text-sm text-gray-700">
            <p>Date: {formatDate(data.organization.createdAt)}</p>
            <p>By: {data.createdByUser ? data.createdByUser.username : 'Unknown'}</p>
          </div>
        </div>
      </div>

      <div>
        <Button
          type="submit"
          variant="primary"
        >
          Update Organization
        </Button>
      </div>
    </form>
  </Card>


  <Card>
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
      <h2 class="text-xl font-semibold">Organization Admins</h2>
      <Button
        variant="primary"
        onClick={() => window.location.href = '/dashboard/developer/admins/new'}
      >
        Create New Admin
      </Button>
    </div>

    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Username
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Email
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          {#if data.admins.length === 0}
            <tr>
              <td colspan="4" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                No admins in this organization
              </td>
            </tr>
          {:else}
            {#each data.admins as admin}
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="font-medium text-gray-900">{admin.username}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {admin.email}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  {#if admin.isActive}
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      Active
                    </span>
                  {:else}
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                      Inactive
                    </span>
                  {/if}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <Button
                    variant="light"
                    size="sm"
                    onClick={() => window.location.href = `/dashboard/developer/admins/${admin.id}`}
                  >
                    Edit
                  </Button>
                </td>
              </tr>
            {/each}
          {/if}
        </tbody>
      </table>
    </div>
  </Card>
</div>
