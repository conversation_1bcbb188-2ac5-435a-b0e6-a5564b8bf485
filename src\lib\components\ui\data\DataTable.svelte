<script lang="ts">
  const {
    columns = [],
    data = [],
    class: className = ''
  } = $props<{
    columns: Array<{key: string, label: string}>,
    data: Array<Record<string, any>>,
    class?: string
  }>();
  const containerClass = $derived(`
    overflow-x-auto ${className}
  `);
</script>

<div class={containerClass}>
  <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
    <thead class="bg-gray-50 dark:bg-gray-700">
      <tr>
        {#each columns as column}
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
            {column.label}
          </th>
        {/each}
      </tr>
    </thead>
    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
      {#each data as row}
        <tr>
          {#each columns as column}
            <td class="px-6 py-4 whitespace-nowrap">
              {@html row[column.key]}
            </td>
          {/each}
        </tr>
      {/each}
    </tbody>
  </table>
</div>
