import fs from 'fs';
import pdfParse from 'pdf-parse';
import type { ExtractedPage } from '$lib/types';

export const TIME_RELATED_TERMS = new Set([
  'laikas', 'laiko', 'laiką', 'laiku', 'gylis', 'gylio', 'gylį', 'gyliu',
  'prik<PERSON>omyb<PERSON>', 'priklausomybės', 'priklausomybę', 'trukm<PERSON>', 'trukmės', 'trukmę',
  'greitis', 'greičio', 'greitį', 'periodas', 'periodo', 'periodą',
  'uždavinys', 'uždavinio', 'uždavinį', 'uždaviniu', 'dažnis', 'daž<PERSON>', 'dažnį',
  'O(n)', 'O(log n)', 'O(n log n)', 'O(n²)', 'O(n^2)', 'O(2^n)', 'O(n!)',
  'kvadratin', 'tiesin', 'logaritm', 'eksponent', 'faktorial'
]);

export const EXCLUDE_TERMS = new Set([
  'turnys', 'turinys', 'sąlyga', 'sąly<PERSON>', 'sąlygą', 'išvados',
  'bibliograf', 'literatūr', 'šaltini'
]);

export async function extractTextFromPDF(filePath: string): Promise<ExtractedPage[]> {
  try {
    if (!fs.existsSync(filePath)) {
      console.error(`PDF file does not exist: ${filePath}`);
      throw new Error('PDF file not found');
    }

    const pdfBytes = fs.readFileSync(filePath);

    if (pdfBytes.length === 0) {
      console.error(`PDF file is empty: ${filePath}`);
      throw new Error('PDF file is empty');
    }

    const pdfSignature = pdfBytes.slice(0, 5).toString();
    if (!pdfSignature.startsWith('%PDF')) {
      console.error(`Invalid PDF signature: ${pdfSignature}`);
      throw new Error('Invalid PDF structure: File does not appear to be a valid PDF');
    }
    let pdfData: Awaited<ReturnType<typeof pdfParse>>;
    try {
      pdfData = await pdfParse(pdfBytes, {
        pagerender: async (pageData: any) => {
          try {
            const textContent = await pageData.getTextContent();
            if (!textContent || !textContent.items) {
              return '';
            }

            return textContent.items
              .map((item: any) => item.str || '')
              .join(' ');
          } catch (pageError) {
            console.warn('Error rendering PDF page:', pageError);
            return '';
          }
        }
      });
    } catch (error) {
      const parseError = error as Error;
      console.error('Error parsing PDF:', parseError);
      throw new Error(`Invalid PDF structure: ${parseError.message || 'Unable to parse PDF'}`);
    }

    let pageTexts: string[] = [];

    if (pdfData.text.includes('\f')) {
      pageTexts = pdfData.text.split('\f').filter(text => text.trim().length > 0);
    }
    if (pageTexts.length < pdfData.numpages * 0.7) {
      const pageBreakPatterns = [
        /\n\s*(\d+)\s*\n/g,
        /\n\s*Page\s+(\d+)\s+of\s+\d+\s*\n/gi,
        /\n\s*-\s*(\d+)\s*-\s*\n/g
      ];

      for (const pattern of pageBreakPatterns) {
        const matches = [...pdfData.text.matchAll(pattern)];
        if (matches.length > 0) {
          pageTexts = [];
          let lastIndex = 0;

          for (const match of matches) {
            if (match.index && match.index > lastIndex) {
              const pageText = pdfData.text.substring(lastIndex, match.index).trim();
              if (pageText.length > 0) pageTexts.push(pageText);
              lastIndex = match.index;
            }
          }

          if (lastIndex < pdfData.text.length) {
            const pageText = pdfData.text.substring(lastIndex).trim();
            if (pageText.length > 0) pageTexts.push(pageText);
          }

          if (pageTexts.length >= pdfData.numpages * 0.7) break;
        }
      }
    }

    if (pageTexts.length < pdfData.numpages * 0.5) {
      const avgCharsPerPage = Math.ceil(pdfData.text.length / pdfData.numpages);
      pageTexts = [];

      for (let i = 0; i < pdfData.numpages; i++) {
        const start = i * avgCharsPerPage;
        const end = Math.min(start + avgCharsPerPage, pdfData.text.length);
        const lookAhead = Math.min(end + 150, pdfData.text.length);

        let adjustedEnd = end;
        const nextParagraph = pdfData.text.indexOf('\n\n', end);

        if (nextParagraph > 0 && nextParagraph < lookAhead) {
          adjustedEnd = nextParagraph + 2;
        } else {
          const sentenceEndRegex = /[.!?]\s+/g;
          for (const match of pdfData.text.substring(end, lookAhead).matchAll(sentenceEndRegex)) {
            if (match.index !== undefined) {
              adjustedEnd = end + match.index + match[0].length;
              break;
            }
          }
        }

        const pageText = pdfData.text.substring(start, adjustedEnd).trim();
        if (pageText.length > 0) pageTexts.push(pageText);
      }
    }

    const extractedPages: ExtractedPage[] = [];

    for (let i = 0; i < pageTexts.length; i++) {
      const pageNum = i + 1;
      const pageText = pageTexts[i].trim();

      if (pageText.length === 0) {
        continue;
      }

      const isCodePage = isPageContainingOnlyCode(pageText);
      const hasTimeTerms = containsTimeTerms(pageText);

      extractedPages.push({
        pageNumber: pageNum,
        text: pageText,
        hasTimeTerms: hasTimeTerms,
        hasExcludeTerms: containsExcludeTerms(pageText) || (isCodePage && !hasTimeTerms),
        isExcluded: isCodePage && !hasTimeTerms, // Will be updated during filtering
        isCodePage: isCodePage
      });
    }

    if (extractedPages.length === 0) {
      throw new Error('No pages could be extracted from the PDF');
    }

    return extractedPages;
  } catch (error) {
    console.error('Error extracting text from PDF:', error);
    const errorMessage = error instanceof Error
      ? error.message
      : 'Unknown error processing PDF';
    throw new Error(`Error extracting text from PDF: ${errorMessage}`);
  }
}

export function isPageContainingOnlyCode(text: string): boolean {
  if (!text || text.trim().length === 0) {
    return false;
  }

  const codeIndicators = [
    'public', 'private', 'class', 'function', 'def ', 'return', 'import',
    'var ', 'const ', 'let ', 'if(', 'if (', 'for(', 'for (', 'while(', 'while (',
    '{', '}', '=>', '->', '==', '===', '!=', '!==', '&&', '||',
    'int ', 'string ', 'bool ', 'void ', 'float ', 'double '
  ];

  const lines = text.split('\n').filter(line => line.trim().length > 0);
  let codeLines = 0;

  for (const line of lines) {
    const trimmedLine = line.trim();

    if (codeIndicators.some(indicator => trimmedLine.includes(indicator)) ||
        /^\s{2,}[a-zA-Z0-9_]+/.test(trimmedLine) ||
        /[a-zA-Z0-9_]+\([^)]*\)/.test(trimmedLine)) {
      codeLines++;
    }
  }

  return lines.length > 0 && (codeLines / lines.length) > 0.7;
}

export function containsTimeTerms(text: string): boolean {
  if (!text) return false;

  const lowerText = text.toLowerCase();
  for (const term of TIME_RELATED_TERMS) {
    if (lowerText.includes(term.toLowerCase())) return true;
  }
  return false;
}

export function containsExcludeTerms(text: string): boolean {
  if (!text) return false;

  const lowerText = text.toLowerCase();

  if (lowerText.includes('turnys') || lowerText.includes('turinys')) return true;

  const lines = text.split('\n');
  if (lines.length > 0) {
    const linesToCheck = Math.min(3, lines.length);
    for (let i = 0; i < linesToCheck; i++) {
      for (const term of EXCLUDE_TERMS) {
        if (lines[i].toLowerCase().includes(term.toLowerCase())) return true;
      }
    }
  }

  return false;
}

export function filterPages(pages: ExtractedPage[]): ExtractedPage[] {
  const processedPages = JSON.parse(JSON.stringify(pages)) as ExtractedPage[];
  let turinysSectionIndex = -1;
  let turnysPageIndex = -1;
  let isvadasPageIndex = -1; // Track IŠVADOS (conclusions) section

  for (let i = 0; i < processedPages.length; i++) {
    const page = processedPages[i];
    const lowerText = page.text.toLowerCase();

    const lines = page.text.split('\n');
    const firstFewLines = lines.slice(0, Math.min(3, lines.length)).join(' ').toLowerCase();

    if (firstFewLines.includes('turinys')) {
      turinysSectionIndex = i;
    }

    if (lowerText.includes('turnys')) {
      turnysPageIndex = i;
    }

    if (lines.length > 0 && lines[0].trim().toLowerCase() === 'išvados') {
      isvadasPageIndex = i;
    }
  }

  for (let i = 0; i < processedPages.length; i++) {
    const page = processedPages[i];

    if (turinysSectionIndex >= 0 && i <= turinysSectionIndex) {
      page.isExcluded = true;
      page.excludeReason = i === turinysSectionIndex ?
        'Contains Table of Contents (Turinys)' :
        'Appears before Table of Contents (Turinys)';
      continue;
    }

    if (turnysPageIndex >= 0 && i >= turnysPageIndex) {
      page.isExcluded = true;
      page.excludeReason = i === turnysPageIndex ?
        'Contains TURNYS section' :
        'Appears after TURNYS section';
      continue;
    }

    if (isvadasPageIndex >= 0 && i >= isvadasPageIndex) {
      page.isExcluded = true;
      page.excludeReason = i === isvadasPageIndex ?
        'Contains Conclusions (IŠVADOS) section' :
        'Appears after Conclusions (IŠVADOS) section';
      continue;
    }

    if (!containsTimeTerms(page.text)) {
      page.isExcluded = true;
      page.excludeReason = 'Does not contain any time-related terms';
      continue;
    }

    if (page.isCodePage) {
      page.isExcluded = false;
      page.excludeReason = undefined;
    }
  }

  return processedPages;
}
