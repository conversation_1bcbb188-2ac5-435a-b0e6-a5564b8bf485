import { redirect } from '@sveltejs/kit';
import type { SessionUser } from '$lib/types';

export const ROLE_ROUTES = {
  student: '/dashboard/student',
  lecturer: '/dashboard/lecturer',
  admin: '/dashboard/admin',
  developer: '/dashboard/developer'
} as const;

export function redirectToRoleDashboard(user: SessionUser, status = 303) {
  const targetRoute = ROLE_ROUTES[user.role];

  if (user.role === 'lecturer' && !user.isApproved) {
    return redirect(status, '/pending-approval');
  }

  return redirect(status, targetRoute || '/');
}

export function requireAuth(user: SessionUser | null, status = 303) {
  if (!user) {
    return redirect(status, '/auth/login');
  }
  return user;
}

export function requireUnauthenticated(user: SessionUser | null, status = 302) {
  if (!user) {
    return null;
  }

  if (user.role === 'lecturer' && !user.isApproved) {
    return redirect(status, '/pending-approval');
  }

  return redirect(status, ROLE_ROUTES[user.role] || '/');
}

export function requireRole(user: SessionUser | null, requiredRole: keyof typeof ROLE_ROUTES, status = 303) {
  if (!user) {
    return redirect(status, '/auth/login');
  }

  if (user.role !== requiredRole) {
    return redirectToRoleDashboard(user, status);
  }

  return user;
}
