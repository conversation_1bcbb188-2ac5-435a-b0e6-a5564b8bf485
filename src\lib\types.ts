import type {
  User as DbUser,
  Project as DbProject,
  Group as DbGroup,
  Submission as DbSubmission,
  ComplexityAnalysis as DbComplexityAnalysis
} from '$lib/server/db/schema';
export type UserRole = 'student' | 'lecturer' | 'admin' | 'developer';
export interface SessionUser {
  id: string;
  username: string;
  role: UserRole;
  isActive: boolean;
  isApproved: boolean;
  email: string;
  organization: string | null;
  name?: string | null;
  createdAt?: Date;
  passwordHash?: string;
}

export type User = DbUser;

export type Project = DbProject;
export type Group = DbGroup;
export type ComplexityAnalysis = DbComplexityAnalysis;
export type Submission = DbSubmission & {
  studentName?: string;
  project?: {
    name: string;
    description: string | null;
  };
  complexityAnalysis?: ExtendedComplexityAnalysis;
};

export interface Student {
  id: string;
  username: string;
  name: string | null;
  email: string;
  membershipId?: string;
}

export interface SimpleUser {
  id: string;
  username: string;
  name: string | null;
  email: string;
}

export interface GroupStudent {
  groupId: string;
  groupName: string;
  students: Student[];
}

export interface AssignedStudent {
  id?: string;
  studentId: string;
  student?: Student;
}

export interface ProjectGroup {
  id: string;
  groupId: string;
  group: {
    name: string;
    description: string | null;
  };
}

export interface SimpleLectureTime {
  id: string;
  weekday: 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';
  startTime: string;
  endTime: string;
  location: string | null;
}

// PDF

// Page extracted from a PDF
export interface ExtractedPage {
  pageNumber: number;
  text: string;
  hasTimeTerms?: boolean;
  hasExcludeTerms?: boolean;
  isExcluded?: boolean;
  excludeReason?: string;
  error?: string;
  isCodePage?: boolean;
}

// Table extracted from PDF
export interface ExtractedTable {
  name: string;
  time: number[];
  depth: number[];
}

// Response from Claude API
export interface ClaudeAnalysisResponse {
  success?: boolean;
  tables: ExtractedTable[];
  error?: string;
  includedPages?: ExtractedPage[];
  excludedPages?: ExtractedPage[];
}

// Response for the PDF analysis API
export interface PdfAnalysisResponse {
  success: boolean;
  originalFilename?: string;
  totalPages?: number;
  includedPages?: ExtractedPage[];
  excludedPages?: ExtractedPage[];
  allPages?: ExtractedPage[];
  extractedTables?: ExtractedTable[];
  error?: string;
}

export interface ExtendedComplexityAnalysis extends ComplexityAnalysis {
  parsedTables?: ExtractedTable[];
  parsedIncludedPages?: ExtractedPage[];
  parsedExcludedPages?: ExtractedPage[];
}