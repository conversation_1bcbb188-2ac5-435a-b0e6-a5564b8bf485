import { error, fail, redirect } from '@sveltejs/kit';
import { eq, and, or, sql } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;
  const { adminId } = event.params;

  if (!user || user.role !== 'developer') {
    return redirect(303, '/auth/login');
  }

  const admins = await db
    .select()
    .from(table.user)
    .where(eq(table.user.id, adminId));

  const admin = admins[0];

  if (!admin) {
    throw error(404, 'Admin not found');
  }

  if (admin.role !== 'admin') {
    throw error(400, 'User is not an admin');
  }

  let organizations = [];
  try {
    organizations = await db
      .select({
        id: table.organization.id,
        name: table.organization.name,
        isActive: table.organization.isActive,
        isFrozen: table.organization.isFrozen
      })
      .from(table.organization)
      .where(eq(table.organization.isActive, true))
      .orderBy(table.organization.name);
  } catch (error) {
    console.error('Error fetching organizations with active filter:', error);
    organizations = await db
      .select({
        id: table.organization.id,
        name: table.organization.name
      })
      .from(table.organization)
      .orderBy(table.organization.name);
  }

  let adminOrganization = null;
  if (admin.organization) {
    try {
      const [org] = await db
        .select()
        .from(table.organization)
        .where(eq(table.organization.id, admin.organization));

      const [userCount] = await db
        .select({
          count: sql<number>`count(${table.user.id})`
        })
        .from(table.user)
        .where(
          and(
            eq(table.user.organization, admin.organization),
            or(
              eq(table.user.role, 'student'),
              eq(table.user.role, 'lecturer')
            )
          )
        );

      adminOrganization = {
        ...org,
        userCount: userCount ? userCount.count : 0
      };
    } catch (error) {
      console.error('Error fetching admin organization:', error);
    }
  }

  return {
    user,
    admin,
    organizations,
    adminOrganization
  };
};

export const actions: Actions = {
  updateAdmin: async (event) => {
    const { user } = event.locals;
    const { adminId } = event.params;

    if (!user || user.role !== 'developer') {
      return fail(403, { message: 'Unauthorized' });
    }

    const formData = await event.request.formData();
    const email = formData.get('email')?.toString();
    const isActive = formData.has('isActive');

    if (!email) {
      return fail(400, { message: 'Email is required' });
    }

    try {
      await db
        .update(table.user)
        .set({
          email,
          isActive
        })
        .where(eq(table.user.id, adminId));

      return {
        success: true,
        message: 'Admin updated successfully'
      };
    } catch (error) {
      console.error('Admin update error:', error);
      return fail(500, { message: 'An error occurred while updating the admin' });
    }
  },

  updateOrganization: async (event) => {
    const { user } = event.locals;
    const { adminId } = event.params;

    if (!user || user.role !== 'developer') {
      return fail(403, { message: 'Unauthorized' });
    }

    const formData = await event.request.formData();
    const organizationId = formData.get('organization')?.toString();

    try {
      const [admin] = await db
        .select({ organization: table.user.organization })
        .from(table.user)
        .where(eq(table.user.id, adminId));

      if (!admin) {
        return fail(404, { message: 'Admin not found' });
      }

      if (!organizationId || organizationId === '') {
        await db
          .update(table.user)
          .set({
            organization: null
          })
          .where(eq(table.user.id, adminId));

        return {
          success: true,
          message: 'Admin removed from organization successfully'
        };
      }

      const [organization] = await db
        .select()
        .from(table.organization)
        .where(eq(table.organization.id, organizationId));

      if (!organization) {
        return fail(404, { message: 'Organization not found' });
      }

      try {
        if (organization.isFrozen) {
          return fail(400, { message: 'Cannot assign admin to a frozen organization' });
        }

        if (!organization.isActive) {
          return fail(400, { message: 'Cannot assign admin to an inactive organization' });
        }
      } catch (error) {
        console.error('Error checking organization status:', error);
      }

      await db
        .update(table.user)
        .set({
          organization: organizationId
        })
        .where(eq(table.user.id, adminId));

      return {
        success: true,
        message: `Admin assigned to organization "${organization.name}" successfully`
      };
    } catch (error) {
      console.error('Admin organization update error:', error);
      return fail(500, { message: 'An error occurred while updating the admin organization' });
    }
  },
};