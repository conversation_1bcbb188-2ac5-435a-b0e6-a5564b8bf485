import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { eq, and } from 'drizzle-orm';

export const GET: RequestHandler = async ({ params, locals }) => {
  try {
    const { user } = locals;
    const { projectId } = params;

    if (!user) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const [project] = await db
      .select()
      .from(table.project)
      .where(
        and(
          eq(table.project.id, projectId),
          eq(table.project.createdBy, user.id)
        )
      );

    if (!project) {
      return json({ error: 'Project not found' }, { status: 404 });
    }

    return json(project);
  } catch (error) {
    console.error('Error fetching project:', error);
    return json({ error: 'Failed to fetch project' }, { status: 500 });
  }
};
