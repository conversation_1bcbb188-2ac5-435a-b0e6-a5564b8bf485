import nodemailer from 'nodemailer';
import { env } from '$env/dynamic/private';
import type { User } from '../db/schema';

const EMAIL_CONFIG = {
  PASSWORD_RESET_EXPIRY_MINUTES: 15,
  FROM_ADDRESS: env.EMAIL_FROM || '<EMAIL>',
  FROM_NAME: env.EMAIL_FROM_NAME || 'PDFBankas',
};

const transporter = nodemailer.createTransport({
  host: env.EMAIL_HOST || 'smtp.protonmail.ch',
  port: parseInt(env.EMAIL_PORT || '465'),
  secure: true,
  auth: {
    user: env.EMAIL_USER,
    pass: env.EMAIL_PASSWORD,
  },
  tls: {
    rejectUnauthorized: false
  }
});

export async function sendPasswordResetEmail(
  user: User,
  resetToken: string,
  resetUrl: string
): Promise<any> {
  const resetLink = `${resetUrl}?token=${resetToken}`;

  const mailOptions = {
    from: `"${EMAIL_CONFIG.FROM_NAME}" <${EMAIL_CONFIG.FROM_ADDRESS}>`,
    to: user.email,
    subject: 'Reset Your Password',
    text: `Hello ${user.name || user.username},\n\nYou recently requested to reset your password for your PDFBankas account.\nUse the link below to reset it. This link is only valid for ${EMAIL_CONFIG.PASSWORD_RESET_EXPIRY_MINUTES} minutes.\n\n${resetLink}\n\nIf you did not request a password reset, please ignore this email or contact support if you have questions.\n\nThanks,\nThe PDFBankas Team`,
    html: `<!DOCTYPE html><html><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Reset Your Password</title><style>body{font-family:Arial,sans-serif;line-height:1.6;color:#333;max-width:600px;margin:0 auto;padding:20px}.container{border:1px solid #e1e1e1;border-radius:5px;padding:20px;background-color:#f9f9f9}.button{display:inline-block;background-color:rgb(160,187,229);color:white;text-decoration:none;padding:10px 20px;border-radius:5px;margin:20px 0}.footer{margin-top:30px;font-size:12px;color:#666;text-align:center}</style></head><body><div class="container"><h2>Reset Your Password</h2><p>Hello ${user.name || user.username},</p><p>You recently requested to reset your password for your PDFBankas account. Use the button below to reset it. This link is only valid for ${EMAIL_CONFIG.PASSWORD_RESET_EXPIRY_MINUTES} minutes.</p><a href="${resetLink}" class="button">Reset Your Password</a><p>If the button doesn't work, copy and paste this link into your browser:</p><p>${resetLink}</p><p>If you did not request a password reset, please ignore this email or contact support if you have questions.</p><p>Thanks,<br>Martynas Butkus</p></div><div class="footer"><p>This is an automated message, please do not reply to this email.</p></div></body></html>`,
  };

  try {
    return await transporter.sendMail(mailOptions);
  } catch (error) {
    console.error('Error sending password reset email:', error);
    throw error;
  }
}

export function getPasswordResetExpiry(): Date {
  const expiryMinutes = parseInt(env.PASSWORD_RESET_EXPIRY_MINUTES || '') ||
    EMAIL_CONFIG.PASSWORD_RESET_EXPIRY_MINUTES;

  const expiryDate = new Date();
  expiryDate.setMinutes(expiryDate.getMinutes() + expiryMinutes);

  return expiryDate;
}
