<script lang="ts">
  const {
    type = 'button',
    variant = 'default',
    size = 'md',
    disabled = false,
    class: className = '',
    onClick = undefined,
    form = undefined,
    children
  } = $props<{
    type?: 'button' | 'submit' | 'reset',
    variant?: 'default' | 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark',
    size?: 'sm' | 'md' | 'lg',
    disabled?: boolean,
    class?: string,
    onClick?: () => void,
    form?: string,
    children: any
  }>();

  const variantClasses = {
    default: 'border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-gray-500',
    primary: 'border-transparent text-white bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 focus:ring-blue-500',
    secondary: 'border-transparent text-white bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 focus:ring-gray-500',
    success: 'border-transparent text-white bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 focus:ring-green-500',
    danger: 'border-transparent text-white bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-600 focus:ring-red-500',
    warning: 'border-transparent text-gray-900 dark:text-gray-800 bg-yellow-400 dark:bg-yellow-500 hover:bg-yellow-500 dark:hover:bg-yellow-400 focus:ring-yellow-500',
    info: 'border-transparent text-white bg-indigo-600 dark:bg-indigo-700 hover:bg-indigo-700 dark:hover:bg-indigo-600 focus:ring-indigo-500',
    light: 'border border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 focus:ring-gray-200',
    dark: 'border-transparent text-white bg-gray-800 dark:bg-gray-900 hover:bg-gray-900 dark:hover:bg-black focus:ring-gray-700'
  };

  const sizeClasses = {
    sm: 'px-2.5 py-1.5 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-2 text-base'
  };
  const buttonClass = $derived(`
    inline-flex items-center justify-center
    font-medium rounded-md border
    focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800
    transition-colors duration-200
    shadow-sm
    ${variantClasses[variant as keyof typeof variantClasses]}
    ${sizeClasses[size as keyof typeof sizeClasses]}
    ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
    ${className}
  `);
</script>

<button
  {type}
  class={buttonClass}
  {disabled}
  onclick={onClick}
  {form}
>
  {@render children()}
</button>
