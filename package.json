{"name": "pdf-bak", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "dev:fast": "./dev.sh", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "db:start": "podman compose up", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:seed": "node seed-db.js"}, "devDependencies": {"@sveltejs/adapter-auto": "^5.0.0", "@sveltejs/kit": "^2.21.1", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.14.0", "@types/nodemailer": "^6.4.17", "autoprefixer": "^10.4.17", "drizzle-kit": "^0.30.3", "postcss": "^8.4.35", "postcss-load-config": "^5.0.2", "svelte": "^5.32.0", "svelte-check": "^4.0.0", "tailwindcss": "^3.4.1", "typescript": "^5.0.0", "vite": "^6.2.5"}, "dependencies": {"@node-rs/argon2": "^2.0.2", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "@rollup/rollup-win32-x64-msvc": "^4.39.0", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "argon2": "^0.41.1", "axios": "^1.9.0", "canvas": "^3.1.0", "chart.js": "^4.4.9", "drizzle-orm": "^0.40.0", "jszip": "^3.10.1", "lru-cache": "^11.1.0", "node-7z": "^3.0.0", "node-unrar-js": "^2.0.2", "nodemailer": "^7.0.2", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pdf-poppler": "^0.2.1", "pdf2pic": "^3.1.4", "pdfjs-dist": "^5.2.133", "postgres": "^3.4.5", "sharp": "^0.34.1"}}