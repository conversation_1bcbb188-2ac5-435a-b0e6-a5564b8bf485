<script lang="ts">
  const {
    id = '',
    name = '',
    type = 'text',
    label = '',
    value = '',
    placeholder = '',
    required = false,
    disabled = false,
    minlength = undefined,
    maxlength = undefined,
    min = undefined,
    max = undefined,
    pattern = undefined,
    helpText = '',
    class: className = '',
    onChange = undefined,
    onFocus = undefined,
    onBlur = undefined,
  } = $props<{
    id: string,
    name: string,
    type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'date' | 'time' | 'datetime-local',
    label?: string,
    value?: string | number,
    placeholder?: string,
    required?: boolean,
    disabled?: boolean,
    minlength?: number,
    maxlength?: number,
    min?: number,
    max?: number,
    pattern?: string,
    helpText?: string,
    class?: string,
    onChange?: (e: Event) => void,
    onFocus?: (e: FocusEvent) => void,
    onBlur?: (e: FocusEvent) => void,
  }>();

  const inputClass = $derived(`
    appearance-none relative block w-full px-4 py-3 
    border border-gray-300 dark:border-gray-600 
    placeholder-gray-500 dark:placeholder-gray-400 
    text-gray-900 dark:text-white dark:bg-gray-700 
    rounded-md focus:outline-none focus:ring-blue-500 
    focus:border-blue-500 focus:z-10 text-base
    ${disabled ? 'opacity-60 cursor-not-allowed' : ''}
    ${className}
  `);
</script>

<div>
  {#if label}
    <label for={id} class="block text-base font-medium text-gray-700 dark:text-gray-300 mb-2">
      {label}
      {#if required}<span class="text-red-500">*</span>{/if}
    </label>
  {/if}
  
  <input
    {id}
    {name}
    {type}
    {placeholder}
    {required}
    {disabled}
    {minlength}
    {maxlength}
    {min}
    {max}
    {pattern}
    value={value}
    class={inputClass}
    oninput={onChange}
    onfocus={onFocus}
    onblur={onBlur}
  />
  
  {#if helpText}
    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
      {helpText}
    </p>
  {/if}
</div>
