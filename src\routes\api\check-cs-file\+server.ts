import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { eq, count } from 'drizzle-orm';

export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    const { user } = locals;

    if (!user) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const submissionId = url.searchParams.get('submissionId');

    if (!submissionId) {
      return json({ error: 'Missing submission ID' }, { status: 400 });
    }

    // get the submission from the database
    const [submission] = await db
      .select()
      .from(table.submission)
      .where(eq(table.submission.id, submissionId));

    if (!submission) {
      return json({ error: 'Submission not found' }, { status: 404 });
    }

    // is authorized
    if (user.role !== 'developer' && user.role !== 'admin' && submission.studentId !== user.id) {
      return json({ error: 'Unauthorized' }, { status: 403 });
    }

    const [result] = await db
      .select({
        fileCount: count(table.csFile.id)
      })
      .from(table.csFile)
      .where(eq(table.csFile.submissionId, submissionId));

    const csFilesCount = result?.fileCount || 0;
    const hasCSFile = csFilesCount > 0;

    console.log('Checking CS file availability:', {
      submissionId,
      hasCSFile,
      csFilesCount
    });

    return json({
      hasCSFile,
      csFilesCount
    });
  } catch (error) {
    console.error('Error checking CS file:', error);
    return json({ error: 'Failed to check CS file' }, { status: 500 });
  }
};
