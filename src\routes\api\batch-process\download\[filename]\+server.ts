import { error } from '@sveltejs/kit';
import fs from 'fs';
import path from 'path';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ params, locals }) => {
  try {
    const { user } = locals;

    if (!user || user.role !== 'lecturer') {
      throw error(403, 'Unauthorized');
    }

    const { filename } = params;
    if (!filename || filename.includes('..') || !filename.match(/^batch_cs_files_\d{4}-\d{2}-\d{2}\.zip$/)) {
      throw error(400, 'Invalid filename');
    }

    const filePath = path.join(process.cwd(), 'temp', filename);

    if (!fs.existsSync(filePath)) {
      console.error(`File not found: ${filePath}`);
      throw error(404, 'File not found');
    }

    try {
      const stats = fs.statSync(filePath);
      if (stats.size === 0) {
        console.error(`File is empty: ${filePath}`);
        throw error(500, 'File is empty');
      }
    } catch (statError) {
      console.error(`Error checking file stats: ${filePath}`, statError);
      throw error(500, 'Error accessing file');
    }

    let fileBuffer;
    try {
      fileBuffer = fs.readFileSync(filePath);
      console.log(`Successfully read file: ${filePath} (${fileBuffer.length} bytes)`);
    } catch (readError) {
      console.error(`Error reading file: ${filePath}`, readError);
      throw error(500, 'Error reading file');
    }
    const headers = new Headers();
    headers.set('Content-Type', 'application/zip');
    headers.set('Content-Disposition', `attachment; filename="CS_Files_${new Date().toISOString().split('T')[0]}.zip"`);

    headers.set('Cache-Control', 'no-store, max-age=0');
    setTimeout(() => {
      try {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          console.log(`Deleted temporary file: ${filePath}`);
        }
      } catch (unlinkError) {
        console.error(`Error deleting temporary file: ${filePath}`, unlinkError);
      }
    }, 5000);

    return new Response(fileBuffer, { status: 200, headers });
  } catch (err) {
    console.error('Error in batch-process download handler:', err);
    if (err instanceof Error) {
      throw error(500, err.message);
    }
    throw error(500, 'Unknown error occurred');
  }
};
