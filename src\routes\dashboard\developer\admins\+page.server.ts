import { redirect } from '@sveltejs/kit';
import { eq, inArray, sql, and, or } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;

  if (!user || user.role !== 'developer') {
    return redirect(303, '/auth/login');
  }

  const admins = await db
    .select({
      id: table.user.id,
      username: table.user.username,
      email: table.user.email,
      isActive: table.user.isActive,
      createdAt: table.user.createdAt,
      organization: table.user.organization
    })
    .from(table.user)
    .where(eq(table.user.role, 'admin'))
    .orderBy(table.user.username);

  const organizationMap = new Map();

  try {
    // Get all organizations
    const organizationIds = admins
      .filter(admin => admin.organization !== null)
      .map(admin => admin.organization as string);

    if (organizationIds.length > 0) {
      const organizations = await db
        .select({
          id: table.organization.id,
          name: table.organization.name
        })
        .from(table.organization)
        .where(inArray(table.organization.id as any, organizationIds as any));

      // Get user counts for organization
      const organizationUserCounts = await db
        .select({
          organization: table.user.organization,
          count: sql<number>`count(${table.user.id})`
        })
        .from(table.user)
        .where(
          and(
            inArray(table.user.organization as any, organizationIds as any),
            or(
              eq(table.user.role, 'student'),
              eq(table.user.role, 'lecturer')
            )
          )
        )
        .groupBy(table.user.organization);

      const userCountMap = new Map();
      organizationUserCounts.forEach(item => {
        userCountMap.set(item.organization, item.count);
      });

      organizations.forEach(org => {
        organizationMap.set(org.id, {
          ...org,
          userCount: userCountMap.get(org.id) || 0
        });
      });
    }
  } catch (error) {
    console.error('Error fetching organizations:', error);
  }

  const adminCounts = {
    total: admins.length,
    active: admins.filter(admin => admin.isActive).length,
    inactive: admins.filter(admin => !admin.isActive).length
  };

  return {
    user,
    admins,
    adminCounts,
    organizationMap: Object.fromEntries(organizationMap)
  };
};