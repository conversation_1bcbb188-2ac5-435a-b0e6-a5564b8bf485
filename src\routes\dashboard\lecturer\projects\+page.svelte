<script lang="ts">
  import {
    Card,
    Countdown,
    <PERSON><PERSON>,
    PageHeader,
    EmptyState
  } from '$lib/components/ui';
  import { locale, t, type Locale } from '$lib/stores/locale';

  let { data } = $props();
  let currentLocale = $state<Locale>($locale);

  $effect(() => {
    currentLocale = $locale;
  });

  function formatDate(dateString: string | Date): string {
    return new Date(dateString).toLocaleDateString();
  }

  function formatDateTime(dateString: string | Date | null): string {
    if (!dateString) return t('common.noDeadline', currentLocale);
    return new Date(dateString).toLocaleString();
  }

  function isDeadlinePassed(deadline: string | Date | null): boolean {
    if (!deadline) return false;
    return new Date() > new Date(deadline);
  }
</script>

<div>
  <div class="flex justify-between items-center mb-6">
    <PageHeader title={t('dashboard.lecturer.projects.title', currentLocale)} />
    <Button
      variant="primary"
      onClick={() => window.location.href = '/dashboard/lecturer/projects/new'}
    >
      <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
      </svg>
      {t('dashboard.lecturer.projects.createNewProject', currentLocale)}
    </Button>
  </div>

  {#if data.projects.length === 0}
    <Card>
      <EmptyState
        icon="folder"
        message={t('dashboard.lecturer.projects.noProjects', currentLocale)}
        description={t('dashboard.lecturer.projects.noProjectsDesc', currentLocale)}
      />
      <div class="mt-6 text-center">
        <Button
          variant="primary"
          onClick={() => window.location.href = '/dashboard/lecturer/projects/new'}
        >
          <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
          </svg>
          {t('dashboard.lecturer.projects.createNewProject', currentLocale)}
        </Button>
      </div>
    </Card>
  {:else}
    <div class="grid grid-cols-1 gap-6">
      {#each data.projects as project}
        <Card>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h2 class="text-xl font-semibold text-gray-900">{project.name}</h2>
              {#if project.description}
                <p class="mt-1 text-sm text-gray-600">{project.description}</p>
              {/if}
            </div>
            <div class="flex justify-end items-start">
              <Button
                variant="light"
                onClick={() => window.location.href = `/dashboard/lecturer/projects/${project.id}`}
              >
                {t('common.viewDetails', currentLocale)}
              </Button>
            </div>
          </div>

          <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <span class="text-sm text-gray-500">{t('dashboard.stats.submissions', currentLocale)}</span>
              <p class="text-sm font-medium text-gray-900">{project.submissionCount}</p>
            </div>
            <div>
              <span class="text-sm text-gray-500">{t('common.lastUpdated', currentLocale)}</span>
              <p class="text-sm font-medium text-gray-900">{formatDate(project.updatedAt)}</p>
            </div>
            <div>
              <span class="text-sm text-gray-500">{t('common.deadline', currentLocale)}</span>
              {#if project.deadline}
                <p class="text-sm font-medium text-gray-900">
                  {#if isDeadlinePassed(project.deadline)}
                    <span class="text-red-600">{t('common.deadlinePassed', currentLocale)} ({formatDateTime(project.deadline)})</span>
                  {:else}
                    <Countdown deadline={project.deadline} showSeconds={false} />
                  {/if}
                </p>
              {:else}
                <p class="text-sm font-medium text-gray-500">{t('common.noDeadline', currentLocale)}</p>
              {/if}
            </div>
          </div>

          <div class="mt-4 flex flex-wrap gap-2">
            {#if project.isHidden}
              <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clip-rule="evenodd"></path>
                  <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"></path>
                </svg>
                Hidden
              </div>
            {/if}
            {#if project.maxAttempts}
              <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                </svg>
                Max Attempts: {project.maxAttempts}
              </div>
            {/if}
          </div>
        </Card>
      {/each}
    </div>
  {/if}
</div>