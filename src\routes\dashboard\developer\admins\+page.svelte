<script lang="ts">
  import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    Button,
    DashboardSection
  } from '$lib/components/ui';

  type Admin = {
    id: string;
    username: string;
    email: string;
    isActive: boolean;
    createdAt: string | Date;
    organization: string | null;
  };

  type Organization = {
    id: string;
    name: string;
    userCount: number;
    isActive: boolean;
    isFrozen: boolean;
  };

  let { data } = $props();

  let selectedOrganization = $state('');
  let filteredAdmins = $derived(filterAdmins(data.admins, selectedOrganization));

  function formatDate(dateString: string | Date | null | undefined): string {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString();
  }

  // Filter admins by organization
  function filterAdmins(admins: Admin[], orgId: string): Admin[] {
    if (!orgId) return admins;
    return admins.filter(admin => admin.organization === orgId);
  }

  // Get all organizations for the filter dropdown
  let organizationsArray = $state<Organization[]>([]);

  $effect(() => {
    if (!data.organizationMap) return;
    organizationsArray = Object.entries(data.organizationMap).map(([id, org]: [string, any]) => ({
      id,
      name: org.name,
      userCount: org.userCount || 0,
      isActive: org.isActive,
      isFrozen: org.isFrozen
    }));
  });

  // Check if organizationMap exists and convert it to a proper Map if needed
  $effect(() => {
    if (data.organizationMap && typeof data.organizationMap === 'object') {
      // Convert back to Map if it was serialized as an object
      if (!(data.organizationMap instanceof Map)) {
        const tempMap = new Map();
        Object.entries(data.organizationMap).forEach(([key, value]) => {
          tempMap.set(key, value);
        });
        data.organizationMap = tempMap;
      }
    }
  });
</script>

<div>
  <div class="mb-6 flex items-center justify-between">
    <PageHeader title="Admin Management" />
    <Button
      variant="primary"
      onClick={() => window.location.href = '/dashboard/developer/admins/new'}
    >
      Create New Admin
    </Button>
  </div>

  <DashboardSection cols="3" gap="6" marginBottom={true}>
    <Card>
      <h2 class="text-lg font-medium mb-2">Total Admins</h2>
      <p class="text-3xl font-bold">{data.adminCounts.total}</p>
    </Card>

    <Card>
      <h2 class="text-lg font-medium mb-2">Active Admins</h2>
      <p class="text-3xl font-bold text-green-600">{data.adminCounts.active}</p>
    </Card>

    <Card>
      <h2 class="text-lg font-medium mb-2">Inactive Admins</h2>
      <p class="text-3xl font-bold text-red-600">{data.adminCounts.inactive}</p>
    </Card>
  </DashboardSection>

  <Card title="Admin Accounts">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
      <div class="w-full md:w-64 self-end">
        <select
          id="organizationFilter"
          bind:value={selectedOrganization}
          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
        >
          <option value="">All Organizations</option>
          {#each organizationsArray as org}
            <option
              value={org.id}
              class={org.isFrozen ? 'text-red-500' : org.isActive ? 'text-green-500' : 'text-gray-500'}
            >
              {org.name} ({org.userCount} users) {org.isFrozen ? '(Frozen)' : ''}
            </option>
          {/each}
        </select>
      </div>
    </div>

    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Admin
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Organization
            </th>

            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Created
            </th>
            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          {#if filteredAdmins.length === 0}
            <tr>
              <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                {data.admins.length === 0 ? 'No admin accounts found' : 'No admins match the selected filter'}
              </td>
            </tr>
          {:else}
            {#each filteredAdmins as admin}
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{admin.username}</div>
                  <div class="text-sm text-gray-500">{admin.email}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    admin.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {admin.isActive ? 'active' : 'inactive'}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  {#if admin.organization && data.organizationMap && data.organizationMap[admin.organization]}
                    <div class="text-sm font-medium text-gray-900">{data.organizationMap[admin.organization].name}</div>
                    <div class="text-xs text-gray-500">
                      {data.organizationMap[admin.organization].userCount || 0} users
                    </div>
                  {:else}
                    <span class="text-xs text-gray-400">None</span>
                  {/if}
                </td>

                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {formatDate(admin.createdAt)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <a href={`/dashboard/developer/admins/${admin.id}`} class="text-blue-600 hover:text-blue-900">Edit</a>
                </td>
              </tr>
            {/each}
          {/if}
        </tbody>
      </table>
    </div>
  </Card>
</div>