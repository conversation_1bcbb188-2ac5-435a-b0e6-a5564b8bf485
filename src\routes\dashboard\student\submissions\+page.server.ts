import { redirect, fail } from '@sveltejs/kit';
import { eq, and, desc, inArray } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { saveFile, fileExists, getAbsoluteFilePath } from '$lib/server/storage/fileStorage';
import fs from 'fs';
import crypto from 'crypto';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;

  if (!user) {
    return redirect(303, '/auth/login');
  }

  if (user.role !== 'student') {
    const redirectMap = {
      lecturer: '/dashboard/lecturer',
      admin: '/dashboard/admin',
      developer: '/dashboard/developer'
    };

    return redirect(303, redirectMap[user.role as keyof typeof redirectMap] || '/');
  }

  const submissions = await db
    .select({
      id: table.submission.id,
      originalFilename: table.submission.originalFilename,
      submittedAt: table.submission.submittedAt,
      fileSize: table.submission.fileSize,
      filePath: table.submission.filePath,
      project: {
        id: table.project.id,
        name: table.project.name
      }
    })
    .from(table.submission)
    .innerJoin(table.project, eq(table.submission.projectId, table.project.id))
    .where(eq(table.submission.studentId, user.id))
    .orderBy(desc(table.submission.submittedAt));

  return {
    user,
    submissions
  };
};

export const actions: Actions = {
  deleteSubmission: async (event) => {
    const { user } = event.locals;

    if (!user || user.role !== 'student') {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const formData = await event.request.formData();
    const submissionId = formData.get('submissionId')?.toString();

    if (!submissionId) {
      return fail(400, { success: false, message: 'Submission ID is required' });
    }

    try {
      const [submission] = await db
        .select()
        .from(table.submission)
        .where(
          and(
            eq(table.submission.id, submissionId),
            eq(table.submission.studentId, user.id)
          )
        );

      if (!submission) {
        return fail(404, { success: false, message: 'Submission not found or does not belong to you' });
      }

      await db
        .delete(table.csFile)
        .where(eq(table.csFile.submissionId, submissionId));

      await db
        .delete(table.complexityAnalysis)
        .where(eq(table.complexityAnalysis.submissionId, submissionId));
      await db
        .delete(table.submissionAttempt)
        .where(eq(table.submissionAttempt.submissionId, submissionId));

      // Delete the file
      if (submission.filePath && fileExists(submission.filePath)) {
        try {
          const absolutePath = getAbsoluteFilePath(submission.filePath);
          fs.unlinkSync(absolutePath);
        } catch (fileError) {
          console.error('Error deleting file:', fileError);
        }
      }

      // delete submission record
      await db
        .delete(table.submission)
        .where(eq(table.submission.id, submissionId));

      return { success: true, message: 'Submission deleted successfully' };
    } catch (error) {
      console.error('Error deleting submission:', error);
      return fail(500, { success: false, message: 'An error occurred while deleting the submission' });
    }
  },

  submitFile: async (event) => {
    const { user } = event.locals;

    if (!user || user.role !== 'student') {
      return fail(403, { success: false, message: 'Unauthorized' });
    }

    const formData = await event.request.formData();
    const projectId = formData.get('projectId')?.toString();
    const file = formData.get('file') as File;

    if (!projectId) {
      return fail(400, { success: false, message: 'Project ID is required' });
    }

    if (!file || file.size === 0) {
      return fail(400, { success: false, message: 'Please select a file to upload' });
    }

    if (file.type !== 'application/pdf') {
      return fail(400, { success: false, message: 'Only PDF files are allowed' });
    }

    const [project] = await db
      .select({
        id: table.project.id,
        name: table.project.name
      })
      .from(table.project)
      .where(eq(table.project.id, projectId));

    if (!project) {
      return fail(404, { success: false, message: 'Project not found' });
    }

    const [projectStudent] = await db
      .select()
      .from(table.projectStudent)
      .where(
        and(
          eq(table.projectStudent.projectId, projectId),
          eq(table.projectStudent.studentId, user.id)
        )
      );

    if (!projectStudent) {
      const studentGroups = await db
        .select({
          groupId: table.groupMember.groupId
        })
        .from(table.groupMember)
        .where(eq(table.groupMember.studentId, user.id));

      const studentGroupIds = studentGroups.map(g => g.groupId);

      if (studentGroupIds.length === 0) {
        return fail(403, { success: false, message: 'You are not assigned to this project' });
      }

      const [projectGroup] = await db
        .select()
        .from(table.projectGroup)
        .where(
          and(
            eq(table.projectGroup.projectId, projectId),
            inArray(table.projectGroup.groupId, studentGroupIds)
          )
        );

      if (!projectGroup) {
        return fail(403, { success: false, message: 'You are not assigned to this project' });
      }

      // Check if this specific student is assigned to project
      const [assignedStudent] = await db
        .select({
          studentId: table.projectStudent.studentId
        })
        .from(table.projectStudent)
        .where(
          and(
            eq(table.projectStudent.projectId, projectId),
            eq(table.projectStudent.studentId, user.id)
          )
        );

      if (!assignedStudent) {
        return fail(403, { success: false, message: 'You are not assigned to this project' });
      }
    }

    try {
      const filePath = await saveFile(user.id, file, file.name);

      await db.insert(table.submission).values({
        id: crypto.randomUUID(),
        projectId,
        studentId: user.id,
        filePath,
        fileSize: file.size,
        originalFilename: file.name
      });

      return {
        success: true,
        message: 'Your submission has been received successfully'
      };
    } catch (error) {
      console.error('Submission error:', error);
      return fail(500, { success: false, message: 'An error occurred during file upload. Please try again.' });
    }
  }
};