import { fail, redirect } from '@sveltejs/kit';
import { eq } from 'drizzle-orm';
import { hashPassword } from '$lib/server/auth/password';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;

  if (!user) {
    return redirect(303, '/auth/login');
  }

  if (user.role !== 'admin') {
    const redirectMap = {
      student: '/dashboard/student',
      lecturer: '/dashboard/lecturer',
      developer: '/dashboard/developer'
    };

    return redirect(303, redirectMap[user.role as keyof typeof redirectMap] || '/');
  }

  return { user };
};

export const actions: Actions = {
  createUser: async ({ request, locals }) => {
    const formData = await request.formData();
    const username = formData.get('username')?.toString();
    const email = formData.get('email')?.toString();
    const password = formData.get('password')?.toString();
    let role = formData.get('role')?.toString() as 'student' | 'lecturer' | 'admin' | 'developer';

    if (role === 'admin' || role === 'developer') {
      return fail(403, {
        success: false,
        message: 'You are not authorized to create admin or developer accounts'
      });
    }

    if (role !== 'student' && role !== 'lecturer') {
      role = 'student';
    }

    if (!username || !email || !password || !role) {
      return fail(400, {
        success: false,
        message: 'All fields are required'
      });
    }

    if (password.length < 8) {
      return fail(400, {
        success: false,
        message: 'Password must be at least 8 characters long'
      });
    }

    const existingUser = await db
      .select({ id: table.user.id })
      .from(table.user)
      .where(
        eq(table.user.username, username)
      );

    if (existingUser.length > 0) {
      return fail(400, {
        success: false,
        message: 'Username already exists'
      });
    }

    const existingEmail = await db
      .select({ id: table.user.id })
      .from(table.user)
      .where(
        eq(table.user.email, email)
      );

    if (existingEmail.length > 0) {
      return fail(400, {
        success: false,
        message: 'Email already exists'
      });
    }

    try {
      const userId = crypto.randomUUID();
      const passwordHash = await hashPassword(password);
      const isApproved = role === 'student';

      await db.insert(table.user).values({
        id: userId,
        username,
        email,
        passwordHash,
        role,
        isApproved,
        isActive: true,
        createdAt: new Date()
      });


      return {
        success: true,
        message: 'User created successfully'
      };
    } catch (error) {
      console.error('Error creating user:', error);
      return fail(500, {
        success: false,
        message: 'Error creating user. Please try again.'
      });
    }
  }
};