import { fail, redirect } from '@sveltejs/kit';
import { eq } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { validatePasswordResetToken, markTokenAsUsed } from '$lib/server/auth/passwordReset';
import { hashPassword, validatePasswordForReset } from '$lib/server/auth/password';
import { validateTextInput } from '$lib/server/security/inputValidation';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
	// Redirect to dashboard if logged in
	if (event.locals.user) {
		const { role } = event.locals.user;

		if (role === 'student') return redirect(302, '/dashboard/student');
		if (role === 'lecturer') {
			if (!event.locals.user.isApproved) {
				return redirect(302, '/pending-approval');
			}
			return redirect(302, '/dashboard/lecturer');
		}
		if (role === 'admin') return redirect(302, '/dashboard/admin');
		if (role === 'developer') return redirect(302, '/dashboard/developer');
	}

	// Check if token is valid
	const token = event.url.searchParams.get('token');

	if (!token) {
		console.log('No token provided in URL parameters');
		return {
			isValidToken: false,
			errorMessage: 'No reset token provided'
		};
	}

	try {
		console.log('Validating token:', token.substring(0, 5) + '...');
		const user = await validatePasswordResetToken(token);

		if (!user) {
			console.log('Token validation failed - no valid user found');
			return {
				isValidToken: false,
				errorMessage: 'Invalid or expired password reset link'
			};
		}

		return {
			isValidToken: true,
			userId: user.id // Include user ID for additional verification
		};
	} catch (error) {
		console.error('Error validating reset token:', error);
		return {
			isValidToken: false,
			errorMessage: 'An error occurred while validating your reset token'
		};
	}
};

export const actions: Actions = {
	resetPassword: async (event) => {
		const formData = await event.request.formData();
		const token = formData.get('token')?.toString();
		const password = formData.get('password')?.toString();
		const confirmPassword = formData.get('confirmPassword')?.toString();

		if (!token) {
			console.error('No token provided in form data');
			return fail(400, {
				error: true,
				message: 'Invalid or missing token'
			});
		}

		// Enhanced password validation
		const passwordTextValidation = validateTextInput(password, 1, 255);
		if (!passwordTextValidation.isValid) {
			return fail(400, {
				error: true,
				message: passwordTextValidation.error || 'Invalid password format'
			});
		}

		if (password !== confirmPassword) {
			return fail(400, {
				error: true,
				message: 'Passwords do not match'
			});
		}

		// Validate token and get user
		const user = await validatePasswordResetToken(token);
		if (!user) {
			return fail(400, {
				error: true,
				message: 'Invalid or expired reset token'
			});
		}

		// Comprehensive password validation with HIBP check
		const passwordValidation = await validatePasswordForReset(password, user.id);
		if (!passwordValidation.isValid) {
			return fail(400, {
				error: true,
				message: passwordValidation.message,
				passwordDetails: passwordValidation.details
			});
		}

		try {
			console.log('Processing password reset for user:', user.id);
			const passwordHash = await hashPassword(password);

			try {
				await db
					.update(table.user)
					.set({ passwordHash })
					.where(eq(table.user.id, user.id));

				console.log('Password updated successfully for user:', user.id);
			} catch (dbError) {
				console.error('Database error updating password:', dbError);
				return fail(500, {
					error: true,
					message: 'Failed to update password in database. Please try again.'
				});
			}
			const tokenMarked = await markTokenAsUsed(token);
			if (!tokenMarked) {
				console.warn('Failed to mark token as used, but password was updated');
			}

			return {
				success: true,
				message: 'Your password has been reset successfully. You can now log in with your new password.'
			};
		} catch (error) {
			console.error('Error resetting password:', error);
			return fail(500, {
				error: true,
				message: 'An error occurred while resetting your password. Please try again.'
			});
		}
	}
};
