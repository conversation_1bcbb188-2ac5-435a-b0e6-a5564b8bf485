import { redirect, fail } from '@sveltejs/kit';
import { eq, asc, or, and, inArray, isNull } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;

  if (!user) {
    return redirect(303, '/auth/login');
  }

  if (user.role !== 'admin') {
    const redirectMap = {
      student: '/dashboard/student',
      lecturer: '/dashboard/lecturer',
      developer: '/dashboard/developer'
    };

    return redirect(303, redirectMap[user.role as keyof typeof redirectMap] || '/');
  }

  let users = [];
  try {
    if (user.organization) {
      users = await db
        .select({
          id: table.user.id,
          username: table.user.username,
          name: table.user.name,
          email: table.user.email,
          role: table.user.role,
          isActive: table.user.isActive,
          isApproved: table.user.isApproved,
          organization: table.user.organization,
          createdAt: table.user.createdAt
        })
        .from(table.user)
        .where(
          and(
            eq(table.user.organization, user.organization),
            or(
              eq(table.user.role, 'student'),
              eq(table.user.role, 'lecturer'),
              eq(table.user.role, 'admin')
            )
          )
        )
        .orderBy(asc(table.user.username));
    } else {
      // tie, kur nera org
      users = await db
      .select({
        id: table.user.id,
        username: table.user.username,
        name: table.user.name,
        email: table.user.email,
        role: table.user.role,
        isActive: table.user.isActive,
        isApproved: table.user.isApproved,
        organization: table.user.organization,
        createdAt: table.user.createdAt
      })
      .from(table.user)
      .where(
        and(
          isNull(table.user.organization),
          or(
            eq(table.user.role, 'student'),
            eq(table.user.role, 'lecturer'),
            eq(table.user.role, 'admin')
          )
        )
      )
      .orderBy(asc(table.user.username));
    }
  } catch (error) {
    console.error('Error fetching users:', error);
    if (user.organization) {
      users = await db
        .select({
          id: table.user.id,
          username: table.user.username,
          email: table.user.email,
          role: table.user.role,
          isActive: table.user.isActive,
          isApproved: table.user.isApproved,
          organization: table.user.organization,
          createdAt: table.user.createdAt
        })
        .from(table.user)
        .where(
          and(
            eq(table.user.organization, user.organization),
            or(
              eq(table.user.role, 'student'),
              eq(table.user.role, 'lecturer'),
              eq(table.user.role, 'admin')
            )
          )
        )
        .orderBy(asc(table.user.username));
    } else {
      users = await db
        .select({
          id: table.user.id,
          username: table.user.username,
          email: table.user.email,
          role: table.user.role,
          isActive: table.user.isActive,
          isApproved: table.user.isApproved,
          organization: table.user.organization,
          createdAt: table.user.createdAt
        })
        .from(table.user)
        .where(
          or(
            eq(table.user.role, 'student'),
            eq(table.user.role, 'lecturer'),
            eq(table.user.role, 'admin')
          )
        )
        .orderBy(asc(table.user.username));
    }
  }

  let organizationMap = new Map();

  try {
    const organizationIds = [...new Set(
      users
        .filter(user => user.organization !== null)
        .map(user => user.organization as string)
    )];

    if (organizationIds.length > 0) {
      const organizations = await db
        .select({
          id: table.organization.id,
          name: table.organization.name
        })
        .from(table.organization)
        .where(inArray(table.organization.id, organizationIds));

      organizations.forEach(org => {
        organizationMap.set(org.id, org.name);
      });
    }
  } catch (error) {
    console.error('Error fetching organization names:', error);
  }

  return {
    user,
    users,
    organizationMap: Object.fromEntries(organizationMap)
  };
};

export const actions: Actions = {
  toggleUserStatus: async ({ request }) => {
    const formData = await request.formData();
    const userId = formData.get('userId')?.toString();
    const currentStatus = formData.get('currentStatus') === 'true';

    if (!userId) {
      return fail(400, { success: false, message: 'User ID is required' });
    }

    try {
      await db
        .update(table.user)
        .set({
          isActive: !currentStatus,
        })
        .where(eq(table.user.id, userId));

      return {
        success: true,
        message: `User ${currentStatus ? 'deactivated' : 'activated'} successfully`
      };
    } catch (error) {
      console.error('Error toggling user status:', error);
      return fail(500, {
        success: false,
        message: 'An error occurred while updating the user status'
      });
    }
  }
};