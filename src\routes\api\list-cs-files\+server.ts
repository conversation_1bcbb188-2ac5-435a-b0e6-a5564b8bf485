import { json } from '@sveltejs/kit';
import type { Request<PERSON><PERSON><PERSON> } from './$types';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { eq, and } from 'drizzle-orm';

export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    const { user } = locals;

    if (!user) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const submissionId = url.searchParams.get('submissionId');

    if (!submissionId) {
      return json({ error: 'Missing submission ID' }, { status: 400 });
    }

    const [submission] = await db
      .select()
      .from(table.submission)
      .where(eq(table.submission.id, submissionId));

    if (!submission) {
      return json({ error: 'Submission not found' }, { status: 404 });
    }

    let isAuthorized = false;

    // who can access their submissions
    if (user.role === 'student' && submission.studentId === user.id) {
      isAuthorized = true;
    }
    else if (user.role === 'lecturer') {
      const [project] = await db
        .select()
        .from(table.project)
        .where(
          and(
            eq(table.project.id, submission.projectId),
            eq(table.project.createdBy, user.id)
          )
        );

      if (project) {
        isAuthorized = true;
      }
    }

    if (!isAuthorized) {
      return json({ error: 'Unauthorized' }, { status: 403 });
    }

    const csFiles = await db
      .select({
        id: table.csFile.id,
        fileName: table.csFile.fileName,
        filePath: table.csFile.filePath,
        createdAt: table.csFile.createdAt
      })
      .from(table.csFile)
      .where(eq(table.csFile.submissionId, submissionId));

    let allFiles = [...csFiles];

    return json({
      files: allFiles,
      submissionId
    });
  } catch (error) {
    console.error('Error listing CS files:', error);
    return json({ error: 'Failed to list CS files' }, { status: 500 });
  }
};
