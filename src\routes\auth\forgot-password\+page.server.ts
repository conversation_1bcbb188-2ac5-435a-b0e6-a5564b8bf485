import { fail, redirect } from '@sveltejs/kit';
import { eq } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { createPasswordResetToken } from '$lib/server/auth/passwordReset';
import { sendPasswordResetEmail } from '$lib/server/email/email';
import { validateEmail } from '$lib/utils/validation';
import type { Actions, PageServerLoad } from './$types';
import { env } from '$env/dynamic/private';
import { requireUnauthenticated } from '$lib/utils/auth';

export const load: PageServerLoad = async (event) => {
	requireUnauthenticated(event.locals.user);
	return {};
};

export const actions: Actions = {
	forgotPassword: async (event) => {
		const formData = await event.request.formData();
		const email = formData.get('email');

		if (!validateEmail(email)) {
			return fail(400, {
				error: true,
				message: 'Please enter a valid email address'
			});
		}

		try {
			const users = await db
				.select()
				.from(table.user)
				.where(eq(table.user.email, email));

			const user = users[0];
			if (!user || !user.isActive) {
				return {
					success: true,
					message: 'If your email is registered, you will receive a password reset link shortly.'
				};
			}

			const token = await createPasswordResetToken(user.id);

			const appUrl = env.APP_URL || `${event.url.protocol}//${event.url.host}`;
			const resetUrl = `${appUrl}/auth/reset-password`;
			await sendPasswordResetEmail(user, token, resetUrl);

			return {
				success: true,
				message: 'If your email is registered, you will receive a password reset link shortly.'
			};
		} catch (error) {
			console.error('Error in forgot password flow:', error);
			return fail(500, {
				error: true,
				message: 'An error occurred. Please try again later.'
			});
		}
	}
};
