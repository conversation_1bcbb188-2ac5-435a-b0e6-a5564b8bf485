import { json } from '@sveltejs/kit';
import { and, eq } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async (event) => {
  const { user } = event.locals;
  const { projectId } = event.params;

  if (!user || user.role !== 'lecturer') {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 403,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    // Get all project groups
    const projectGroups = await db
      .select({
        id: table.projectGroup.id,
        groupId: table.projectGroup.groupId,
        group: {
          name: table.group.name,
          description: table.group.description
        }
      })
      .from(table.projectGroup)
      .innerJoin(
        table.group,
        eq(table.projectGroup.groupId, table.group.id)
      )
      .where(eq(table.projectGroup.projectId, projectId));

    return json(projectGroups);
  } catch (error) {
    console.error('Error fetching project groups:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
