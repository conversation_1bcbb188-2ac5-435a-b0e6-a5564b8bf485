/// <reference types="svelte" />
/// <reference types="vite/client" />

declare global {
	namespace App {
		interface Locals {
			user: import('$lib/server/auth').SessionValidationResult['user'];
			session: import('$lib/server/auth').SessionValidationResult['session']
		}
	}
}

declare module '$env/dynamic/private' {
	export const env: {
		[key: string]: string | undefined;
	};
}

declare module '$env/dynamic/public' {
	export const env: {
		[key: string]: string | undefined;
	};
}

export {};
