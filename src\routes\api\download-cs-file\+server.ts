import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { eq, and } from 'drizzle-orm';
import { textFileDownloadResponse, unauthorizedResponse, forbiddenResponse, notFoundResponse, errorResponse } from '$lib/server/api/responseUtils';

export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    const { user } = locals;
    if (!user) return unauthorizedResponse();

    const submissionId = url.searchParams.get('submissionId');
    if (!submissionId) return errorResponse('Missing submission ID');

    const fileId = url.searchParams.get('fileId');
    if (!fileId) return notFoundResponse('No CS file ID provided');

    const [submission] = await db.select().from(table.submission)
      .where(eq(table.submission.id, submissionId));
    if (!submission) return notFoundResponse('Submission not found');
    let isAuthorized = false;
    if (user.role === 'student' && submission.studentId === user.id) {
      isAuthorized = true;
    } else if (user.role === 'lecturer') {
      const [project] = await db.select().from(table.project)
        .where(and(
          eq(table.project.id, submission.projectId),
          eq(table.project.createdBy, user.id)
        ));
      if (project) isAuthorized = true;
    }

    if (!isAuthorized) return forbiddenResponse('You do not have permission to access this file');

    const [csFile] = await db.select().from(table.csFile)
      .where(eq(table.csFile.id, fileId));
    if (!csFile) return notFoundResponse('CS file not found');
    if (csFile.submissionId !== submissionId)
      return forbiddenResponse('CS file does not belong to this submission');
    return textFileDownloadResponse(csFile.fileContent, `${csFile.fileName}.txt`);
  } catch (error) {
    console.error('Error downloading CS file:', error);
    return errorResponse('Failed to download CS file', 500);
  }
};
