<script lang="ts">
  const {
    label = '',
    value = 0,
    class: className = ''
  } = $props<{
    label: string,
    value: number,
    class?: string
  }>();

  const cardClass = $derived(`
    bg-gray-50 dark:bg-gray-700
    p-4 rounded-lg
    ${className}
  `);
  const displayValue = $derived(
    typeof value === 'number' && !Number.isInteger(value)
      ? value.toFixed(2)
      : value.toString()
  );
</script>

<div class={cardClass}>
  <div class="flex flex-col">
    <span class="text-sm text-gray-500 dark:text-gray-400">{label}</span>
    <span class="text-2xl font-bold text-gray-900 dark:text-white">{displayValue}</span>
  </div>
</div>
