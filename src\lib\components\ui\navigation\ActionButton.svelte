<script lang="ts">
  // Passed values
  let { href = '', title = '', description = '', color = 'blue', children } = $props<{
    href?: string,
    title?: string,
    description?: string,
    color?: 'blue' | 'green' | 'purple' | 'red' | 'gray',
    children?: any
  }>();

  // Colors
  const colorMap = {
    blue: {
      bg: 'bg-blue-50 dark:bg-gray-800',
      hover: 'hover:bg-blue-100 dark:hover:bg-gray-700',
      border: 'border-blue-100 dark:border-gray-700',
      text: 'text-blue-700 dark:text-blue-300'
    },
    green: {
      bg: 'bg-green-50 dark:bg-gray-800',
      hover: 'hover:bg-green-100 dark:hover:bg-gray-700',
      border: 'border-green-100 dark:border-gray-700',
      text: 'text-green-700 dark:text-green-300'
    },
    indigo: {
      bg: 'bg-indigo-50 dark:bg-gray-800',
      hover: 'hover:bg-indigo-100 dark:hover:bg-gray-700',
      border: 'border-indigo-100 dark:border-gray-700',
      text: 'text-indigo-700 dark:text-indigo-300'
    },
    red: {
      bg: 'bg-red-50 dark:bg-gray-800',
      hover: 'hover:bg-red-100 dark:hover:bg-gray-700',
      border: 'border-red-100 dark:border-gray-700',
      text: 'text-red-700 dark:text-red-300'
    }
  };

  const styles = colorMap[color as keyof typeof colorMap] || colorMap.blue;
</script>

<a
  href={href}
  class="block p-3 {styles.bg} {styles.hover} rounded-lg border {styles.border} transition-colors"
>
  <h3 class="font-semibold {styles.text}">{title}</h3>
  {#if description}
    <p class="text-sm text-gray-600 dark:text-gray-300">{description}</p>
  {/if}
  {#if children}
    {@render children()}
  {/if}
</a>
